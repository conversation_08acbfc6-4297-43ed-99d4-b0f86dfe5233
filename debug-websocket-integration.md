# WebSocket Integration Debug Guide

## Issue: Clients subscribed to same topic but not seeing each other's slot locks

### Current Status
- ✅ Both browsers subscribe to the same topic: `slots.dfdfe915-da70-4ec1-a7bb-b52d0bb74788.79236043-1fdd-4c7e-b200-74a57730f344.0e41472f-3b93-4821-8026-ee4b11d85068.2025-06-11`
- ❌ When one browser locks a slot, the other doesn't see the update

### Debugging Steps

#### 1. Check Backend WebSocket Stats
```bash
# Check if backend is running
curl http://localhost:8080/api/websocket/health

# Get WebSocket connection stats
curl http://localhost:8080/api/websocket/stats | jq .

# Expected response should show:
# - activeConnections: 2 (for both browsers)
# - topicStats.topicSubscriptions should include your topic with 2 subscribers
```

#### 2. Check Backend Logs During Slot Lock
When you lock a slot, you should see these logs in the backend:

```
🔒 Slot locked: shop=dfdfe915-da70-4ec1-a7bb-b52d0bb74788, service=79236043-1fdd-4c7e-b200-74a57730f344, employee=0e41472f-3b93-4821-8026-ee4b11d85068, dateTime=2025-06-11T10:00:00
Broadcasting to topic 'slots.dfdfe915-da70-4ec1-a7bb-b52d0bb74788.79236043-1fdd-4c7e-b200-74a57730f344.0e41472f-3b93-4821-8026-ee4b11d85068.2025-06-11' with message: {type=SLOT_UPDATE, action=LOCKED, ...}
Broadcasting to topic 'slots.dfdfe915-da70-4ec1-a7bb-b52d0bb74788.79236043-1fdd-4c7e-b200-74a57730f344.0e41472f-3b93-4821-8026-ee4b11d85068.2025-06-11' - Subscribers: 2
Topic 'slots.dfdfe915-da70-4ec1-a7bb-b52d0bb74788.79236043-1fdd-4c7e-b200-74a57730f344.0e41472f-3b93-4821-8026-ee4b11d85068.2025-06-11' broadcast complete: 2 success, 0 failures
```

#### 3. Most Likely Issues

**Issue A: Backend Not Broadcasting**
- The slot locking API isn't calling the WebSocket broadcast
- Check if `SlotLockingService.lockSlot()` calls `slotUpdateService.broadcastSlotLocked()`

**Issue B: Topic Mismatch**
- Frontend subscribes to one topic format
- Backend broadcasts to a different topic format
- Check topic generation in both frontend and backend

**Issue C: WebSocket Connection Issues**
- Clients think they're subscribed but backend doesn't have them registered
- Check WebSocket connection establishment

#### 4. Quick Test Commands

```bash
# Test slot locking API directly
curl -X POST http://localhost:8080/api/appointments/lock-slot \
  -H "Content-Type: application/json" \
  -d '{
    "shopId": "dfdfe915-da70-4ec1-a7bb-b52d0bb74788",
    "serviceId": "79236043-1fdd-4c7e-b200-74a57730f344", 
    "employeeId": "0e41472f-3b93-4821-8026-ee4b11d85068",
    "dateTime": "2025-06-11T10:00:00"
  }'

# Check WebSocket stats after lock
curl http://localhost:8080/api/websocket/stats | jq .topicStats
```

### Frontend Debugging

#### 1. Use the WebSocket Message Debugger
- Go to: `http://localhost:3000/test` → "WebSocket Message Debugger"
- Connect WebSocket
- Subscribe to topic
- Lock slot
- Watch for messages in real-time

#### 2. Check Browser Console
Look for these messages:
```
🎯 WebSocket subscribing to slot updates: slots.dfdfe915-da70-4ec1-a7bb-b52d0bb74788.79236043-1fdd-4c7e-b200-74a57730f344.0e41472f-3b93-4821-8026-ee4b11d85068.2025-06-11
✅ Subscription confirmed for topic: slots.dfdfe915-da70-4ec1-a7bb-b52d0bb74788.79236043-1fdd-4c7e-b200-74a57730f344.0e41472f-3b93-4821-8026-ee4b11d85068.2025-06-11
📡 Topic message: slots.dfdfe915-da70-4ec1-a7bb-b52d0bb74788.79236043-1fdd-4c7e-b200-74a57730f344.0e41472f-3b93-4821-8026-ee4b11d85068.2025-06-11
🎯 SLOT UPDATE: LOCKED - 2025-06-11T10:00:00
```

### Expected Working Flow

1. **Browser A**: Connects WebSocket → Subscribes to topic
2. **Browser B**: Connects WebSocket → Subscribes to same topic  
3. **Backend**: Shows 2 subscribers for the topic
4. **Browser A**: Locks slot → API call to `/api/appointments/lock-slot`
5. **Backend**: 
   - Creates Redis lock
   - Calls `slotUpdateService.broadcastSlotLocked()`
   - Broadcasts to WebSocket topic with 2 subscribers
6. **Browser B**: Receives WebSocket message → Updates UI to show slot as locked

### Troubleshooting Commands

```bash
# Check if Redis is working
redis-cli ping

# Check backend logs for WebSocket activity
tail -f logs/application.log | grep -E "(WebSocket|slot|topic|broadcast)"

# Test WebSocket connection manually
wscat -c ws://localhost:8080/websocket

# Check if slot locking service is working
curl -X POST http://localhost:8080/api/appointments/lock-slot \
  -H "Content-Type: application/json" \
  -d '{"shopId":"test","serviceId":"test","employeeId":"test","dateTime":"2025-06-11T10:00:00"}'
```

### Common Fixes

1. **Restart Backend**: Sometimes WebSocket connections get stuck
2. **Clear Redis**: `redis-cli flushall` to clear any stuck locks
3. **Check Logs**: Look for exceptions in slot locking or WebSocket broadcasting
4. **Verify Topic Format**: Ensure frontend and backend use same topic naming

### Testing Multi-User Scenario

1. Open two browser tabs to: `http://localhost:3000/test/enhanced-workflow`
2. In both tabs: Click "Connect WS"
3. Verify both show "Subscribed: ✅"
4. In Tab 1: Click "Lock Real Slot"
5. In Tab 2: Should immediately see a slot update message
6. In Tab 1: Click "Unlock Real Slot"  
7. In Tab 2: Should immediately see slot unlock message

If this doesn't work, the issue is in the backend WebSocket broadcasting chain.
