# BeautyHub Testing Documentation

This document provides comprehensive information about the testing strategy and implementation for the BeautyHub application.

## 🧪 Testing Overview

BeautyHub uses a multi-layered testing approach to ensure reliability and quality:

- **Unit Tests**: Test individual components and functions in isolation
- **Integration Tests**: Test interactions between different layers and components
- **End-to-End Tests**: Test complete user workflows and API communication
- **Frontend Tests**: Test React components, stores, and user interactions
- **Backend Tests**: Test entities, repositories, services, and controllers

## 📁 Test Structure

```
beautyhub/
├── src/test/groovy/com/ddimitko/beautyhub/
│   ├── entity/                 # Entity validation tests
│   ├── repository/             # Repository and database tests
│   ├── service/                # Service layer tests
│   ├── controller/             # Controller and API tests
│   ├── integration/            # Integration tests
│   └── BeautyhubApplicationTests.groovy
├── src/test/resources/
│   └── application-test.properties
├── bhfrontend/src/
│   ├── components/__tests__/   # Component tests
│   ├── store/__tests__/        # State management tests
│   └── lib/__tests__/          # Utility and API tests
├── run-tests.sh               # Test runner script
└── TESTING.md                 # This file
```

## 🎯 Test Categories

### Backend Tests

#### 1. Entity Tests
- **Location**: `src/test/groovy/com/ddimitko/beautyhub/entity/`
- **Purpose**: Validate entity constraints, relationships, and business logic
- **Example**: `UserEntityTest.groovy`

```groovy
@Test
@DisplayName("Should create valid user with all required fields")
void shouldCreateValidUser() {
    Set<ConstraintViolation<User>> violations = validator.validate(user)
    assertTrue(violations.isEmpty())
}
```

#### 2. Repository Tests
- **Location**: `src/test/groovy/com/ddimitko/beautyhub/repository/`
- **Purpose**: Test data access layer and custom queries
- **Example**: `UserRepositoryTest.groovy`

```groovy
@Test
@DisplayName("Should find user by email successfully")
void shouldFindUserByEmail() {
    Optional<User> foundUser = userRepository.findByEmail("<EMAIL>")
    assertTrue(foundUser.isPresent())
}
```

#### 3. Service Tests
- **Location**: `src/test/groovy/com/ddimitko/beautyhub/service/`
- **Purpose**: Test business logic and service interactions
- **Example**: `UserServiceTest.groovy`

```groovy
@Test
@DisplayName("Should create user successfully")
void shouldCreateUserSuccessfully() {
    User createdUser = userService.createUser(email, password, firstName, lastName, role)
    assertNotNull(createdUser)
}
```

#### 4. Controller Tests
- **Location**: `src/test/groovy/com/ddimitko/beautyhub/controller/`
- **Purpose**: Test REST API endpoints and request/response handling
- **Example**: `AuthControllerTest.groovy`

```groovy
@Test
@DisplayName("Should login successfully with valid credentials")
void shouldLoginSuccessfully() throws Exception {
    mockMvc.perform(post("/api/auth/login")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(loginRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath('$.token').exists())
}
```

#### 5. Integration Tests
- **Location**: `src/test/groovy/com/ddimitko/beautyhub/integration/`
- **Purpose**: Test complete workflows and system integration
- **Examples**: 
  - `AuthIntegrationTest.groovy` - Full authentication flow
  - `DatabaseIntegrationTest.groovy` - Database relationships and constraints

### Frontend Tests

#### 1. Component Tests
- **Location**: `bhfrontend/src/components/__tests__/`
- **Purpose**: Test React component rendering and user interactions
- **Example**: `LoginForm.test.js`

```javascript
test('renders login form with all required fields', () => {
    renderLoginForm();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
});
```

#### 2. Store Tests
- **Location**: `bhfrontend/src/store/__tests__/`
- **Purpose**: Test state management and business logic
- **Example**: `authStore.test.js`

```javascript
test('should handle login successfully', async () => {
    await act(async () => {
        await result.current.login(mockLoginData);
    });
    expect(result.current.isAuthenticated).toBe(true);
});
```

#### 3. API Tests
- **Location**: `bhfrontend/src/lib/__tests__/`
- **Purpose**: Test API client and HTTP interactions
- **Example**: `api.test.js`

```javascript
test('should add authorization header when token exists', () => {
    const config = { headers: {} };
    const result = interceptor(config);
    expect(result.headers.Authorization).toBe(`Bearer ${mockToken}`);
});
```

## 🚀 Running Tests

### Quick Start

```bash
# Run all tests
./run-tests.sh

# Run only backend tests
./run-tests.sh --backend-only

# Run only frontend tests
./run-tests.sh --frontend-only

# Run end-to-end tests
./run-tests.sh --e2e

# Run everything including e2e
./run-tests.sh --all
```

### Manual Test Execution

#### Backend Tests
```bash
# All backend tests
./gradlew test

# Specific test class
./gradlew test --tests UserServiceTest

# Integration tests only
./gradlew test --tests "*IntegrationTest"

# With coverage
./gradlew test jacocoTestReport
```

#### Frontend Tests
```bash
cd bhfrontend

# All frontend tests
npm test

# With coverage
npm test -- --coverage

# Watch mode
npm test -- --watch

# Specific test file
npm test -- LoginForm.test.js
```

## 📊 Test Coverage

### Backend Coverage
- **Target**: 80%+ line coverage
- **Report Location**: `build/reports/jacoco/test/html/index.html`
- **Command**: `./gradlew jacocoTestReport`

### Frontend Coverage
- **Target**: 80%+ line coverage
- **Report Location**: `bhfrontend/coverage/lcov-report/index.html`
- **Command**: `npm test -- --coverage`

## 🔧 Test Configuration

### Backend Test Configuration
- **Profile**: `test`
- **Database**: H2 in-memory database
- **Config File**: `src/test/resources/application-test.properties`

Key configurations:
```properties
spring.datasource.url=jdbc:h2:mem:testdb
spring.jpa.hibernate.ddl-auto=create-drop
spring.flyway.enabled=false
jwt.secret=testSecretKeyForJWTTokenGenerationInTestEnvironmentOnly
```

### Frontend Test Configuration
- **Framework**: Jest + React Testing Library
- **Config File**: `bhfrontend/package.json` (test scripts)
- **Setup File**: `bhfrontend/src/setupTests.js`

## 🧩 Test Utilities and Helpers

### Backend Test Utilities
- **@DataJpaTest**: For repository tests
- **@WebMvcTest**: For controller tests
- **@SpringBootTest**: For integration tests
- **MockMvc**: For HTTP request testing
- **TestEntityManager**: For database operations in tests

### Frontend Test Utilities
- **React Testing Library**: Component testing
- **Jest**: Test framework and mocking
- **userEvent**: User interaction simulation
- **MockedAxios**: API mocking

## 📋 Test Checklist

### Before Committing
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code coverage meets minimum threshold
- [ ] No test warnings or errors
- [ ] New features have corresponding tests

### Test Quality Guidelines
- [ ] Tests are isolated and independent
- [ ] Tests have descriptive names
- [ ] Tests cover both happy path and error cases
- [ ] Tests use appropriate assertions
- [ ] Tests are maintainable and readable

## 🐛 Debugging Tests

### Backend Test Debugging
```bash
# Run with debug logging
./gradlew test -Dlogging.level.com.ddimitko.beautyhub=DEBUG

# Run single test with debug
./gradlew test --tests UserServiceTest --debug-jvm
```

### Frontend Test Debugging
```bash
# Run with verbose output
npm test -- --verbose

# Debug specific test
npm test -- --testNamePattern="should login successfully"
```

## 📈 Continuous Integration

Tests are automatically run in CI/CD pipeline:
- **Trigger**: On every push and pull request
- **Backend**: Gradle test task
- **Frontend**: npm test with coverage
- **Reports**: Coverage reports uploaded to CI artifacts

## 🔄 Test Maintenance

### Regular Tasks
- Review and update test data
- Remove obsolete tests
- Update mocks when APIs change
- Maintain test documentation
- Monitor test execution time

### Best Practices
- Keep tests simple and focused
- Use meaningful test data
- Mock external dependencies
- Test edge cases and error conditions
- Regularly review test coverage reports

## 📚 Additional Resources

- [Spring Boot Testing Guide](https://spring.io/guides/gs/testing-web/)
- [React Testing Library Documentation](https://testing-library.com/docs/react-testing-library/intro/)
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Groovy Testing Guide](https://groovy-lang.org/testing.html)

---

For questions or issues with tests, please check the existing test files for examples or consult the team documentation.
