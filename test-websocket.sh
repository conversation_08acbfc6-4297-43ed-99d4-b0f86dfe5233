#!/bin/bash

# WebSocket Testing Script for BeautyHub
echo "🔌 Testing BeautyHub WebSocket Configuration"
echo "============================================="

BASE_URL="http://localhost:8080"
WS_URL="ws://localhost:8080"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to check if server is running
check_server() {
    print_status "Checking if backend server is running..."
    if curl -f -s "$BASE_URL/api/test/health" > /dev/null; then
        print_success "Backend server is running"
        return 0
    else
        print_error "Backend server is not running"
        print_status "Please start the backend server first: ./gradlew bootRun"
        return 1
    fi
}

# Function to test WebSocket health endpoint
test_websocket_health() {
    print_status "Testing WebSocket health endpoint..."
    
    response=$(curl -s "$BASE_URL/api/test/websocket/health")
    if [ $? -eq 0 ]; then
        print_success "WebSocket health endpoint is accessible"
        echo "Response: $response" | jq '.' 2>/dev/null || echo "Response: $response"
    else
        print_error "WebSocket health endpoint failed"
        return 1
    fi
}

# Function to test WebSocket stats endpoint
test_websocket_stats() {
    print_status "Testing WebSocket stats endpoint..."
    
    response=$(curl -s "$BASE_URL/api/test/websocket/stats")
    if [ $? -eq 0 ]; then
        print_success "WebSocket stats endpoint is accessible"
        echo "Response: $response" | jq '.' 2>/dev/null || echo "Response: $response"
    else
        print_error "WebSocket stats endpoint failed"
        return 1
    fi
}

# Function to test WebSocket broadcast endpoint
test_websocket_broadcast() {
    print_status "Testing WebSocket broadcast endpoint..."
    
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"message":"Test broadcast from script"}' \
        "$BASE_URL/api/test/websocket/broadcast")
    
    if [ $? -eq 0 ]; then
        print_success "WebSocket broadcast endpoint is working"
        echo "Response: $response" | jq '.' 2>/dev/null || echo "Response: $response"
    else
        print_error "WebSocket broadcast endpoint failed"
        return 1
    fi
}

# Function to run backend WebSocket tests
run_backend_tests() {
    print_status "Running backend WebSocket integration tests..."
    
    cd "$(dirname "$0")"
    
    # Run specific WebSocket tests
    ./gradlew test --tests "*WebSocketIntegrationTest*" --info
    
    if [ $? -eq 0 ]; then
        print_success "Backend WebSocket tests passed"
    else
        print_error "Backend WebSocket tests failed"
        return 1
    fi
}

# Function to test WebSocket connection with wscat (if available)
test_websocket_connection() {
    print_status "Testing WebSocket connection..."
    
    if command -v wscat &> /dev/null; then
        print_status "Using wscat to test WebSocket connection..."
        
        # Test SockJS endpoint
        timeout 10s wscat -c "$WS_URL/notifications/websocket" --execute "Hello WebSocket" 2>/dev/null
        if [ $? -eq 0 ]; then
            print_success "SockJS WebSocket connection test passed"
        else
            print_warning "SockJS WebSocket connection test failed or timed out"
        fi
        
        # Test native WebSocket endpoint
        timeout 10s wscat -c "$WS_URL/ws" --execute "Hello Native WebSocket" 2>/dev/null
        if [ $? -eq 0 ]; then
            print_success "Native WebSocket connection test passed"
        else
            print_warning "Native WebSocket connection test failed or timed out"
        fi
    else
        print_warning "wscat not available. Install with: npm install -g wscat"
        print_status "Skipping direct WebSocket connection tests"
    fi
}

# Function to check WebSocket configuration
check_websocket_config() {
    print_status "Checking WebSocket configuration..."
    
    # Check if WebSocket endpoints are configured
    if curl -f -s "$BASE_URL/ws-notifications/info" > /dev/null; then
        print_success "SockJS WebSocket endpoint is configured"
    else
        print_warning "SockJS WebSocket endpoint may not be properly configured"
    fi
    
    # Check CORS configuration
    response=$(curl -s -I -X OPTIONS \
        -H "Origin: http://localhost:3000" \
        -H "Access-Control-Request-Method: GET" \
        "$BASE_URL/ws-notifications/info")
    
    if echo "$response" | grep -q "Access-Control-Allow-Origin"; then
        print_success "CORS is configured for WebSocket endpoints"
    else
        print_warning "CORS may not be properly configured for WebSocket"
    fi
}

# Function to generate WebSocket test report
generate_test_report() {
    print_status "Generating WebSocket test report..."
    
    report_file="websocket-test-report.txt"
    
    {
        echo "BeautyHub WebSocket Test Report"
        echo "Generated: $(date)"
        echo "================================"
        echo ""
        
        echo "Backend Server Status:"
        curl -s "$BASE_URL/api/test/health" | jq '.' 2>/dev/null || echo "Failed to get health status"
        echo ""
        
        echo "WebSocket Health:"
        curl -s "$BASE_URL/api/test/websocket/health" | jq '.' 2>/dev/null || echo "Failed to get WebSocket health"
        echo ""
        
        echo "WebSocket Statistics:"
        curl -s "$BASE_URL/api/test/websocket/stats" | jq '.' 2>/dev/null || echo "Failed to get WebSocket stats"
        echo ""
        
        echo "Configuration Check:"
        echo "- STOMP endpoint: $BASE_URL/stomp"
        echo "- SockJS endpoint: $BASE_URL/notifications"
        echo "- Native WebSocket: $BASE_URL/ws"
        echo ""
        
        echo "Test Results:"
        echo "- Backend tests: Run './gradlew test --tests *WebSocketIntegrationTest*'"
        echo "- Frontend tests: Open http://localhost:3000/test/websocket"
        echo ""
        
    } > "$report_file"
    
    print_success "Test report generated: $report_file"
}

# Main test execution
main() {
    echo ""
    print_status "Starting WebSocket tests..."
    echo ""
    
    # Check if server is running
    if ! check_server; then
        exit 1
    fi
    
    echo ""
    
    # Run tests
    test_websocket_health
    echo ""
    
    test_websocket_stats
    echo ""
    
    test_websocket_broadcast
    echo ""
    
    check_websocket_config
    echo ""
    
    test_websocket_connection
    echo ""
    
    # Run backend tests if requested
    if [ "$1" = "--backend-tests" ] || [ "$1" = "-b" ]; then
        run_backend_tests
        echo ""
    fi
    
    # Generate report
    generate_test_report
    echo ""
    
    print_success "WebSocket testing completed!"
    echo ""
    print_status "Next steps:"
    echo "1. Run backend tests: ./test-websocket.sh --backend-tests"
    echo "2. Test frontend: Open http://localhost:3000/test/websocket"
    echo "3. Check logs: tail -f logs/application.log"
    echo "4. Monitor connections: curl $BASE_URL/api/test/websocket/stats"
    echo ""
}

# Handle command line arguments
case "$1" in
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --backend-tests, -b    Run backend WebSocket integration tests"
        echo "  --help, -h            Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0                    Run basic WebSocket tests"
        echo "  $0 --backend-tests    Run all tests including backend integration tests"
        ;;
    *)
        main "$@"
        ;;
esac
