# Fake Stripe Subscription Demo

This document demonstrates how the fake Stripe subscription system works for testing the BeautyHub shop creation wizard.

## Overview

When a shop owner creates a shop that accepts card payments, they must complete a subscription before the shop can process payments. Since we haven't implemented real Stripe integration yet, this fake system simulates the entire subscription flow.

## API Endpoints

### 1. Create Shop (with card payments)
```bash
POST /api/shops
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "Premium Beauty Salon",
  "description": "A premium beauty salon",
  "businessTypes": ["HAIRDRESSER", "BEAUTY_SALON"],
  "address": "123 Premium Street",
  "city": "New York",
  "state": "NY",
  "postalCode": "10001",
  "country": "USA",
  "phone": "******-PREMIUM",
  "email": "<EMAIL>",
  "acceptsCardPayments": true
}
```

**Response:**
```json
{
  "shopId": "uuid-here",
  "message": "Shop created successfully!",
  "name": "Premium Beauty Salon",
  "address": "123 Premium Street, New York, NY 10001, USA",
  "requiresStripeSetup": true,
  "requiresSubscription": true,
  "nextStep": "subscription_setup"
}
```

### 2. Create Fake Stripe Subscription
```bash
POST /api/subscriptions/shops/{shopId}
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "planName": "Premium Plan",
  "amount": 4999,
  "currency": "usd",
  "interval": "month",
  "billingEmail": "<EMAIL>",
  "billingName": "John Doe"
}
```

**Response:**
```json
{
  "shopId": "uuid-here",
  "subscriptionId": "sub_fake_1234567890abcdef",
  "stripeAccountId": "acct_fake_1234567890abcdef",
  "status": "active",
  "message": "Subscription created successfully (fake for testing)",
  "planName": "Premium Plan",
  "amount": 4999,
  "currency": "usd",
  "interval": "month",
  "nextBillingDate": "2025-07-08T16:30:33.000Z",
  "isActive": true
}
```

### 3. Validate Subscription
```bash
POST /api/subscriptions/validate/{shopId}
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "shopId": "uuid-here",
  "subscriptionValid": true,
  "message": "Subscription is valid"
}
```

### 4. Get Subscription Details
```bash
GET /api/subscriptions/shops/{shopId}
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "shopId": "uuid-here",
  "subscriptionId": "sub_fake_1234567890abcdef",
  "stripeAccountId": "acct_fake_1234567890abcdef",
  "status": "active",
  "planName": "Basic Plan",
  "amount": 2999,
  "currency": "usd",
  "interval": "month",
  "nextBillingDate": "2025-07-08T16:30:33.000Z"
}
```

### 5. Cancel Subscription
```bash
DELETE /api/subscriptions/shops/{shopId}
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "message": "Subscription cancelled successfully",
  "shopId": "uuid-here"
}
```

## Shop Creation Wizard Flow

1. **Owner Registration**: User registers as business owner
2. **Shop Creation**: Owner fills shop details and enables card payments
3. **Subscription Required**: System responds with `requiresSubscription: true`
4. **Subscription Setup**: Owner completes fake Stripe subscription
5. **Shop Activation**: Shop is now ready to accept payments

## What Gets Updated

When a subscription is created, the following shop fields are updated:

- `subscriptionId`: Set to fake subscription ID (e.g., "sub_fake_1234567890abcdef")
- `subscriptionActive`: Set to `true`
- `stripeAccountId`: Set to fake Stripe account ID (e.g., "acct_fake_1234567890abcdef")
- `stripeOnboardingCompleted`: Set to `true`

## Testing Features

### ✅ **Comprehensive Test Coverage**
- **StripeServiceTest**: 11 unit tests covering all subscription operations
- **SubscriptionControllerTest**: 9 controller tests for API endpoints
- **Integration Tests**: Full shop creation with subscription flow

### 🔒 **Security Features**
- JWT authentication required for all endpoints
- Owner role verification
- Shop ownership validation
- Prevents duplicate subscriptions

### 🚫 **Error Handling**
- Shop not found
- Shop doesn't accept card payments
- Already has active subscription
- Invalid subscription data
- Access denied for non-owners

## Fake Data Generation

The system generates realistic fake data:
- **Subscription IDs**: `sub_fake_` + 16 random characters
- **Stripe Account IDs**: `acct_fake_` + 16 random characters
- **Billing Dates**: 30 days from creation
- **Status**: Always "active" for successful subscriptions

## Next Steps

This fake system provides a complete foundation for:
1. **Frontend Development**: UI can be built and tested against these endpoints
2. **Integration Testing**: Full shop creation flow can be tested end-to-end
3. **Real Stripe Integration**: Easy to replace fake logic with real Stripe API calls

## Running Tests

```bash
# Run all Stripe service tests
./gradlew test --tests "*StripeService*"

# Run all subscription controller tests  
./gradlew test --tests "*SubscriptionController*"

# Run integration tests
./gradlew test --tests "*ShopCreationWithSubscription*"
```

All tests are passing and provide comprehensive coverage of the fake Stripe subscription functionality.
