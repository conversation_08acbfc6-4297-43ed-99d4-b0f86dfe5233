#!/bin/bash

# Test script for BeautyHub Appointment Creation with Card Payments
echo "💳 Testing BeautyHub Appointment Creation with Stripe Integration"
echo "================================================================="

BASE_URL="http://localhost:8080"

# Set PostgreSQL PATH
export PATH="/Library/PostgreSQL/16/bin:$PATH"

# First, let's create test data using SQL directly
echo "1. Creating test data..."

# Create test data via SQL
PGPASSWORD=admin psql -h localhost -p 5433 -U postgres -d beautyhub << 'EOF'
-- Clean up existing data
DELETE FROM schedule_slots;
DELETE FROM services;
DELETE FROM employees;
DELETE FROM shop_business_types;
DELETE FROM shops;
DELETE FROM users;

-- Insert test user (shop owner)
INSERT INTO users (id, email, first_name, last_name, password, role, email_verified, enabled, created_at, updated_at)
VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    '<PERSON>',
    '<PERSON>',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTUDqyWvekKvrgFF.E/Nxu.TGfvOSWO2',
    'OWNER',
    true,
    true,
    NOW(),
    NOW()
);

-- Get the owner ID
\set owner_id (SELECT id FROM users WHERE email = '<EMAIL>')

-- Insert test shop with Stripe connected account
INSERT INTO shops (
    id, 
    owner_id, 
    name, 
    description, 
    address, 
    city, 
    state, 
    postal_code, 
    country, 
    phone, 
    email,
    accepts_card_payments,
    stripe_account_id,
    stripe_onboarding_completed,
    subscription_active,
    subscription_id,
    active,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'Elite Beauty Salon',
    'Premium beauty services with expert stylists',
    '123 Beauty Street',
    'New York',
    'NY',
    '10001',
    'USA',
    '******-0123',
    '<EMAIL>',
    true,
    null,
    false,
    true,
    'sub_test_subscription_id',
    true,
    NOW(),
    NOW()
);

-- Get the shop ID
\set shop_id (SELECT id FROM shops WHERE name = 'Elite Beauty Salon')

-- Insert shop business types
INSERT INTO shop_business_types (shop_id, business_type) VALUES
((SELECT id FROM shops WHERE name = 'Elite Beauty Salon'), 'BEAUTY_SALON'),
((SELECT id FROM shops WHERE name = 'Elite Beauty Salon'), 'HAIRDRESSER');

-- Insert test employee user
INSERT INTO users (id, email, first_name, last_name, password, role, email_verified, enabled, created_at, updated_at)
VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    'Sarah',
    'Johnson',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTUDqyWvekKvrgFF.E/Nxu.TGfvOSWO2',
    'EMPLOYEE',
    true,
    true,
    NOW(),
    NOW()
);

-- Insert test employee
INSERT INTO employees (
    id,
    user_id,
    shop_id,
    specialties,
    bio,
    years_experience,
    hourly_rate,
    commission_rate,
    active,
    hire_date,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    (SELECT id FROM shops WHERE name = 'Elite Beauty Salon'),
    'Hair Cutting, Hair Coloring, Styling',
    'Expert stylist with 8 years of experience in modern hair techniques',
    8,
    45.00,
    0.30,
    true,
    NOW() - INTERVAL '2 years',
    NOW(),
    NOW()
);

-- Insert test services
INSERT INTO services (
    id,
    shop_id,
    employee_id,
    name,
    description,
    category,
    price,
    duration_minutes,
    deposit_amount,
    requires_deposit,
    online_booking_enabled,
    preparation_time_minutes,
    cleanup_time_minutes,
    active,
    created_at,
    updated_at
) VALUES 
(
    gen_random_uuid(),
    (SELECT id FROM shops WHERE name = 'Elite Beauty Salon'),
    (SELECT id FROM employees WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')),
    'Premium Haircut & Style',
    'Professional haircut with wash, cut, and styling',
    'Hair Services',
    75.00,
    90,
    25.00,
    true,
    true,
    10,
    15,
    true,
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    (SELECT id FROM shops WHERE name = 'Elite Beauty Salon'),
    (SELECT id FROM employees WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')),
    'Hair Color Treatment',
    'Full hair coloring service with premium products',
    'Hair Services',
    150.00,
    180,
    50.00,
    true,
    true,
    15,
    20,
    true,
    NOW(),
    NOW()
);

-- Insert employee schedule (Monday to Saturday)
INSERT INTO schedule_slots (
    id,
    employee_id,
    day_of_week,
    start_time,
    end_time,
    active,
    created_at,
    updated_at
) VALUES 
(gen_random_uuid(), (SELECT id FROM employees WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')), 'MONDAY', '09:00:00', '18:00:00', true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM employees WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')), 'TUESDAY', '09:00:00', '18:00:00', true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM employees WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')), 'WEDNESDAY', '09:00:00', '18:00:00', true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM employees WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')), 'THURSDAY', '09:00:00', '18:00:00', true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM employees WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')), 'FRIDAY', '09:00:00', '18:00:00', true, NOW(), NOW()),
(gen_random_uuid(), (SELECT id FROM employees WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')), 'SATURDAY', '10:00:00', '16:00:00', true, NOW(), NOW());

-- Insert a test customer user
INSERT INTO users (id, email, first_name, last_name, password, role, email_verified, enabled, created_at, updated_at)
VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    'Jane',
    'Doe',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTUDqyWvekKvrgFF.E/Nxu.TGfvOSWO2',
    'USER',
    true,
    true,
    NOW(),
    NOW()
);

-- Display the created test data
SELECT 'Test data created successfully!' as message;
SELECT 'Shop: ' || name || ' (ID: ' || id || ')' as shop_info FROM shops WHERE name = 'Elite Beauty Salon';
SELECT 'Employee: ' || u.first_name || ' ' || u.last_name || ' (ID: ' || e.id || ')' as employee_info 
FROM employees e JOIN users u ON e.user_id = u.id WHERE u.email = '<EMAIL>';
SELECT 'Services: ' || string_agg(name || ' (ID: ' || id || ')', ', ') as service_info FROM services;
EOF

echo "✅ Test data created successfully!"
echo ""

# Get the IDs from the database
echo "2. Retrieving test data IDs..."
SHOP_ID=$(PGPASSWORD=admin psql -h localhost -p 5433 -U postgres -d beautyhub -t -c "SELECT id FROM shops WHERE name = 'Elite Beauty Salon';" | tr -d ' ')
EMPLOYEE_ID=$(PGPASSWORD=admin psql -h localhost -p 5433 -U postgres -d beautyhub -t -c "SELECT e.id FROM employees e JOIN users u ON e.user_id = u.id WHERE u.email = '<EMAIL>';" | tr -d ' ')
SERVICE_ID=$(PGPASSWORD=admin psql -h localhost -p 5433 -U postgres -d beautyhub -t -c "SELECT id FROM services WHERE name = 'Premium Haircut & Style';" | tr -d ' ')

echo "Shop ID: $SHOP_ID"
echo "Employee ID: $EMPLOYEE_ID"
echo "Service ID: $SERVICE_ID"
echo ""

# Test 3: Test Available Slots (using Monday instead of Sunday)
echo "3. Testing Available Slots..."
response=$(curl -s "http://localhost:8080/api/appointments/available-slots?shopId=$SHOP_ID&employeeId=$EMPLOYEE_ID&serviceId=$SERVICE_ID&date=2025-06-16")
echo "Available slots response: $response"
echo ""

# Test 4: Test Slot Locking
echo "4. Testing Slot Locking..."
lock_data="{
    \"shopId\": \"$SHOP_ID\",
    \"serviceId\": \"$SERVICE_ID\",
    \"employeeId\": \"$EMPLOYEE_ID\",
    \"dateTime\": \"2025-06-16T14:00:00\"
}"

response=$(curl -s -X POST "$BASE_URL/api/appointments/lock-slot" \
    -H "Content-Type: application/json" \
    -d "$lock_data")
echo "Slot lock response: $response"

# Extract lock token from response
LOCK_TOKEN=$(echo "$response" | jq -r '.lockToken // empty')
echo "Extracted lock token: $LOCK_TOKEN"
echo ""

# Test 5: Test Payment Intent Creation
echo "5. Testing Payment Intent Creation..."
payment_data="{
    \"shopId\": \"$SHOP_ID\",
    \"serviceId\": \"$SERVICE_ID\",
    \"amount\": 75.00,
    \"currency\": \"usd\",
    \"description\": \"Premium Haircut & Style - Elite Beauty Salon\",
    \"customerEmail\": \"<EMAIL>\",
    \"customerName\": \"John Guest\"
}"

response=$(curl -s -X POST "$BASE_URL/api/payments/create-payment-intent" \
    -H "Content-Type: application/json" \
    -d "$payment_data")
echo "Payment intent response: $response"
echo ""

# Test 6: Test Guest Appointment Creation with Card Payment
echo "6. Testing Guest Appointment Creation with Card Payment..."
appointment_data="{
    \"shopId\": \"$SHOP_ID\",
    \"employeeId\": \"$EMPLOYEE_ID\",
    \"serviceId\": \"$SERVICE_ID\",
    \"appointmentDateTime\": \"2025-06-16T14:00:00\",
    \"paymentType\": \"CARD\",
    \"notes\": \"Test appointment with Stripe payment\",
    \"guestEmail\": \"<EMAIL>\",
    \"guestFirstName\": \"John\",
    \"guestLastName\": \"Guest\",
    \"guestPhone\": \"+1234567890\",
    \"slotLockToken\": \"$LOCK_TOKEN\"
}"

response=$(curl -s -X POST "$BASE_URL/api/appointments" \
    -H "Content-Type: application/json" \
    -d "$appointment_data")
echo "Guest appointment creation response: $response"
echo ""

echo "🎉 Appointment Creation Testing Complete!"
echo "========================================"
