-- Create test data for BeautyHub appointment testing
-- This script creates a complete test environment with shops, employees, services, and schedules

-- Insert test user (shop owner)
INSERT INTO users (id, email, first_name, last_name, password, role, email_verified, enabled, created_at, updated_at)
VALUES (
    '123e4567-e89b-12d3-a456-************',
    '<EMAIL>',
    '<PERSON>',
    '<PERSON>',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTUDqyWvekKvrgFF.E/Nxu.TGfvOSWO2', -- password: password123
    'OWNER',
    true,
    true,
    NOW(),
    NOW()
);

-- Insert test shop with Stripe connected account
INSERT INTO shops (
    id, 
    owner_id, 
    name, 
    description, 
    address, 
    city, 
    state, 
    postal_code, 
    country, 
    phone, 
    email,
    accepts_card_payments,
    stripe_account_id,
    stripe_onboarding_completed,
    subscription_active,
    subscription_id,
    active,
    created_at,
    updated_at
) VALUES (
    '223e4567-e89b-12d3-a456-************',
    '123e4567-e89b-12d3-a456-************',
    'Elite Beauty Salon',
    'Premium beauty services with expert stylists',
    '123 Beauty Street',
    'New York',
    'NY',
    '10001',
    'USA',
    '******-0123',
    '<EMAIL>',
    true,
    'acct_test_stripe_connected_account',
    true,
    true,
    'sub_test_subscription_id',
    true,
    NOW(),
    NOW()
);

-- Insert shop business types
INSERT INTO shop_business_types (shop_id, business_type) VALUES
('223e4567-e89b-12d3-a456-************', 'BEAUTY_SALON'),
('223e4567-e89b-12d3-a456-************', 'HAIRDRESSER');

-- Insert test employee user
INSERT INTO users (id, email, first_name, last_name, password, role, email_verified, enabled, created_at, updated_at)
VALUES (
    '323e4567-e89b-12d3-a456-************',
    '<EMAIL>',
    'Sarah',
    'Johnson',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTUDqyWvekKvrgFF.E/Nxu.TGfvOSWO2', -- password: password123
    'EMPLOYEE',
    true,
    true,
    NOW(),
    NOW()
);

-- Insert test employee
INSERT INTO employees (
    id,
    user_id,
    shop_id,
    specialties,
    bio,
    years_experience,
    hourly_rate,
    commission_rate,
    active,
    hire_date,
    created_at,
    updated_at
) VALUES (
    '423e4567-e89b-12d3-a456-************',
    '323e4567-e89b-12d3-a456-************',
    '223e4567-e89b-12d3-a456-************',
    'Hair Cutting, Hair Coloring, Styling',
    'Expert stylist with 8 years of experience in modern hair techniques',
    8,
    45.00,
    0.30,
    true,
    NOW() - INTERVAL '2 years',
    NOW(),
    NOW()
);

-- Insert test services
INSERT INTO services (
    id,
    shop_id,
    employee_id,
    name,
    description,
    category,
    price,
    duration_minutes,
    deposit_amount,
    requires_deposit,
    online_booking_enabled,
    preparation_time_minutes,
    cleanup_time_minutes,
    active,
    created_at,
    updated_at
) VALUES 
(
    '523e4567-e89b-12d3-a456-************',
    '223e4567-e89b-12d3-a456-************',
    '423e4567-e89b-12d3-a456-************',
    'Premium Haircut & Style',
    'Professional haircut with wash, cut, and styling',
    'Hair Services',
    75.00,
    90,
    25.00,
    true,
    true,
    10,
    15,
    true,
    NOW(),
    NOW()
),
(
    '623e4567-e89b-12d3-a456-************',
    '223e4567-e89b-12d3-a456-************',
    '423e4567-e89b-12d3-a456-************',
    'Hair Color Treatment',
    'Full hair coloring service with premium products',
    'Hair Services',
    150.00,
    180,
    50.00,
    true,
    true,
    15,
    20,
    true,
    NOW(),
    NOW()
);

-- Insert employee schedule (Monday to Friday, 9 AM to 6 PM)
INSERT INTO schedule_slots (
    id,
    employee_id,
    day_of_week,
    start_time,
    end_time,
    active,
    created_at,
    updated_at
) VALUES 
('723e4567-e89b-12d3-a456-426614174001', '423e4567-e89b-12d3-a456-************', 'MONDAY', '09:00:00', '18:00:00', true, NOW(), NOW()),
('723e4567-e89b-12d3-a456-426614174002', '423e4567-e89b-12d3-a456-************', 'TUESDAY', '09:00:00', '18:00:00', true, NOW(), NOW()),
('723e4567-e89b-12d3-a456-426614174003', '423e4567-e89b-12d3-a456-************', 'WEDNESDAY', '09:00:00', '18:00:00', true, NOW(), NOW()),
('723e4567-e89b-12d3-a456-426614174004', '423e4567-e89b-12d3-a456-************', 'THURSDAY', '09:00:00', '18:00:00', true, NOW(), NOW()),
('723e4567-e89b-12d3-a456-426614174005', '423e4567-e89b-12d3-a456-************', 'FRIDAY', '09:00:00', '18:00:00', true, NOW(), NOW()),
('723e4567-e89b-12d3-a456-426614174006', '423e4567-e89b-12d3-a456-************', 'SATURDAY', '10:00:00', '16:00:00', true, NOW(), NOW());

-- Insert a test customer user
INSERT INTO users (id, email, first_name, last_name, password, role, email_verified, enabled, created_at, updated_at)
VALUES (
    '823e4567-e89b-12d3-a456-************',
    '<EMAIL>',
    'Jane',
    'Doe',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTUDqyWvekKvrgFF.E/Nxu.TGfvOSWO2', -- password: password123
    'USER',
    true,
    true,
    NOW(),
    NOW()
);

-- Display the created test data
SELECT 'Test data created successfully!' as message;
SELECT 'Shop ID: 223e4567-e89b-12d3-a456-************' as shop_info;
SELECT 'Employee ID: 423e4567-e89b-12d3-a456-************' as employee_info;
SELECT 'Service IDs: 523e4567-e89b-12d3-a456-************, 623e4567-e89b-12d3-a456-************' as service_info;
SELECT 'Customer ID: 823e4567-e89b-12d3-a456-************' as customer_info;
