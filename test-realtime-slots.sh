#!/bin/bash

# Test script for real-time slot updates
echo "🧪 Testing Real-time Slot Updates"
echo "=================================="

# Test data
SHOP_ID="dfdfe915-da70-4ec1-a7bb-b52d0bb74788"
SERVICE_ID="6aaffcfd-161e-4525-8824-68c79ec41b01"
EMPLOYEE_ID="0e41472f-3b93-4821-8026-ee4b11d85068"
DATE_TIME="2025-06-11T17:00:00"

echo "📋 Test Configuration:"
echo "  Shop ID: $SHOP_ID"
echo "  Service ID: $SERVICE_ID"
echo "  Employee ID: $EMPLOYEE_ID"
echo "  Date Time: $DATE_TIME"
echo ""

# Test 1: Check WebSocket health
echo "🔍 Step 1: Checking WebSocket health..."
HEALTH_RESPONSE=$(curl -s "http://localhost:8080/api/test/websocket/health")
echo "  Response: $HEALTH_RESPONSE"

# Extract active connections count
ACTIVE_CONNECTIONS=$(echo $HEALTH_RESPONSE | grep -o '"activeConnections":[0-9]*' | cut -d':' -f2)
echo "  Active WebSocket connections: $ACTIVE_CONNECTIONS"
echo ""

# Test 2: Lock a slot
echo "🔒 Step 2: Locking a slot..."
LOCK_RESPONSE=$(curl -s -X POST "http://localhost:8080/api/appointments/lock-slot" \
  -H "Content-Type: application/json" \
  -d "{
    \"shopId\": \"$SHOP_ID\",
    \"serviceId\": \"$SERVICE_ID\",
    \"employeeId\": \"$EMPLOYEE_ID\",
    \"dateTime\": \"$DATE_TIME\"
  }")

echo "  Lock Response: $LOCK_RESPONSE"

# Extract lock token
LOCK_TOKEN=$(echo $LOCK_RESPONSE | grep -o '"lockToken":"[^"]*"' | cut -d'"' -f4)
echo "  Lock Token: $LOCK_TOKEN"
echo ""

# Test 3: Check WebSocket health again
echo "🔍 Step 3: Checking WebSocket health after lock..."
HEALTH_RESPONSE_2=$(curl -s "http://localhost:8080/api/test/websocket/health")
echo "  Response: $HEALTH_RESPONSE_2"
echo ""

# Test 4: Wait a moment and unlock the slot
echo "⏳ Step 4: Waiting 3 seconds..."
sleep 3

echo "🔓 Step 5: Unlocking the slot..."
UNLOCK_RESPONSE=$(curl -s -X POST "http://localhost:8080/api/appointments/unlock-slot" \
  -H "Content-Type: application/json" \
  -d "{
    \"shopId\": \"$SHOP_ID\",
    \"serviceId\": \"$SERVICE_ID\",
    \"employeeId\": \"$EMPLOYEE_ID\",
    \"dateTime\": \"$DATE_TIME\",
    \"userId\": \"$LOCK_TOKEN\"
  }")

echo "  Unlock Response: $UNLOCK_RESPONSE"
echo ""

# Test 5: Final WebSocket health check
echo "🔍 Step 6: Final WebSocket health check..."
HEALTH_RESPONSE_3=$(curl -s "http://localhost:8080/api/test/websocket/health")
echo "  Response: $HEALTH_RESPONSE_3"
echo ""

echo "✅ Test completed!"
echo ""
echo "📝 Instructions for manual testing:"
echo "1. Open http://localhost:3000/test/slot-updates in your browser"
echo "2. Click 'Connect WebSocket'"
echo "3. Run this script again to see real-time updates"
echo "4. Or open the appointment booking page: http://localhost:3000/shop/$SHOP_ID/book"
echo ""
echo "🎯 Expected behavior:"
echo "- When a slot is locked, all connected users should see it become unavailable"
echo "- When a slot is unlocked, all connected users should see it become available again"
echo "- The updates should happen in real-time without page refresh"
