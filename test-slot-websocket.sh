#!/bin/bash

# Test script for slot locking and WebSocket integration
# This script tests the complete workflow: API call -> WebSocket broadcast -> Frontend update

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_URL="http://localhost:8080"
FRONTEND_URL="http://localhost:3000"

# Test data
SHOP_ID="123e4567-e89b-12d3-a456-426614174000"
SERVICE_ID="123e4567-e89b-12d3-a456-426614174001"
EMPLOYEE_ID="123e4567-e89b-12d3-a456-426614174002"
DATE_TIME="2025-01-20T10:00:00"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if backend is running
check_backend() {
    print_status "Checking if backend is running..."
    
    if curl -s "$BACKEND_URL/api/health" > /dev/null 2>&1; then
        print_success "Backend is running at $BACKEND_URL"
        return 0
    else
        print_error "Backend is not running at $BACKEND_URL"
        print_error "Please start the Spring Boot application first"
        return 1
    fi
}

# Check if frontend is running
check_frontend() {
    print_status "Checking if frontend is running..."
    
    if curl -s "$FRONTEND_URL" > /dev/null 2>&1; then
        print_success "Frontend is running at $FRONTEND_URL"
        return 0
    else
        print_warning "Frontend is not running at $FRONTEND_URL"
        print_warning "You can start it with: npm start"
        return 1
    fi
}

# Test WebSocket health
test_websocket_health() {
    print_status "Testing WebSocket health..."
    
    response=$(curl -s "$BACKEND_URL/api/websocket/health" || echo "ERROR")
    
    if [[ "$response" == "ERROR" ]]; then
        print_error "Failed to get WebSocket health status"
        return 1
    fi
    
    echo "$response" | jq . 2>/dev/null || echo "$response"
    print_success "WebSocket health check completed"
}

# Test slot locking API
test_slot_lock() {
    print_status "Testing slot lock API..."
    
    local lock_request="{
        \"shopId\": \"$SHOP_ID\",
        \"serviceId\": \"$SERVICE_ID\",
        \"employeeId\": \"$EMPLOYEE_ID\",
        \"dateTime\": \"$DATE_TIME\"
    }"
    
    print_status "Sending lock request: $lock_request"
    
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$lock_request" \
        "$BACKEND_URL/api/appointments/lock-slot" || echo "ERROR")
    
    if [[ "$response" == "ERROR" ]]; then
        print_error "Failed to lock slot"
        return 1
    fi
    
    echo "$response" | jq . 2>/dev/null || echo "$response"
    
    # Extract lock token if present
    lock_token=$(echo "$response" | jq -r '.lockToken // .data.lockToken // empty' 2>/dev/null)
    
    if [[ -n "$lock_token" && "$lock_token" != "null" ]]; then
        print_success "Slot locked successfully with token: $lock_token"
        echo "$lock_token" > /tmp/lock_token.txt
        return 0
    else
        print_error "Failed to get lock token from response"
        return 1
    fi
}

# Test slot unlocking API
test_slot_unlock() {
    print_status "Testing slot unlock API..."
    
    if [[ ! -f /tmp/lock_token.txt ]]; then
        print_error "No lock token found. Run lock test first."
        return 1
    fi
    
    lock_token=$(cat /tmp/lock_token.txt)
    
    local unlock_request="{
        \"shopId\": \"$SHOP_ID\",
        \"serviceId\": \"$SERVICE_ID\",
        \"employeeId\": \"$EMPLOYEE_ID\",
        \"dateTime\": \"$DATE_TIME\",
        \"lockToken\": \"$lock_token\"
    }"
    
    print_status "Sending unlock request: $unlock_request"
    
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$unlock_request" \
        "$BACKEND_URL/api/appointments/unlock-slot" || echo "ERROR")
    
    if [[ "$response" == "ERROR" ]]; then
        print_error "Failed to unlock slot"
        return 1
    fi
    
    echo "$response" | jq . 2>/dev/null || echo "$response"
    print_success "Slot unlocked successfully"
    
    # Clean up
    rm -f /tmp/lock_token.txt
}

# Test WebSocket stats
test_websocket_stats() {
    print_status "Getting WebSocket connection stats..."
    
    response=$(curl -s "$BACKEND_URL/api/websocket/stats" || echo "ERROR")
    
    if [[ "$response" == "ERROR" ]]; then
        print_error "Failed to get WebSocket stats"
        return 1
    fi
    
    echo "$response" | jq . 2>/dev/null || echo "$response"
    print_success "WebSocket stats retrieved"
}

# Main test execution
main() {
    echo ""
    print_status "Starting slot locking and WebSocket integration tests..."
    echo ""
    
    # Check prerequisites
    if ! check_backend; then
        exit 1
    fi
    
    check_frontend
    echo ""
    
    # Run tests
    test_websocket_health
    echo ""
    
    test_websocket_stats
    echo ""
    
    test_slot_lock
    echo ""
    
    print_status "Waiting 2 seconds for WebSocket broadcast..."
    sleep 2
    
    test_slot_unlock
    echo ""
    
    test_websocket_stats
    echo ""
    
    print_success "All tests completed!"
    echo ""
    print_status "To test the frontend integration:"
    print_status "1. Open $FRONTEND_URL/test/enhanced-workflow"
    print_status "2. Click 'Connect WS' to establish WebSocket connection"
    print_status "3. Click 'Lock Real Slot' to test the complete workflow"
    print_status "4. Open another browser tab to see real-time updates"
    echo ""
}

# Run main function
main "$@"
