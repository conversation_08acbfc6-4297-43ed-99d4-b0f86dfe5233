import React, { useState } from 'react'
import { But<PERSON> } from '../components/ui/Button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import WebSocketConnectionTest from '../components/test/WebSocketConnectionTest'
import SlotLockingFlowTest from '../components/test/SlotLockingFlowTest'
import SlotLockCleanupTest from '../components/test/SlotLockCleanupTest'
import SubscriptionDebugger from '../components/test/SubscriptionDebugger'
import SimpleSubscriptionTest from '../components/test/SimpleSubscriptionTest'
import DirectWebSocketTest from '../components/test/DirectWebSocketTest'
import DateExtractionTest from '../components/test/DateExtractionTest'
import EnhancedSlotWorkflowTest from '../components/test/EnhancedSlotWorkflowTest'
import SlotLockingAPITest from '../components/test/SlotLockingAPITest'
import WebSocketDebugger from '../components/test/WebSocketDebugger'
import MinimalWebSocketTest from '../components/test/MinimalWebSocketTest'

const TestPage = () => {
  const [activeTest, setActiveTest] = useState('minimal')

  const tests = [
    { id: 'minimal', name: 'Minimal WebSocket Test', component: MinimalWebSocketTest },
    { id: 'debugger-new', name: 'WebSocket Message Debugger', component: WebSocketDebugger },
    { id: 'enhanced', name: 'Enhanced Slot Workflow Test', component: EnhancedSlotWorkflowTest },
    { id: 'api', name: 'Slot Locking API Test', component: SlotLockingAPITest },
    { id: 'direct', name: 'Direct WebSocket Test', component: DirectWebSocketTest },
    { id: 'simple', name: 'Simple Subscription Test', component: SimpleSubscriptionTest },
    { id: 'debugger', name: 'Subscription Debugger', component: SubscriptionDebugger },
    { id: 'websocket', name: 'WebSocket Connection Test', component: WebSocketConnectionTest },
    { id: 'slotflow', name: 'Slot Locking Flow Test', component: SlotLockingFlowTest },
    { id: 'cleanup', name: 'Slot Lock Cleanup Test', component: SlotLockCleanupTest }
  ]

  const ActiveTestComponent = tests.find(test => test.id === activeTest)?.component

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">BeautyHub Test Suite</h1>
          <p className="text-gray-600">
            Comprehensive testing tools for WebSocket connections, slot locking, and real-time updates.
          </p>
        </div>

        {/* Test Navigation */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {tests.map(test => (
                <Button
                  key={test.id}
                  onClick={() => setActiveTest(test.id)}
                  variant={activeTest === test.id ? 'default' : 'outline'}
                  className={activeTest === test.id ? 'bg-blue-600 hover:bg-blue-700' : ''}
                >
                  {test.name}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Active Test Component */}
        {ActiveTestComponent && <ActiveTestComponent />}

        {/* Instructions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm text-gray-600">
              <div>
                <h4 className="font-semibold text-gray-900">WebSocket Connection Test</h4>
                <p>Tests basic WebSocket connectivity, subscription management, and message handling.</p>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900">Slot Locking Flow Test</h4>
                <p>
                  Comprehensive test for the slot locking workflow. This test will:
                  <br />• Connect to WebSocket and subscribe to slot updates
                  <br />• Lock a slot via API and check for WebSocket notifications
                  <br />• Unlock a slot via API and check for WebSocket notifications
                  <br />• Test direct WebSocket messaging
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900">Slot Lock Cleanup Test</h4>
                <p>Tests slot lock token management, cleanup scenarios, and session storage handling.</p>
              </div>

              <div className="bg-blue-50 p-3 rounded">
                <h4 className="font-semibold text-blue-900">Multi-User Testing</h4>
                <p className="text-blue-800">
                  To test real-time updates between users:
                  <br />1. Open this page in two different browser windows/tabs
                  <br />2. Run the "Slot Locking Flow Test" in one window
                  <br />3. Watch for real-time updates in the other window
                  <br />4. Both windows should show the same slot lock/unlock events
                </p>
              </div>

              <div className="bg-yellow-50 p-3 rounded">
                <h4 className="font-semibold text-yellow-900">Expected Behavior</h4>
                <p className="text-yellow-800">
                  When User A locks a slot, User B should immediately see:
                  <br />• WebSocket message received in the messages panel
                  <br />• Slot status updated in real-time
                  <br />• Visual indicators showing the slot is locked
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default TestPage
