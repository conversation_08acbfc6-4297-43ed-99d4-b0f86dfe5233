-- Test users
INSERT INTO users (id, email, password, first_name, last_name, role, enabled, email_verified, created_at, updated_at) VALUES
('11111111-1111-1111-1111-111111111111', '<EMAIL>', 'hashedPassword123', '<PERSON>', 'Customer', 'USER', true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('22222222-2222-2222-2222-222222222222', '<EMAIL>', 'hashedPassword123', 'Jane', 'Owner', 'OWNER', true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('33333333-3333-3333-3333-333333333333', '<EMAIL>', 'hashedPassword123', 'Sarah', 'Stylist', 'EMPLOYEE', true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('44444444-4444-4444-4444-444444444444', '<EMAIL>', 'hashedPassword123', 'Wrong', 'User', 'USER', true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Test shop
INSERT INTO shops (id, owner_id, name, description, address, city, state, postal_code, country, phone, email, active, accepts_card_payments, subscription_active, stripe_onboarding_completed, created_at, updated_at) VALUES
('55555555-5555-5555-5555-555555555555', '22222222-2222-2222-2222-222222222222', 'Elite Beauty Salon', 'Premium beauty services', '123 Beauty Street', 'New York', 'NY', '10001', 'USA', '******-0123', '<EMAIL>', true, true, true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Test shop business types
INSERT INTO shop_business_types (shop_id, business_type) VALUES
('55555555-5555-5555-5555-555555555555', 'HAIRDRESSER'),
('55555555-5555-5555-5555-555555555555', 'BEAUTY_SALON');

-- Test employee
INSERT INTO employees (id, user_id, shop_id, bio, specialties, years_experience, hourly_rate, commission_rate, active, hire_date, created_at, updated_at) VALUES
('66666666-6666-6666-6666-666666666666', '33333333-3333-3333-3333-333333333333', '55555555-5555-5555-5555-555555555555', 'Experienced hair stylist with 10+ years', 'Hair Cutting, Hair Coloring, Styling', 10, 75.00, 0.20, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Test schedule slot
INSERT INTO schedule_slots (id, employee_id, day_of_week, start_time, end_time, active, created_at, updated_at) VALUES
('77777777-7777-7777-7777-777777777777', '66666666-6666-6666-6666-666666666666', 'MONDAY', '09:00:00', '18:00:00', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Test service
INSERT INTO services (id, employee_id, shop_id, name, description, price, duration_minutes, category, active, online_booking_enabled, requires_deposit, deposit_amount, created_at, updated_at) VALUES
('88888888-8888-8888-8888-888888888888', '66666666-6666-6666-6666-666666666666', '55555555-5555-5555-5555-555555555555', 'Premium Haircut & Style', 'Professional haircut with wash, cut, and styling', 75.00, 90, 'Hair', true, true, true, 25.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Test appointment
INSERT INTO appointments (id, user_id, shop_id, employee_id, service_id, appointment_datetime, end_datetime, payment_type, total_amount, status, confirmation_sent, reminder_sent, created_at, updated_at) VALUES
('99999999-9999-9999-9999-999999999999', '11111111-1111-1111-1111-111111111111', '55555555-5555-5555-5555-555555555555', '66666666-6666-6666-6666-666666666666', '88888888-8888-8888-8888-888888888888', DATEADD('DAY', 3, CURRENT_TIMESTAMP), DATEADD('MINUTE', 90, DATEADD('DAY', 3, CURRENT_TIMESTAMP)), 'CASH', 75.00, 'PENDING', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
