# Test Database Configuration
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA Configuration for Tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.defer-datasource-initialization=true

# Disable Flyway for tests
spring.flyway.enabled=false

# Redis Configuration for Tests (use embedded Redis or mock)
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.timeout=2000ms
spring.redis.database=1

# JWT Configuration for Tests (Base64 encoded)
app.jwt.secret=dGVzdFNlY3JldEtleUZvckpXVFRva2VuR2VuZXJhdGlvbkluVGVzdEVudmlyb25tZW50T25seVRlc3RTZWNyZXRLZXlGb3JKV1RUb2tlbkdlbmVyYXRpb25JblRlc3RFbnZpcm9ubWVudE9ubHk=
app.jwt.expiration=86400000

# Logging Configuration for Tests
logging.level.com.ddimitko.beautyhub=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Disable WebSocket for tests
websocket.enabled=false

# Test-specific configurations
spring.test.database.replace=none
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

# Security Configuration for Tests
spring.security.user.name=testuser
spring.security.user.password=testpass
spring.security.user.roles=USER

# Disable external services for tests
stripe.enabled=false
firebase.enabled=false
email.enabled=false

# Test data configuration
test.data.cleanup=true
test.data.seed=false

# Performance settings for tests
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# Connection pool settings for tests
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.minimum-idle=1
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.idle-timeout=300000

# Validation settings
spring.jpa.properties.hibernate.validator.apply_to_ddl=false
spring.jpa.properties.hibernate.validator.autoregister_listeners=false
