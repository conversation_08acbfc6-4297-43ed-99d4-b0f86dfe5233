package com.ddimitko.beautyhub.integration

import com.ddimitko.beautyhub.dto.*
import com.ddimitko.beautyhub.entity.*
import com.ddimitko.beautyhub.enums.*
import com.ddimitko.beautyhub.repository.*
import com.ddimitko.beautyhub.service.SlotLockingService
import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.MvcResult
import org.springframework.transaction.annotation.Transactional

import java.time.DayOfWeek
import java.time.LocalDateTime
import java.time.LocalTime

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("Appointment Validation Integration Tests")
class AppointmentValidationIntegrationTest {

    @Autowired
    private MockMvc mockMvc

    @Autowired
    private ObjectMapper objectMapper

    @Autowired
    private UserRepository userRepository

    @Autowired
    private ShopRepository shopRepository

    @Autowired
    private EmployeeRepository employeeRepository

    @Autowired
    private ServiceRepository serviceRepository

    @Autowired
    private AppointmentRepository appointmentRepository

    @Autowired
    private ScheduleSlotRepository scheduleSlotRepository

    @Autowired
    private SlotLockingService slotLockingService

    @Autowired
    private RedisTemplate<String, Object> redisTemplate

    private User testUser
    private User ownerUser
    private User employeeUser
    private Shop testShop
    private Employee testEmployee
    private Service testService
    private ScheduleSlot mondaySchedule

    @BeforeEach
    void setUp() {
        // Clean up all repositories
        appointmentRepository.deleteAll()
        scheduleSlotRepository.deleteAll()
        serviceRepository.deleteAll()
        employeeRepository.deleteAll()
        shopRepository.deleteAll()
        userRepository.deleteAll()

        // Clean Redis
        redisTemplate.getConnectionFactory().getConnection().flushAll()

        // Create test user
        testUser = new User()
        testUser.email = "<EMAIL>"
        testUser.password = "hashedPassword123"
        testUser.firstName = "John"
        testUser.lastName = "Customer"
        testUser.role = UserRole.USER
        testUser.enabled = true
        testUser.emailVerified = true
        testUser = userRepository.save(testUser)

        // Create owner user
        ownerUser = new User()
        ownerUser.email = "<EMAIL>"
        ownerUser.password = "hashedPassword123"
        ownerUser.firstName = "Jane"
        ownerUser.lastName = "Owner"
        ownerUser.role = UserRole.OWNER
        ownerUser.enabled = true
        ownerUser.emailVerified = true
        ownerUser = userRepository.save(ownerUser)

        // Create employee user
        employeeUser = new User()
        employeeUser.email = "<EMAIL>"
        employeeUser.password = "hashedPassword123"
        employeeUser.firstName = "Sarah"
        employeeUser.lastName = "Stylist"
        employeeUser.role = UserRole.EMPLOYEE
        employeeUser.enabled = true
        employeeUser.emailVerified = true
        employeeUser = userRepository.save(employeeUser)

        // Create test shop
        testShop = new Shop()
        testShop.owner = ownerUser
        testShop.name = "Elite Beauty Salon"
        testShop.description = "Premium beauty services"
        testShop.businessTypes = [BusinessType.HAIRDRESSER, BusinessType.BEAUTY_SALON] as Set
        testShop.address = "123 Beauty Street"
        testShop.city = "New York"
        testShop.state = "NY"
        testShop.postalCode = "10001"
        testShop.country = "USA"
        testShop.phone = "******-0123"
        testShop.email = "<EMAIL>"
        testShop.active = true
        testShop.acceptsCardPayments = true
        testShop.subscriptionActive = true
        testShop.stripeOnboardingCompleted = true
        testShop = shopRepository.save(testShop)

        // Create test employee
        testEmployee = new Employee()
        testEmployee.user = employeeUser
        testEmployee.shop = testShop
        testEmployee.bio = "Experienced hair stylist with 10+ years"
        testEmployee.specialties = "Hair Cutting, Hair Coloring, Styling"
        testEmployee.yearsExperience = 10
        testEmployee.hourlyRate = new BigDecimal("75.00")
        testEmployee.commissionRate = new BigDecimal("0.20")
        testEmployee.active = true
        testEmployee.hireDate = LocalDateTime.now().minusYears(3)
        testEmployee = employeeRepository.save(testEmployee)

        // Create test service
        testService = new Service()
        testService.employee = testEmployee
        testService.shop = testShop
        testService.name = "Premium Haircut & Style"
        testService.description = "Professional haircut with wash, cut, and styling"
        testService.price = new BigDecimal("75.00")
        testService.durationMinutes = 90
        testService.category = "Hair"
        testService.active = true
        testService.onlineBookingEnabled = true
        testService.requiresDeposit = true
        testService.depositAmount = new BigDecimal("25.00")
        testService = serviceRepository.save(testService)

        // Create schedule slot for Monday
        mondaySchedule = new ScheduleSlot()
        mondaySchedule.employee = testEmployee
        mondaySchedule.dayOfWeek = com.ddimitko.beautyhub.enums.DayOfWeek.MONDAY
        mondaySchedule.startTime = LocalTime.of(9, 0)
        mondaySchedule.endTime = LocalTime.of(18, 0)
        mondaySchedule.active = true
        mondaySchedule = scheduleSlotRepository.save(mondaySchedule)
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["EMPLOYEE"])
    @DisplayName("Should prevent employee from booking at their own shop")
    void shouldPreventEmployeeFromBookingAtTheirOwnShop() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        // Lock slot first
        SlotLockRequest lockRequest = new SlotLockRequest()
        lockRequest.shopId = testShop.id
        lockRequest.serviceId = testService.id
        lockRequest.employeeId = testEmployee.id
        lockRequest.dateTime = appointmentTime

        MvcResult lockResult = mockMvc.perform(post("/api/appointments/lock-slot")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lockRequest)))
                .andExpect(status().isOk())
                .andReturn()

        String lockToken = objectMapper.readValue(
                lockResult.getResponse().getContentAsString(), Map.class
        ).get("lockToken") as String

        // Try to create appointment as employee at their own shop
        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CASH
        appointmentRequest.slotLockToken = lockToken

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment creation failed"))
                .andExpect(jsonPath('$.message').value("Employees cannot book appointments at their own shop"))
    }

    @Test
    @DisplayName("Should validate required guest information for unauthenticated bookings")
    void shouldValidateRequiredGuestInformationForUnauthenticatedBookings() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.slotLockToken = UUID.randomUUID().toString()
        // Missing guest information

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment creation failed"))
    }

    @Test
    @DisplayName("Should validate email format for guest bookings")
    void shouldValidateEmailFormatForGuestBookings() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.guestEmail = "invalid-email" // Invalid email format
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567890"
        appointmentRequest.slotLockToken = UUID.randomUUID().toString()

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
    }

    @Test
    @DisplayName("Should validate phone number format for guest bookings")
    void shouldValidatePhoneNumberFormatForGuestBookings() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "invalid-phone" // Invalid phone format
        appointmentRequest.slotLockToken = UUID.randomUUID().toString()

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
    }

    @Test
    @DisplayName("Should validate appointment time is not in the past")
    void shouldValidateAppointmentTimeIsNotInPast() throws Exception {
        LocalDateTime pastTime = LocalDateTime.now().minusHours(1)

        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = pastTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567890"
        appointmentRequest.slotLockToken = UUID.randomUUID().toString()

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.message').value("Appointment must be in the future"))
    }

    @Test
    @DisplayName("Should validate appointment time is within employee schedule")
    void shouldValidateAppointmentTimeIsWithinEmployeeSchedule() throws Exception {
        // Try to book outside working hours (before 9 AM on Monday)
        LocalDateTime outsideHours = LocalDateTime.of(2025, 6, 16, 8, 0)

        SlotLockRequest lockRequest = new SlotLockRequest()
        lockRequest.shopId = testShop.id
        lockRequest.serviceId = testService.id
        lockRequest.employeeId = testEmployee.id
        lockRequest.dateTime = outsideHours

        MvcResult lockResult = mockMvc.perform(post("/api/appointments/lock-slot")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lockRequest)))
                .andExpect(status().isOk())
                .andReturn()

        String lockToken = objectMapper.readValue(
                lockResult.getResponse().getContentAsString(), Map.class
        ).get("lockToken") as String

        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = outsideHours
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567890"
        appointmentRequest.slotLockToken = lockToken

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.message').value("Appointment time is outside employee's working hours"))
    }

    @Test
    @DisplayName("Should validate service belongs to employee")
    void shouldValidateServiceBelongsToEmployee() throws Exception {
        // Create another employee and service
        User anotherEmployeeUser = new User()
        anotherEmployeeUser.email = "<EMAIL>"
        anotherEmployeeUser.password = "hashedPassword123"
        anotherEmployeeUser.firstName = "Another"
        anotherEmployeeUser.lastName = "Employee"
        anotherEmployeeUser.role = UserRole.EMPLOYEE
        anotherEmployeeUser.enabled = true
        anotherEmployeeUser.emailVerified = true
        anotherEmployeeUser = userRepository.save(anotherEmployeeUser)

        Employee anotherEmployee = new Employee()
        anotherEmployee.user = anotherEmployeeUser
        anotherEmployee.shop = testShop
        anotherEmployee.bio = "Another stylist"
        anotherEmployee.specialties = "Nails"
        anotherEmployee.yearsExperience = 5
        anotherEmployee.hourlyRate = new BigDecimal("50.00")
        anotherEmployee.commissionRate = new BigDecimal("0.15")
        anotherEmployee.active = true
        anotherEmployee.hireDate = LocalDateTime.now().minusYears(1)
        anotherEmployee = employeeRepository.save(anotherEmployee)

        Service anotherService = new Service()
        anotherService.employee = anotherEmployee
        anotherService.shop = testShop
        anotherService.name = "Manicure"
        anotherService.description = "Professional manicure"
        anotherService.price = new BigDecimal("40.00")
        anotherService.durationMinutes = 60
        anotherService.category = "Nails"
        anotherService.active = true
        anotherService.onlineBookingEnabled = true
        anotherService = serviceRepository.save(anotherService)

        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        // Try to book service from one employee with another employee
        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id // First employee
        appointmentRequest.serviceId = anotherService.id // Second employee's service
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567890"
        appointmentRequest.slotLockToken = UUID.randomUUID().toString()

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment creation failed"))
    }

    @Test
    @DisplayName("Should validate negative deposit amount")
    void shouldValidateNegativeDepositAmount() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567890"
        appointmentRequest.slotLockToken = UUID.randomUUID().toString()
        appointmentRequest.depositAmount = new BigDecimal("-10.00") // Negative deposit

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
    }
}
