package com.ddimitko.beautyhub.integration

import com.ddimitko.beautyhub.entity.*
import com.ddimitko.beautyhub.enums.*
import com.ddimitko.beautyhub.repository.*
import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.security.test.context.support.WithUserDetails
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.transaction.annotation.Transactional

import java.time.DayOfWeek
import java.time.LocalDateTime
import java.time.LocalTime

import static org.junit.jupiter.api.Assertions.*
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
@Sql("/test-data.sql")
@DisplayName("Appointment Cancellation Integration Tests")
class AppointmentCancellationIntegrationTest {

    @Autowired
    private MockMvc mockMvc

    @Autowired
    private ObjectMapper objectMapper

    @Autowired
    private UserRepository userRepository

    @Autowired
    private ShopRepository shopRepository

    @Autowired
    private EmployeeRepository employeeRepository

    @Autowired
    private ServiceRepository serviceRepository

    @Autowired
    private AppointmentRepository appointmentRepository

    @Autowired
    private ScheduleSlotRepository scheduleSlotRepository

    // Predefined UUIDs from test-data.sql
    private static final UUID TEST_USER_ID = UUID.fromString("11111111-1111-1111-1111-111111111111")
    private static final UUID OWNER_USER_ID = UUID.fromString("*************-2222-2222-************")
    private static final UUID EMPLOYEE_USER_ID = UUID.fromString("*************-3333-3333-************")
    private static final UUID WRONG_USER_ID = UUID.fromString("*************-4444-4444-************")
    private static final UUID TEST_SHOP_ID = UUID.fromString("*************-5555-5555-************")
    private static final UUID TEST_EMPLOYEE_ID = UUID.fromString("*************-6666-6666-************")
    private static final UUID TEST_SERVICE_ID = UUID.fromString("*************-8888-8888-************")
    private static final UUID TEST_APPOINTMENT_ID = UUID.fromString("*************-9999-9999-************")

    private User testUser
    private User ownerUser
    private Shop testShop
    private Employee testEmployee
    private Service testService
    private Appointment testAppointment

    @BeforeEach
    void setUp() {
        // Load entities from database (created by @Sql)
        testUser = userRepository.findById(TEST_USER_ID).orElseThrow()
        ownerUser = userRepository.findById(OWNER_USER_ID).orElseThrow()
        testShop = shopRepository.findById(TEST_SHOP_ID).orElseThrow()
        testEmployee = employeeRepository.findById(TEST_EMPLOYEE_ID).orElseThrow()
        testService = serviceRepository.findById(TEST_SERVICE_ID).orElseThrow()
        testAppointment = appointmentRepository.findById(TEST_APPOINTMENT_ID).orElseThrow()
    }

    @Test
    @WithUserDetails("<EMAIL>")
    @DisplayName("Should cancel appointment successfully")
    void shouldCancelAppointmentSuccessfully() throws Exception {
        Map<String, String> cancelRequest = [reason: "Schedule conflict"]

        mockMvc.perform(put("/api/appointments/${testAppointment.id}/cancel")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(cancelRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.message').value("Appointment cancelled successfully"))
                .andExpect(jsonPath('$.appointment.status').value("CANCELLED"))
                .andExpect(jsonPath('$.appointment.cancellationReason').value("Schedule conflict"))

        // Verify in database
        Optional<Appointment> cancelled = appointmentRepository.findById(testAppointment.id)
        assertTrue(cancelled.isPresent())
        assertEquals(AppointmentStatus.CANCELLED, cancelled.get().status)
        assertEquals("Schedule conflict", cancelled.get().cancellationReason)
        assertNotNull(cancelled.get().cancelledAt)
        assertEquals(testUser.id.toString(), cancelled.get().cancelledBy)
    }

    @Test
    @WithUserDetails("<EMAIL>")
    @DisplayName("Should fail to cancel appointment within 24 hours")
    void shouldFailToCancelAppointmentWithin24Hours() throws Exception {
        // Create appointment within 24 hours
        Appointment soonAppointment = new Appointment()
        soonAppointment.user = testUser
        soonAppointment.shop = testShop
        soonAppointment.employee = testEmployee
        soonAppointment.service = testService
        soonAppointment.appointmentDateTime = LocalDateTime.now().plusHours(12)
        soonAppointment.endDateTime = LocalDateTime.now().plusHours(13).plusMinutes(30)
        soonAppointment.paymentType = PaymentType.CASH
        soonAppointment.totalAmount = testService.price
        soonAppointment.status = AppointmentStatus.CONFIRMED
        soonAppointment = appointmentRepository.save(soonAppointment)

        Map<String, String> cancelRequest = [reason: "Emergency"]

        mockMvc.perform(put("/api/appointments/${soonAppointment.id}/cancel")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(cancelRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment cancellation failed"))
                .andExpect(jsonPath('$.message').value("Appointment cannot be cancelled"))
    }

    @Test
    @WithUserDetails("<EMAIL>")
    @DisplayName("Should fail to cancel already cancelled appointment")
    void shouldFailToCancelAlreadyCancelledAppointment() throws Exception {
        // Cancel the appointment first
        testAppointment.status = AppointmentStatus.CANCELLED
        testAppointment.cancellationReason = "Already cancelled"
        testAppointment.cancelledAt = LocalDateTime.now()
        appointmentRepository.save(testAppointment)

        Map<String, String> cancelRequest = [reason: "Another reason"]

        mockMvc.perform(put("/api/appointments/${testAppointment.id}/cancel")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(cancelRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment cancellation failed"))
                .andExpect(jsonPath('$.message').value("Appointment cannot be cancelled"))
    }

    @Test
    @WithUserDetails("<EMAIL>")
    @DisplayName("Should fail to cancel completed appointment")
    void shouldFailToCancelCompletedAppointment() throws Exception {
        // Mark appointment as completed
        testAppointment.status = AppointmentStatus.COMPLETED
        appointmentRepository.save(testAppointment)

        Map<String, String> cancelRequest = [reason: "Changed mind"]

        mockMvc.perform(put("/api/appointments/${testAppointment.id}/cancel")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(cancelRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment cancellation failed"))
                .andExpect(jsonPath('$.message').value("Appointment cannot be cancelled"))
    }

    @Test
    @WithUserDetails("<EMAIL>")
    @DisplayName("Should fail to cancel another user's appointment")
    void shouldFailToCancelAnotherUsersAppointment() throws Exception {
        Map<String, String> cancelRequest = [reason: "Unauthorized attempt"]

        mockMvc.perform(put("/api/appointments/${testAppointment.id}/cancel")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(cancelRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment cancellation failed"))
    }


}
