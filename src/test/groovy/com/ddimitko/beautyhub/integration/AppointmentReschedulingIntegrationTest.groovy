package com.ddimitko.beautyhub.integration

import com.ddimitko.beautyhub.dto.*
import com.ddimitko.beautyhub.entity.*
import com.ddimitko.beautyhub.enums.*
import com.ddimitko.beautyhub.repository.*
import com.ddimitko.beautyhub.service.SlotLockingService
import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.MvcResult
import org.springframework.transaction.annotation.Transactional

import java.time.DayOfWeek
import java.time.LocalDateTime
import java.time.LocalTime

import static org.junit.jupiter.api.Assertions.*
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("Appointment Rescheduling Integration Tests")
class AppointmentReschedulingIntegrationTest {

    @Autowired
    private MockMvc mockMvc

    @Autowired
    private ObjectMapper objectMapper

    @Autowired
    private UserRepository userRepository

    @Autowired
    private ShopRepository shopRepository

    @Autowired
    private EmployeeRepository employeeRepository

    @Autowired
    private ServiceRepository serviceRepository

    @Autowired
    private AppointmentRepository appointmentRepository

    @Autowired
    private ScheduleSlotRepository scheduleSlotRepository

    @Autowired
    private SlotLockingService slotLockingService

    @Autowired
    private RedisTemplate<String, Object> redisTemplate

    private User testUser
    private User ownerUser
    private Shop testShop
    private Employee testEmployee
    private Service testService
    private Appointment testAppointment
    private ScheduleSlot mondaySchedule

    @BeforeEach
    void setUp() {
        // Clean up all repositories
        appointmentRepository.deleteAll()
        scheduleSlotRepository.deleteAll()
        serviceRepository.deleteAll()
        employeeRepository.deleteAll()
        shopRepository.deleteAll()
        userRepository.deleteAll()

        // Clean Redis
        redisTemplate.getConnectionFactory().getConnection().flushAll()

        // Create test user
        testUser = new User()
        testUser.email = "<EMAIL>"
        testUser.password = "hashedPassword123"
        testUser.firstName = "John"
        testUser.lastName = "Customer"
        testUser.role = UserRole.USER
        testUser.enabled = true
        testUser.emailVerified = true
        testUser = userRepository.save(testUser)

        // Create owner user
        ownerUser = new User()
        ownerUser.email = "<EMAIL>"
        ownerUser.password = "hashedPassword123"
        ownerUser.firstName = "Jane"
        ownerUser.lastName = "Owner"
        ownerUser.role = UserRole.OWNER
        ownerUser.enabled = true
        ownerUser.emailVerified = true
        ownerUser = userRepository.save(ownerUser)

        // Create employee user
        User employeeUser = new User()
        employeeUser.email = "<EMAIL>"
        employeeUser.password = "hashedPassword123"
        employeeUser.firstName = "Sarah"
        employeeUser.lastName = "Stylist"
        employeeUser.role = UserRole.EMPLOYEE
        employeeUser.enabled = true
        employeeUser.emailVerified = true
        employeeUser = userRepository.save(employeeUser)

        // Create test shop
        testShop = new Shop()
        testShop.owner = ownerUser
        testShop.name = "Elite Beauty Salon"
        testShop.description = "Premium beauty services"
        testShop.businessTypes = [BusinessType.HAIRDRESSER, BusinessType.BEAUTY_SALON] as Set
        testShop.address = "123 Beauty Street"
        testShop.city = "New York"
        testShop.state = "NY"
        testShop.postalCode = "10001"
        testShop.country = "USA"
        testShop.phone = "******-0123"
        testShop.email = "<EMAIL>"
        testShop.active = true
        testShop.acceptsCardPayments = true
        testShop.subscriptionActive = true
        testShop.stripeOnboardingCompleted = true
        testShop = shopRepository.save(testShop)

        // Create test employee
        testEmployee = new Employee()
        testEmployee.user = employeeUser
        testEmployee.shop = testShop
        testEmployee.bio = "Experienced hair stylist with 10+ years"
        testEmployee.specialties = "Hair Cutting, Hair Coloring, Styling"
        testEmployee.yearsExperience = 10
        testEmployee.hourlyRate = new BigDecimal("75.00")
        testEmployee.commissionRate = new BigDecimal("0.20")
        testEmployee.active = true
        testEmployee.hireDate = LocalDateTime.now().minusYears(3)
        testEmployee = employeeRepository.save(testEmployee)

        // Create test service
        testService = new Service()
        testService.employee = testEmployee
        testService.shop = testShop
        testService.name = "Premium Haircut & Style"
        testService.description = "Professional haircut with wash, cut, and styling"
        testService.price = new BigDecimal("75.00")
        testService.durationMinutes = 90
        testService.category = "Hair"
        testService.active = true
        testService.onlineBookingEnabled = true
        testService.requiresDeposit = true
        testService.depositAmount = new BigDecimal("25.00")
        testService = serviceRepository.save(testService)

        // Create schedule slot for Monday
        mondaySchedule = new ScheduleSlot()
        mondaySchedule.employee = testEmployee
        mondaySchedule.dayOfWeek = com.ddimitko.beautyhub.enums.DayOfWeek.MONDAY
        mondaySchedule.startTime = LocalTime.of(9, 0)
        mondaySchedule.endTime = LocalTime.of(18, 0)
        mondaySchedule.active = true
        mondaySchedule = scheduleSlotRepository.save(mondaySchedule)

        // Create test appointment
        createTestAppointment()
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["USER"])
    @DisplayName("Should reschedule appointment successfully")
    void shouldRescheduleAppointmentSuccessfully() throws Exception {
        LocalDateTime newAppointmentTime = LocalDateTime.of(2025, 6, 16, 16, 0)

        // First, lock the new slot
        SlotLockRequest lockRequest = new SlotLockRequest()
        lockRequest.shopId = testShop.id
        lockRequest.serviceId = testService.id
        lockRequest.employeeId = testEmployee.id
        lockRequest.dateTime = newAppointmentTime

        MvcResult lockResult = mockMvc.perform(post("/api/appointments/lock-slot")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lockRequest)))
                .andExpect(status().isOk())
                .andReturn()

        String lockResponseContent = lockResult.getResponse().getContentAsString()
        Map<String, Object> lockResponse = objectMapper.readValue(lockResponseContent, Map.class)
        String lockToken = (String) lockResponse.get("lockToken")

        // Now reschedule the appointment
        AppointmentUpdateRequest updateRequest = new AppointmentUpdateRequest()
        updateRequest.appointmentDateTime = newAppointmentTime
        updateRequest.notes = "Rescheduled appointment"
        updateRequest.slotLockToken = lockToken

        mockMvc.perform(put("/api/appointments/${testAppointment.id}")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.message').value("Appointment updated successfully"))
                .andExpect(jsonPath('$.appointment.appointmentDateTime').exists())

        // Verify in database
        Optional<Appointment> updated = appointmentRepository.findById(testAppointment.id)
        assertTrue(updated.isPresent())
        assertEquals(newAppointmentTime, updated.get().appointmentDateTime)
        assertEquals("Rescheduled appointment", updated.get().notes)

        // Verify slot is no longer locked
        assertFalse(slotLockingService.isSlotLocked(testShop.id, testService.id, testEmployee.id, newAppointmentTime))
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["USER"])
    @DisplayName("Should fail to reschedule without lock token")
    void shouldFailToRescheduleWithoutLockToken() throws Exception {
        LocalDateTime newAppointmentTime = LocalDateTime.of(2025, 6, 16, 16, 0)

        AppointmentUpdateRequest updateRequest = new AppointmentUpdateRequest()
        updateRequest.appointmentDateTime = newAppointmentTime
        updateRequest.notes = "Rescheduled appointment"
        // No slotLockToken provided

        mockMvc.perform(put("/api/appointments/${testAppointment.id}")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment update failed"))
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["USER"])
    @DisplayName("Should fail to reschedule within 24 hours")
    void shouldFailToRescheduleWithin24Hours() throws Exception {
        // Create appointment within 24 hours
        Appointment soonAppointment = new Appointment()
        soonAppointment.user = testUser
        soonAppointment.shop = testShop
        soonAppointment.employee = testEmployee
        soonAppointment.service = testService
        soonAppointment.appointmentDateTime = LocalDateTime.now().plusHours(12)
        soonAppointment.endDateTime = LocalDateTime.now().plusHours(13).plusMinutes(30)
        soonAppointment.paymentType = PaymentType.CASH
        soonAppointment.totalAmount = testService.price
        soonAppointment.status = AppointmentStatus.CONFIRMED
        soonAppointment = appointmentRepository.save(soonAppointment)

        LocalDateTime newAppointmentTime = LocalDateTime.of(2025, 6, 16, 16, 0)

        AppointmentUpdateRequest updateRequest = new AppointmentUpdateRequest()
        updateRequest.appointmentDateTime = newAppointmentTime
        updateRequest.slotLockToken = UUID.randomUUID().toString()

        mockMvc.perform(put("/api/appointments/${soonAppointment.id}")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment update failed"))
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["USER"])
    @DisplayName("Should fail to reschedule to past time")
    void shouldFailToRescheduleToPastTime() throws Exception {
        LocalDateTime pastTime = LocalDateTime.now().minusHours(1)

        AppointmentUpdateRequest updateRequest = new AppointmentUpdateRequest()
        updateRequest.appointmentDateTime = pastTime
        updateRequest.slotLockToken = UUID.randomUUID().toString()

        mockMvc.perform(put("/api/appointments/${testAppointment.id}")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment update failed"))
                .andExpect(jsonPath('$.message').value("Appointment must be in the future"))
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["USER"])
    @DisplayName("Should fail to reschedule another user's appointment")
    void shouldFailToRescheduleAnotherUsersAppointment() throws Exception {
        LocalDateTime newAppointmentTime = LocalDateTime.of(2025, 6, 16, 16, 0)

        AppointmentUpdateRequest updateRequest = new AppointmentUpdateRequest()
        updateRequest.appointmentDateTime = newAppointmentTime
        updateRequest.slotLockToken = UUID.randomUUID().toString()

        mockMvc.perform(put("/api/appointments/${testAppointment.id}")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment update failed"))
    }

    private void createTestAppointment() {
        testAppointment = new Appointment()
        testAppointment.user = testUser
        testAppointment.shop = testShop
        testAppointment.employee = testEmployee
        testAppointment.service = testService
        testAppointment.appointmentDateTime = LocalDateTime.of(2025, 6, 16, 14, 0)
        testAppointment.endDateTime = LocalDateTime.of(2025, 6, 16, 15, 30)
        testAppointment.paymentType = PaymentType.CASH
        testAppointment.totalAmount = testService.price
        testAppointment.status = AppointmentStatus.PENDING
        testAppointment = appointmentRepository.save(testAppointment)
    }
}
