package com.ddimitko.beautyhub.integration

import com.ddimitko.beautyhub.entity.*
import com.ddimitko.beautyhub.enums.*
import com.ddimitko.beautyhub.repository.*
import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.transaction.annotation.Transactional

import java.time.LocalDateTime

import static org.junit.jupiter.api.Assertions.*
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("Appointment Status Management Integration Tests")
class AppointmentStatusManagementIntegrationTest {

    @Autowired
    private MockMvc mockMvc

    @Autowired
    private ObjectMapper objectMapper

    @Autowired
    private UserRepository userRepository

    @Autowired
    private ShopRepository shopRepository

    @Autowired
    private EmployeeRepository employeeRepository

    @Autowired
    private ServiceRepository serviceRepository

    @Autowired
    private AppointmentRepository appointmentRepository

    private User testUser
    private User ownerUser
    private User employeeUser
    private Shop testShop
    private Employee testEmployee
    private Service testService
    private Appointment testAppointment

    @BeforeEach
    void setUp() {
        // Clean up all repositories
        appointmentRepository.deleteAll()
        serviceRepository.deleteAll()
        employeeRepository.deleteAll()
        shopRepository.deleteAll()
        userRepository.deleteAll()

        // Create test user
        testUser = new User()
        testUser.email = "<EMAIL>"
        testUser.password = "hashedPassword123"
        testUser.firstName = "John"
        testUser.lastName = "Customer"
        testUser.role = UserRole.USER
        testUser.enabled = true
        testUser.emailVerified = true
        testUser = userRepository.save(testUser)

        // Create owner user
        ownerUser = new User()
        ownerUser.email = "<EMAIL>"
        ownerUser.password = "hashedPassword123"
        ownerUser.firstName = "Jane"
        ownerUser.lastName = "Owner"
        ownerUser.role = UserRole.OWNER
        ownerUser.enabled = true
        ownerUser.emailVerified = true
        ownerUser = userRepository.save(ownerUser)

        // Create employee user
        employeeUser = new User()
        employeeUser.email = "<EMAIL>"
        employeeUser.password = "hashedPassword123"
        employeeUser.firstName = "Sarah"
        employeeUser.lastName = "Stylist"
        employeeUser.role = UserRole.EMPLOYEE
        employeeUser.enabled = true
        employeeUser.emailVerified = true
        employeeUser = userRepository.save(employeeUser)

        // Create test shop
        testShop = new Shop()
        testShop.owner = ownerUser
        testShop.name = "Elite Beauty Salon"
        testShop.description = "Premium beauty services"
        testShop.businessTypes = [BusinessType.HAIRDRESSER, BusinessType.BEAUTY_SALON] as Set
        testShop.address = "123 Beauty Street"
        testShop.city = "New York"
        testShop.state = "NY"
        testShop.postalCode = "10001"
        testShop.country = "USA"
        testShop.phone = "******-0123"
        testShop.email = "<EMAIL>"
        testShop.active = true
        testShop.acceptsCardPayments = true
        testShop.subscriptionActive = true
        testShop.stripeOnboardingCompleted = true
        testShop = shopRepository.save(testShop)

        // Create test employee
        testEmployee = new Employee()
        testEmployee.user = employeeUser
        testEmployee.shop = testShop
        testEmployee.bio = "Experienced hair stylist with 10+ years"
        testEmployee.specialties = "Hair Cutting, Hair Coloring, Styling"
        testEmployee.yearsExperience = 10
        testEmployee.hourlyRate = new BigDecimal("75.00")
        testEmployee.commissionRate = new BigDecimal("0.20")
        testEmployee.active = true
        testEmployee.hireDate = LocalDateTime.now().minusYears(3)
        testEmployee = employeeRepository.save(testEmployee)

        // Create test service
        testService = new Service()
        testService.employee = testEmployee
        testService.shop = testShop
        testService.name = "Premium Haircut & Style"
        testService.description = "Professional haircut with wash, cut, and styling"
        testService.price = new BigDecimal("75.00")
        testService.durationMinutes = 90
        testService.category = "Hair"
        testService.active = true
        testService.onlineBookingEnabled = true
        testService.requiresDeposit = true
        testService.depositAmount = new BigDecimal("25.00")
        testService = serviceRepository.save(testService)

        // Create test appointment
        createTestAppointment()
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["EMPLOYEE"])
    @DisplayName("Should confirm pending appointment")
    void shouldConfirmPendingAppointment() throws Exception {
        assertEquals(AppointmentStatus.PENDING, testAppointment.status)

        Map<String, String> statusUpdate = [status: "CONFIRMED"]

        mockMvc.perform(put("/api/appointments/${testAppointment.id}/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusUpdate)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.message').value("Appointment status updated successfully"))
                .andExpect(jsonPath('$.appointment.status').value("CONFIRMED"))

        // Verify in database
        Optional<Appointment> updated = appointmentRepository.findById(testAppointment.id)
        assertTrue(updated.isPresent())
        assertEquals(AppointmentStatus.CONFIRMED, updated.get().status)
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["EMPLOYEE"])
    @DisplayName("Should complete confirmed appointment")
    void shouldCompleteConfirmedAppointment() throws Exception {
        // First confirm the appointment
        testAppointment.status = AppointmentStatus.CONFIRMED
        appointmentRepository.save(testAppointment)

        Map<String, String> statusUpdate = [status: "COMPLETED"]

        mockMvc.perform(put("/api/appointments/${testAppointment.id}/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusUpdate)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.message').value("Appointment status updated successfully"))
                .andExpect(jsonPath('$.appointment.status').value("COMPLETED"))

        // Verify in database
        Optional<Appointment> updated = appointmentRepository.findById(testAppointment.id)
        assertTrue(updated.isPresent())
        assertEquals(AppointmentStatus.COMPLETED, updated.get().status)
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["EMPLOYEE"])
    @DisplayName("Should mark appointment as no-show")
    void shouldMarkAppointmentAsNoShow() throws Exception {
        // Set appointment to past time and confirmed status
        testAppointment.appointmentDateTime = LocalDateTime.now().minusHours(2)
        testAppointment.endDateTime = LocalDateTime.now().minusMinutes(30)
        testAppointment.status = AppointmentStatus.CONFIRMED
        appointmentRepository.save(testAppointment)

        Map<String, String> statusUpdate = [status: "NO_SHOW"]

        mockMvc.perform(put("/api/appointments/${testAppointment.id}/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusUpdate)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.message').value("Appointment status updated successfully"))
                .andExpect(jsonPath('$.appointment.status').value("NO_SHOW"))

        // Verify in database
        Optional<Appointment> updated = appointmentRepository.findById(testAppointment.id)
        assertTrue(updated.isPresent())
        assertEquals(AppointmentStatus.NO_SHOW, updated.get().status)
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["USER"])
    @DisplayName("Should fail when customer tries to update status")
    void shouldFailWhenCustomerTriesToUpdateStatus() throws Exception {
        Map<String, String> statusUpdate = [status: "CONFIRMED"]

        mockMvc.perform(put("/api/appointments/${testAppointment.id}/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusUpdate)))
                .andExpect(status().isForbidden())
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["OWNER"])
    @DisplayName("Should allow owner to update appointment status")
    void shouldAllowOwnerToUpdateAppointmentStatus() throws Exception {
        Map<String, String> statusUpdate = [status: "CONFIRMED"]

        mockMvc.perform(put("/api/appointments/${testAppointment.id}/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusUpdate)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.appointment.status').value("CONFIRMED"))
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["EMPLOYEE"])
    @DisplayName("Should fail to update status to invalid value")
    void shouldFailToUpdateStatusToInvalidValue() throws Exception {
        Map<String, String> statusUpdate = [status: "INVALID_STATUS"]

        mockMvc.perform(put("/api/appointments/${testAppointment.id}/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusUpdate)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Status update failed"))
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["EMPLOYEE"])
    @DisplayName("Should fail to update cancelled appointment status")
    void shouldFailToUpdateCancelledAppointmentStatus() throws Exception {
        // Cancel the appointment first
        testAppointment.status = AppointmentStatus.CANCELLED
        testAppointment.cancellationReason = "Customer cancelled"
        testAppointment.cancelledAt = LocalDateTime.now()
        appointmentRepository.save(testAppointment)

        Map<String, String> statusUpdate = [status: "CONFIRMED"]

        mockMvc.perform(put("/api/appointments/${testAppointment.id}/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusUpdate)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Status update failed"))
                .andExpect(jsonPath('$.message').value("Cannot update status of cancelled appointment"))
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["EMPLOYEE"])
    @DisplayName("Should fail to update completed appointment status")
    void shouldFailToUpdateCompletedAppointmentStatus() throws Exception {
        // Mark appointment as completed
        testAppointment.status = AppointmentStatus.COMPLETED
        appointmentRepository.save(testAppointment)

        Map<String, String> statusUpdate = [status: "PENDING"]

        mockMvc.perform(put("/api/appointments/${testAppointment.id}/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusUpdate)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Status update failed"))
                .andExpect(jsonPath('$.message').value("Cannot update status of completed appointment"))
    }

    @Test
    @DisplayName("Should retrieve appointments by status")
    void shouldRetrieveAppointmentsByStatus() throws Exception {
        // Create appointments with different statuses
        createAppointmentWithStatus(AppointmentStatus.CONFIRMED)
        createAppointmentWithStatus(AppointmentStatus.COMPLETED)
        createAppointmentWithStatus(AppointmentStatus.CANCELLED)

        mockMvc.perform(get("/api/appointments/by-status")
                .param("status", "PENDING"))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$').isArray())
                .andExpect(jsonPath('$.length()').value(1))
                .andExpect(jsonPath('$[0].status').value("PENDING"))

        mockMvc.perform(get("/api/appointments/by-status")
                .param("status", "CONFIRMED"))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$').isArray())
                .andExpect(jsonPath('$.length()').value(1))
                .andExpect(jsonPath('$[0].status').value("CONFIRMED"))
    }

    private void createTestAppointment() {
        testAppointment = new Appointment()
        testAppointment.user = testUser
        testAppointment.shop = testShop
        testAppointment.employee = testEmployee
        testAppointment.service = testService
        testAppointment.appointmentDateTime = LocalDateTime.now().plusDays(3)
        testAppointment.endDateTime = LocalDateTime.now().plusDays(3).plusMinutes(90)
        testAppointment.paymentType = PaymentType.CASH
        testAppointment.totalAmount = testService.price
        testAppointment.status = AppointmentStatus.PENDING
        testAppointment = appointmentRepository.save(testAppointment)
    }

    private void createAppointmentWithStatus(AppointmentStatus status) {
        Appointment appointment = new Appointment()
        appointment.user = testUser
        appointment.shop = testShop
        appointment.employee = testEmployee
        appointment.service = testService
        appointment.appointmentDateTime = LocalDateTime.now().plusDays(5)
        appointment.endDateTime = LocalDateTime.now().plusDays(5).plusMinutes(90)
        appointment.paymentType = PaymentType.CASH
        appointment.totalAmount = testService.price
        appointment.status = status
        appointmentRepository.save(appointment)
    }
}
