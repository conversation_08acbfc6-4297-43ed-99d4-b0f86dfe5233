package com.ddimitko.beautyhub.integration

import com.ddimitko.beautyhub.config.JwtConfig
import com.ddimitko.beautyhub.config.TestConfig

import com.ddimitko.beautyhub.dto.ShopCreationRequest
import com.ddimitko.beautyhub.dto.ShopCreationResponse
import com.ddimitko.beautyhub.dto.SubscriptionRequest
import com.ddimitko.beautyhub.dto.SubscriptionResponse
import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.enums.BusinessType
import com.ddimitko.beautyhub.enums.UserRole
import com.ddimitko.beautyhub.repository.ShopRepository
import com.ddimitko.beautyhub.repository.UserRepository
import com.ddimitko.beautyhub.security.CustomUserPrincipal
import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.transaction.annotation.Transactional

import static org.junit.jupiter.api.Assertions.*
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("Shop Creation with Subscription Integration Tests")
class ShopCreationWithSubscriptionIntegrationTest {

    @Autowired
    private MockMvc mockMvc

    @Autowired
    private ObjectMapper objectMapper

    @Autowired
    private UserRepository userRepository

    @Autowired
    private ShopRepository shopRepository

    @Autowired
    private JwtConfig jwtConfig

    private String jwtToken
    private User ownerUser

    @BeforeEach
    void setUp() {
        // Clean up database
        shopRepository.deleteAll()
        userRepository.deleteAll()

        // Create and save owner user
        ownerUser = new User()
        ownerUser.email = "<EMAIL>"
        ownerUser.password = "encodedPassword"
        ownerUser.firstName = "John"
        ownerUser.lastName = "Doe"
        ownerUser.role = UserRole.OWNER
        ownerUser = userRepository.save(ownerUser)

        // Generate JWT token using CustomUserPrincipal
        CustomUserPrincipal userPrincipal = CustomUserPrincipal.create(ownerUser)
        jwtToken = jwtConfig.generateToken(userPrincipal)
    }

    @Test
    @DisplayName("Should complete full shop creation with subscription flow")
    void shouldCompleteFullShopCreationWithSubscriptionFlow() throws Exception {
        // Step 1: Create shop that accepts card payments
        ShopCreationRequest shopRequest = new ShopCreationRequest()
        shopRequest.name = "Premium Beauty Salon"
        shopRequest.description = "A premium beauty salon with card payment support"
        shopRequest.businessTypes = [BusinessType.HAIRDRESSER, BusinessType.BEAUTY_SALON] as Set
        shopRequest.address = "123 Premium Street"
        shopRequest.city = "New York"
        shopRequest.state = "NY"
        shopRequest.postalCode = "10001"
        shopRequest.country = "USA"
        shopRequest.phone = "******-PREMIUM"
        shopRequest.email = "<EMAIL>"
        shopRequest.website = "https://premiumbeauty.com"
        shopRequest.acceptsCardPayments = true

        String shopCreationResponse = mockMvc.perform(post("/api/shops")
                .header("Authorization", "Bearer " + jwtToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(shopRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("\$.shopId").exists())
                .andExpect(jsonPath("\$.message").value("Shop created successfully!"))
                .andExpect(jsonPath("\$.name").value(shopRequest.name))
                .andExpect(jsonPath("\$.requiresSubscription").value(true))
                .andExpect(jsonPath("\$.nextStep").value("subscription_setup"))
                .andReturn().getResponse().getContentAsString()

        ShopCreationResponse shopResponse = objectMapper.readValue(shopCreationResponse, ShopCreationResponse.class)
        UUID shopId = shopResponse.shopId

        // Verify shop was created but subscription is not active
        Shop createdShop = shopRepository.findById(shopId).orElse(null)
        assertNotNull(createdShop)
        assertEquals(shopRequest.name, createdShop.name)
        assertTrue(createdShop.acceptsCardPayments)
        assertFalse(createdShop.subscriptionActive)
        assertNull(createdShop.subscriptionId)

        // Step 2: Create subscription for the shop
        SubscriptionRequest subscriptionRequest = new SubscriptionRequest()
        subscriptionRequest.planName = "Premium Plan"
        subscriptionRequest.amount = 4999 // $49.99
        subscriptionRequest.currency = "usd"
        subscriptionRequest.interval = "month"
        subscriptionRequest.billingEmail = "<EMAIL>"
        subscriptionRequest.billingName = "John Doe"

        String subscriptionCreationResponse = mockMvc.perform(post("/api/subscriptions/shops/{shopId}", shopId)
                .header("Authorization", "Bearer " + jwtToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(subscriptionRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("\$.shopId").value(shopId.toString()))
                .andExpect(jsonPath("\$.subscriptionId").exists())
                .andExpect(jsonPath("\$.stripeAccountId").exists())
                .andExpect(jsonPath("\$.status").value("active"))
                .andExpect(jsonPath("\$.planName").value("Premium Plan"))
                .andExpect(jsonPath("\$.amount").value(4999))
                .andExpect(jsonPath("\$.currency").value("usd"))
                .andExpect(jsonPath("\$.interval").value("month"))
                .andExpect(jsonPath("\$.isActive").value(true))
                .andReturn().getResponse().getContentAsString()

        SubscriptionResponse subscriptionResponse = objectMapper.readValue(subscriptionCreationResponse, SubscriptionResponse.class)

        // Verify subscription was created and shop was updated
        Shop updatedShop = shopRepository.findById(shopId).orElse(null)
        assertNotNull(updatedShop)
        assertTrue(updatedShop.subscriptionActive)
        assertNotNull(updatedShop.subscriptionId)
        assertNotNull(updatedShop.stripeAccountId)
        assertTrue(updatedShop.stripeOnboardingCompleted)
        assertTrue(updatedShop.subscriptionId.startsWith("sub_fake_"))
        assertTrue(updatedShop.stripeAccountId.startsWith("acct_fake_"))

        // Step 3: Validate subscription
        mockMvc.perform(post("/api/subscriptions/validate/{shopId}", shopId)
                .header("Authorization", "Bearer " + jwtToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("\$.shopId").value(shopId.toString()))
                .andExpect(jsonPath("\$.subscriptionValid").value(true))
                .andExpect(jsonPath("\$.message").value("Subscription is valid"))

        // Step 4: Get subscription details
        mockMvc.perform(get("/api/subscriptions/shops/{shopId}", shopId)
                .header("Authorization", "Bearer " + jwtToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("\$.shopId").value(shopId.toString()))
                .andExpect(jsonPath("\$.subscriptionId").value(subscriptionResponse.subscriptionId))
                .andExpect(jsonPath("\$.stripeAccountId").value(subscriptionResponse.stripeAccountId))
                .andExpect(jsonPath("\$.status").value("active"))

        // Step 5: Verify shop can now accept payments
        assertTrue(updatedShop.canAcceptPayments())
        assertTrue(updatedShop.isSubscriptionValid())
    }

    @Test
    @DisplayName("Should handle shop creation without card payments (no subscription required)")
    void shouldHandleShopCreationWithoutCardPayments() throws Exception {
        // Create shop that doesn't accept card payments
        ShopCreationRequest shopRequest = new ShopCreationRequest()
        shopRequest.name = "Cash Only Salon"
        shopRequest.description = "A salon that only accepts cash"
        shopRequest.businessTypes = [BusinessType.HAIRDRESSER] as Set
        shopRequest.address = "456 Cash Street"
        shopRequest.city = "Boston"
        shopRequest.state = "MA"
        shopRequest.postalCode = "02101"
        shopRequest.country = "USA"
        shopRequest.phone = "******-CASHONLY"
        shopRequest.email = "<EMAIL>"
        shopRequest.acceptsCardPayments = false

        mockMvc.perform(post("/api/shops")
                .header("Authorization", "Bearer " + jwtToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(shopRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("\$.shopId").exists())
                .andExpect(jsonPath("\$.message").value("Shop created successfully!"))
                .andExpect(jsonPath("\$.name").value(shopRequest.name))
                .andExpect(jsonPath("\$.requiresSubscription").value(false))
                .andExpect(jsonPath("\$.requiresStripeSetup").value(false))
                .andExpect(jsonPath("\$.nextStep").value("dashboard"))
    }

    @Test
    @DisplayName("Should prevent duplicate subscription creation")
    void shouldPreventDuplicateSubscriptionCreation() throws Exception {
        // Create shop
        ShopCreationRequest shopRequest = new ShopCreationRequest()
        shopRequest.name = "Test Salon"
        shopRequest.businessTypes = [BusinessType.HAIRDRESSER] as Set
        shopRequest.address = "123 Test St"
        shopRequest.city = "Test City"
        shopRequest.state = "TS"
        shopRequest.postalCode = "12345"
        shopRequest.country = "USA"
        shopRequest.acceptsCardPayments = true

        String shopCreationResponse = mockMvc.perform(post("/api/shops")
                .header("Authorization", "Bearer " + jwtToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(shopRequest)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString()

        ShopCreationResponse shopResponse = objectMapper.readValue(shopCreationResponse, ShopCreationResponse.class)
        UUID shopId = shopResponse.shopId

        // Create first subscription
        SubscriptionRequest subscriptionRequest = new SubscriptionRequest()
        subscriptionRequest.planName = "Basic Plan"
        subscriptionRequest.amount = 2999

        mockMvc.perform(post("/api/subscriptions/shops/{shopId}", shopId)
                .header("Authorization", "Bearer " + jwtToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(subscriptionRequest)))
                .andExpect(status().isOk())

        // Try to create second subscription (should fail)
        mockMvc.perform(post("/api/subscriptions/shops/{shopId}", shopId)
                .header("Authorization", "Bearer " + jwtToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(subscriptionRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("\$.error").value("Subscription creation failed"))
                .andExpect(jsonPath("\$.message").value("Shop already has an active subscription"))
    }

    @Test
    @DisplayName("Should handle subscription cancellation")
    void shouldHandleSubscriptionCancellation() throws Exception {
        // Create shop and subscription first
        ShopCreationRequest shopRequest = new ShopCreationRequest()
        shopRequest.name = "Cancellation Test Salon"
        shopRequest.businessTypes = [BusinessType.HAIRDRESSER] as Set
        shopRequest.address = "789 Cancel St"
        shopRequest.city = "Cancel City"
        shopRequest.state = "CC"
        shopRequest.postalCode = "99999"
        shopRequest.country = "USA"
        shopRequest.acceptsCardPayments = true

        String shopCreationResponse = mockMvc.perform(post("/api/shops")
                .header("Authorization", "Bearer " + jwtToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(shopRequest)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString()

        ShopCreationResponse shopResponse = objectMapper.readValue(shopCreationResponse, ShopCreationResponse.class)
        UUID shopId = shopResponse.shopId

        // Create subscription
        SubscriptionRequest subscriptionRequest = new SubscriptionRequest()
        mockMvc.perform(post("/api/subscriptions/shops/{shopId}", shopId)
                .header("Authorization", "Bearer " + jwtToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(subscriptionRequest)))
                .andExpect(status().isOk())

        // Verify subscription is active
        Shop shopWithSubscription = shopRepository.findById(shopId).orElse(null)
        assertTrue(shopWithSubscription.subscriptionActive)

        // Cancel subscription
        mockMvc.perform(delete("/api/subscriptions/shops/{shopId}", shopId)
                .header("Authorization", "Bearer " + jwtToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("\$.message").value("Subscription cancelled successfully"))

        // Verify subscription is cancelled
        Shop shopAfterCancellation = shopRepository.findById(shopId).orElse(null)
        assertFalse(shopAfterCancellation.subscriptionActive)
        assertNull(shopAfterCancellation.subscriptionId)
    }
}
