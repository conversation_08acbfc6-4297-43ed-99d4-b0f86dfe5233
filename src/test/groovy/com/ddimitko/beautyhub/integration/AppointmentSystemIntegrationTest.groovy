package com.ddimitko.beautyhub.integration

import com.ddimitko.beautyhub.dto.*
import com.ddimitko.beautyhub.entity.*
import com.ddimitko.beautyhub.enums.*
import com.ddimitko.beautyhub.repository.*
import com.ddimitko.beautyhub.service.*
import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.MvcResult
import org.springframework.transaction.annotation.Transactional

import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

import static org.junit.jupiter.api.Assertions.*
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("Appointment System Integration Tests")
class AppointmentSystemIntegrationTest {

    @Autowired
    private MockMvc mockMvc

    @Autowired
    private ObjectMapper objectMapper

    @Autowired
    private UserRepository userRepository

    @Autowired
    private ShopRepository shopRepository

    @Autowired
    private EmployeeRepository employeeRepository

    @Autowired
    private ServiceRepository serviceRepository

    @Autowired
    private AppointmentRepository appointmentRepository

    @Autowired
    private ScheduleSlotRepository scheduleSlotRepository

    @Autowired
    private AppointmentService appointmentService

    @Autowired
    private SlotLockingService slotLockingService

    @Autowired
    private RedisTemplate<String, Object> redisTemplate

    private User testUser
    private User ownerUser
    private Shop testShop
    private Employee testEmployee
    private Service testService
    private ScheduleSlot mondaySchedule

    @BeforeEach
    void setUp() {
        // Clean up all repositories
        appointmentRepository.deleteAll()
        scheduleSlotRepository.deleteAll()
        serviceRepository.deleteAll()
        employeeRepository.deleteAll()
        shopRepository.deleteAll()
        userRepository.deleteAll()

        // Clean Redis
        redisTemplate.getConnectionFactory().getConnection().flushAll()

        // Create test user
        testUser = new User()
        testUser.email = "<EMAIL>"
        testUser.password = "hashedPassword123"
        testUser.firstName = "John"
        testUser.lastName = "Customer"
        testUser.role = UserRole.USER
        testUser.enabled = true
        testUser.emailVerified = true
        testUser = userRepository.save(testUser)

        // Create owner user
        ownerUser = new User()
        ownerUser.email = "<EMAIL>"
        ownerUser.password = "hashedPassword123"
        ownerUser.firstName = "Jane"
        ownerUser.lastName = "Owner"
        ownerUser.role = UserRole.OWNER
        ownerUser.enabled = true
        ownerUser.emailVerified = true
        ownerUser = userRepository.save(ownerUser)

        // Create employee user
        User employeeUser = new User()
        employeeUser.email = "<EMAIL>"
        employeeUser.password = "hashedPassword123"
        employeeUser.firstName = "Sarah"
        employeeUser.lastName = "Stylist"
        employeeUser.role = UserRole.EMPLOYEE
        employeeUser.enabled = true
        employeeUser.emailVerified = true
        employeeUser = userRepository.save(employeeUser)

        // Create test shop
        testShop = new Shop()
        testShop.owner = ownerUser
        testShop.name = "Elite Beauty Salon"
        testShop.description = "Premium beauty services"
        testShop.businessTypes = [BusinessType.HAIRDRESSER, BusinessType.BEAUTY_SALON] as Set
        testShop.address = "123 Beauty Street"
        testShop.city = "New York"
        testShop.state = "NY"
        testShop.postalCode = "10001"
        testShop.country = "USA"
        testShop.phone = "******-0123"
        testShop.email = "<EMAIL>"
        testShop.active = true
        testShop.acceptsCardPayments = true
        testShop.subscriptionActive = true
        testShop.stripeOnboardingCompleted = true
        testShop = shopRepository.save(testShop)

        // Create test employee
        testEmployee = new Employee()
        testEmployee.user = employeeUser
        testEmployee.shop = testShop
        testEmployee.bio = "Experienced hair stylist with 10+ years"
        testEmployee.specialties = "Hair Cutting, Hair Coloring, Styling"
        testEmployee.yearsExperience = 10
        testEmployee.hourlyRate = new BigDecimal("75.00")
        testEmployee.commissionRate = new BigDecimal("0.20")
        testEmployee.active = true
        testEmployee.hireDate = LocalDateTime.now().minusYears(3)
        testEmployee = employeeRepository.save(testEmployee)

        // Create test service
        testService = new Service()
        testService.employee = testEmployee
        testService.shop = testShop
        testService.name = "Premium Haircut & Style"
        testService.description = "Professional haircut with wash, cut, and styling"
        testService.price = new BigDecimal("75.00")
        testService.durationMinutes = 90
        testService.category = "Hair"
        testService.active = true
        testService.onlineBookingEnabled = true
        testService.requiresDeposit = true
        testService.depositAmount = new BigDecimal("25.00")
        testService = serviceRepository.save(testService)

        // Create schedule slot for Monday
        mondaySchedule = new ScheduleSlot()
        mondaySchedule.employee = testEmployee
        mondaySchedule.dayOfWeek = com.ddimitko.beautyhub.enums.DayOfWeek.MONDAY
        mondaySchedule.startTime = LocalTime.of(9, 0)
        mondaySchedule.endTime = LocalTime.of(18, 0)
        mondaySchedule.active = true
        mondaySchedule = scheduleSlotRepository.save(mondaySchedule)
    }

    @Test
    @DisplayName("Should retrieve available slots for a service")
    void shouldRetrieveAvailableSlotsForService() throws Exception {
        // Use a Monday date in the future
        LocalDate testDate = LocalDate.of(2025, 6, 16) // This is a Monday

        MvcResult result = mockMvc.perform(get("/api/appointments/available-slots")
                .param("shopId", testShop.id.toString())
                .param("employeeId", testEmployee.id.toString())
                .param("serviceId", testService.id.toString())
                .param("date", testDate.toString()))
                .andExpect(status().isOk())
                .andReturn()

        String responseContent = result.getResponse().getContentAsString()
        List<AvailableSlotResponse> slots = objectMapper.readValue(responseContent, 
                objectMapper.getTypeFactory().constructCollectionType(List.class, AvailableSlotResponse.class))

        assertFalse(slots.isEmpty(), "Should return available slots")
        
        // Verify slot properties
        AvailableSlotResponse firstSlot = slots.get(0)
        assertEquals(testEmployee.id, firstSlot.employeeId)
        assertEquals(testService.id, firstSlot.serviceId)
        assertEquals(testService.name, firstSlot.serviceName)
        assertEquals(testService.durationMinutes, firstSlot.durationMinutes)
        assertEquals(testService.price, firstSlot.price)
        assertTrue(firstSlot.available)
        assertFalse(firstSlot.locked)
    }

    @Test
    @DisplayName("Should lock a time slot successfully")
    void shouldLockTimeSlotSuccessfully() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        SlotLockRequest lockRequest = new SlotLockRequest()
        lockRequest.shopId = testShop.id
        lockRequest.serviceId = testService.id
        lockRequest.employeeId = testEmployee.id
        lockRequest.dateTime = appointmentTime

        MvcResult result = mockMvc.perform(post("/api/appointments/lock-slot")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lockRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.message').value("Slot locked successfully"))
                .andExpect(jsonPath('$.lockToken').exists())
                .andExpect(jsonPath('$.expiresIn').value(5))
                .andReturn()

        String responseContent = result.getResponse().getContentAsString()
        Map<String, Object> response = objectMapper.readValue(responseContent, Map.class)
        String lockToken = (String) response.get("lockToken")

        assertNotNull(lockToken, "Lock token should be returned")
        assertTrue(UUID.fromString(lockToken) != null, "Lock token should be a valid UUID")

        // Verify slot is locked in Redis
        assertTrue(slotLockingService.isSlotLocked(testShop.id, testService.id, testEmployee.id, appointmentTime))
    }

    @Test
    @DisplayName("Should create guest appointment with lock token as ID")
    void shouldCreateGuestAppointmentWithLockTokenAsId() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        // First, lock the slot
        SlotLockRequest lockRequest = new SlotLockRequest()
        lockRequest.shopId = testShop.id
        lockRequest.serviceId = testService.id
        lockRequest.employeeId = testEmployee.id
        lockRequest.dateTime = appointmentTime

        MvcResult lockResult = mockMvc.perform(post("/api/appointments/lock-slot")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lockRequest)))
                .andExpect(status().isOk())
                .andReturn()

        String lockResponseContent = lockResult.getResponse().getContentAsString()
        Map<String, Object> lockResponse = objectMapper.readValue(lockResponseContent, Map.class)
        String lockToken = (String) lockResponse.get("lockToken")

        // Create appointment with lock token
        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.notes = "Integration test appointment"
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567890"
        appointmentRequest.slotLockToken = lockToken

        MvcResult appointmentResult = mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.message').value("Appointment created successfully"))
                .andExpect(jsonPath('$.appointment.id').value(lockToken))
                .andReturn()

        String appointmentResponseContent = appointmentResult.getResponse().getContentAsString()
        Map<String, Object> appointmentResponse = objectMapper.readValue(appointmentResponseContent, Map.class)
        Map<String, Object> appointment = (Map<String, Object>) appointmentResponse.get("appointment")

        // Verify appointment ID matches lock token
        assertEquals(lockToken, appointment.get("id"), "Appointment ID should match lock token")
        assertEquals("<EMAIL>", appointment.get("guestEmail"))
        assertEquals("John", appointment.get("guestFirstName"))
        assertEquals("Guest", appointment.get("guestLastName"))
        assertEquals("+1234567890", appointment.get("guestPhone"))

        // Verify appointment exists in database with correct ID
        Optional<Appointment> savedAppointment = appointmentRepository.findById(UUID.fromString(lockToken))
        assertTrue(savedAppointment.isPresent(), "Appointment should be saved with lock token as ID")

        // Verify slot is no longer locked in Redis
        assertFalse(slotLockingService.isSlotLocked(testShop.id, testService.id, testEmployee.id, appointmentTime),
                "Slot should be unlocked after successful appointment creation")
    }

    @Test
    @DisplayName("Should fail to create appointment without lock token")
    void shouldFailToCreateAppointmentWithoutLockToken() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567890"
        // No slotLockToken provided

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment creation failed"))
                .andExpect(jsonPath('$.message').value("Slot lock token is required for appointment creation"))
    }

    @Test
    @DisplayName("Should fail to create appointment with invalid lock token")
    void shouldFailToCreateAppointmentWithInvalidLockToken() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567890"
        appointmentRequest.slotLockToken = UUID.randomUUID().toString() // Invalid token

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment creation failed"))
                .andExpect(jsonPath('$.message').value("Invalid or expired slot lock token"))
    }

    @Test
    @DisplayName("Should fail to create appointment with expired lock token")
    void shouldFailToCreateAppointmentWithExpiredLockToken() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        // Lock the slot
        SlotLockRequest lockRequest = new SlotLockRequest()
        lockRequest.shopId = testShop.id
        lockRequest.serviceId = testService.id
        lockRequest.employeeId = testEmployee.id
        lockRequest.dateTime = appointmentTime

        MvcResult lockResult = mockMvc.perform(post("/api/appointments/lock-slot")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lockRequest)))
                .andExpect(status().isOk())
                .andReturn()

        String lockResponseContent = lockResult.getResponse().getContentAsString()
        Map<String, Object> lockResponse = objectMapper.readValue(lockResponseContent, Map.class)
        String lockToken = (String) lockResponse.get("lockToken")

        // Manually expire the lock by deleting it from Redis
        slotLockingService.unlockSlot(testShop.id, testService.id, testEmployee.id, appointmentTime)

        // Try to create appointment with expired token
        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567890"
        appointmentRequest.slotLockToken = lockToken

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment creation failed"))
                .andExpect(jsonPath('$.message').value("Invalid or expired slot lock token"))
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["USER"])
    @DisplayName("Should create authenticated user appointment with lock token as ID")
    void shouldCreateAuthenticatedUserAppointmentWithLockTokenAsId() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 15, 30)

        // Lock the slot
        SlotLockRequest lockRequest = new SlotLockRequest()
        lockRequest.shopId = testShop.id
        lockRequest.serviceId = testService.id
        lockRequest.employeeId = testEmployee.id
        lockRequest.dateTime = appointmentTime

        MvcResult lockResult = mockMvc.perform(post("/api/appointments/lock-slot")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lockRequest)))
                .andExpect(status().isOk())
                .andReturn()

        String lockResponseContent = lockResult.getResponse().getContentAsString()
        Map<String, Object> lockResponse = objectMapper.readValue(lockResponseContent, Map.class)
        String lockToken = (String) lockResponse.get("lockToken")

        // Create appointment for authenticated user
        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CASH
        appointmentRequest.notes = "Authenticated user appointment"
        appointmentRequest.slotLockToken = lockToken

        MvcResult appointmentResult = mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.message').value("Appointment created successfully"))
                .andExpect(jsonPath('$.appointment.id').value(lockToken))
                .andReturn()

        String appointmentResponseContent = appointmentResult.getResponse().getContentAsString()
        Map<String, Object> appointmentResponse = objectMapper.readValue(appointmentResponseContent, Map.class)
        Map<String, Object> appointment = (Map<String, Object>) appointmentResponse.get("appointment")

        // Verify appointment details
        assertEquals(lockToken, appointment.get("id"))
        assertEquals("<EMAIL>", appointment.get("userEmail"))
        assertEquals("John Customer", appointment.get("userName"))
        assertEquals(false, appointment.get("guestAppointment"))

        // Verify in database
        Optional<Appointment> savedAppointment = appointmentRepository.findById(UUID.fromString(lockToken))
        assertTrue(savedAppointment.isPresent())
        assertEquals(testUser.id, savedAppointment.get().user.id)
    }

    @Test
    @DisplayName("Should prevent double booking of the same slot")
    void shouldPreventDoubleBookingOfSameSlot() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        // First user locks the slot
        SlotLockRequest lockRequest1 = new SlotLockRequest()
        lockRequest1.shopId = testShop.id
        lockRequest1.serviceId = testService.id
        lockRequest1.employeeId = testEmployee.id
        lockRequest1.dateTime = appointmentTime

        MvcResult lockResult1 = mockMvc.perform(post("/api/appointments/lock-slot")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lockRequest1)))
                .andExpect(status().isOk())
                .andReturn()

        String lockResponseContent1 = lockResult1.getResponse().getContentAsString()
        Map<String, Object> lockResponse1 = objectMapper.readValue(lockResponseContent1, Map.class)
        String lockToken1 = (String) lockResponse1.get("lockToken")

        // Second user tries to lock the same slot
        SlotLockRequest lockRequest2 = new SlotLockRequest()
        lockRequest2.shopId = testShop.id
        lockRequest2.serviceId = testService.id
        lockRequest2.employeeId = testEmployee.id
        lockRequest2.dateTime = appointmentTime

        mockMvc.perform(post("/api/appointments/lock-slot")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lockRequest2)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Slot lock failed"))
                .andExpect(jsonPath('$.message').value("Slot is already locked by another user"))

        // First user creates appointment successfully
        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567890"
        appointmentRequest.slotLockToken = lockToken1

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.appointment.id').value(lockToken1))

        // Verify appointment exists
        assertTrue(appointmentRepository.findById(UUID.fromString(lockToken1)).isPresent())
    }

    @Test
    @DisplayName("Should handle concurrent slot locking correctly")
    void shouldHandleConcurrentSlotLockingCorrectly() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 16, 0)

        // Simulate concurrent requests by using the service directly
        UUID userId1 = UUID.randomUUID()
        UUID userId2 = UUID.randomUUID()

        // First lock should succeed
        boolean lock1 = slotLockingService.lockSlot(testShop.id, testService.id, testEmployee.id, appointmentTime, userId1)
        assertTrue(lock1, "First lock should succeed")

        // Second lock should fail
        boolean lock2 = slotLockingService.lockSlot(testShop.id, testService.id, testEmployee.id, appointmentTime, userId2)
        assertFalse(lock2, "Second lock should fail")

        // Verify only first user owns the lock
        assertTrue(slotLockingService.isSlotLockedByUser(testShop.id, testService.id, testEmployee.id, appointmentTime, userId1))
        assertFalse(slotLockingService.isSlotLockedByUser(testShop.id, testService.id, testEmployee.id, appointmentTime, userId2))
    }

    @Test
    @DisplayName("Should validate appointment time is within employee schedule")
    void shouldValidateAppointmentTimeIsWithinEmployeeSchedule() throws Exception {
        // Try to book outside of employee's working hours (before 9 AM)
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 8, 0)

        SlotLockRequest lockRequest = new SlotLockRequest()
        lockRequest.shopId = testShop.id
        lockRequest.serviceId = testService.id
        lockRequest.employeeId = testEmployee.id
        lockRequest.dateTime = appointmentTime

        MvcResult lockResult = mockMvc.perform(post("/api/appointments/lock-slot")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lockRequest)))
                .andExpect(status().isOk())
                .andReturn()

        String lockResponseContent = lockResult.getResponse().getContentAsString()
        Map<String, Object> lockResponse = objectMapper.readValue(lockResponseContent, Map.class)
        String lockToken = (String) lockResponse.get("lockToken")

        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567890"
        appointmentRequest.slotLockToken = lockToken

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment creation failed"))
                .andExpect(jsonPath('$.message').value("Appointment time is outside employee's working hours"))
    }

    @Test
    @DisplayName("Should validate appointment is in the future")
    void shouldValidateAppointmentIsInFuture() throws Exception {
        // Try to book in the past
        LocalDateTime pastTime = LocalDateTime.now().minusHours(1)

        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = pastTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567890"
        appointmentRequest.slotLockToken = UUID.randomUUID().toString()

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Validation failed"))
                .andExpect(jsonPath('$.message').value("Appointment must be in the future"))
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["USER"])
    @DisplayName("Should retrieve user appointments")
    void shouldRetrieveUserAppointments() throws Exception {
        // Create a test appointment first
        createTestAppointment()

        mockMvc.perform(get("/api/appointments/my-appointments"))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$').isArray())
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["OWNER"])
    @DisplayName("Should retrieve shop appointments")
    void shouldRetrieveShopAppointments() throws Exception {
        // Create a test appointment first
        createTestAppointment()

        mockMvc.perform(get("/api/appointments/shop/" + testShop.id))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$').isArray())
    }

    @Test
    @WithMockUser(username = "<EMAIL>", roles = ["EMPLOYEE"])
    @DisplayName("Should retrieve employee appointments")
    void shouldRetrieveEmployeeAppointments() throws Exception {
        // Create a test appointment first
        createTestAppointment()

        mockMvc.perform(get("/api/appointments/employee/" + testEmployee.id))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$').isArray())
    }

    @Test
    @DisplayName("Should retrieve guest appointments by email")
    void shouldRetrieveGuestAppointmentsByEmail() throws Exception {
        // Create a guest appointment first
        createTestGuestAppointment()

        mockMvc.perform(get("/api/appointments/guest")
                .param("email", "<EMAIL>"))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$').isArray())
    }

    @Test
    @DisplayName("Should validate guest information for unauthenticated bookings")
    void shouldValidateGuestInformationForUnauthenticatedBookings() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.slotLockToken = UUID.randomUUID().toString()
        // Missing guest information

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Appointment creation failed"))
                .andExpect(jsonPath('$.message').value("Guest information is required for unauthenticated bookings"))
    }

    // Helper methods
    private void createTestAppointment() {
        Appointment appointment = new Appointment()
        appointment.user = testUser
        appointment.shop = testShop
        appointment.employee = testEmployee
        appointment.service = testService
        appointment.appointmentDateTime = LocalDateTime.of(2025, 6, 16, 14, 0)
        appointment.endDateTime = LocalDateTime.of(2025, 6, 16, 15, 30)
        appointment.paymentType = PaymentType.CASH
        appointment.totalAmount = testService.price
        appointment.status = AppointmentStatus.PENDING
        appointmentRepository.save(appointment)
    }

    private void createTestGuestAppointment() {
        Appointment appointment = new Appointment()
        appointment.shop = testShop
        appointment.employee = testEmployee
        appointment.service = testService
        appointment.appointmentDateTime = LocalDateTime.of(2025, 6, 16, 15, 0)
        appointment.endDateTime = LocalDateTime.of(2025, 6, 16, 16, 30)
        appointment.paymentType = PaymentType.CARD
        appointment.totalAmount = testService.price
        appointment.status = AppointmentStatus.PENDING
        appointment.guestEmail = "<EMAIL>"
        appointment.guestFirstName = "John"
        appointment.guestLastName = "Guest"
        appointment.guestPhone = "+1234567890"
        appointmentRepository.save(appointment)
    }
}
