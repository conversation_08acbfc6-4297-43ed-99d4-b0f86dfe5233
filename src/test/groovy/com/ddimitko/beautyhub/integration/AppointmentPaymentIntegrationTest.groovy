package com.ddimitko.beautyhub.integration

import com.ddimitko.beautyhub.dto.*
import com.ddimitko.beautyhub.entity.*
import com.ddimitko.beautyhub.enums.*
import com.ddimitko.beautyhub.repository.*
import com.ddimitko.beautyhub.service.SlotLockingService
import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.MvcResult
import org.springframework.transaction.annotation.Transactional

import java.time.DayOfWeek
import java.time.LocalDateTime
import java.time.LocalTime

import static org.junit.jupiter.api.Assertions.*
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("Appointment Payment Integration Tests")
class AppointmentPaymentIntegrationTest {

    @Autowired
    private MockMvc mockMvc

    @Autowired
    private ObjectMapper objectMapper

    @Autowired
    private UserRepository userRepository

    @Autowired
    private ShopRepository shopRepository

    @Autowired
    private EmployeeRepository employeeRepository

    @Autowired
    private ServiceRepository serviceRepository

    @Autowired
    private AppointmentRepository appointmentRepository

    @Autowired
    private ScheduleSlotRepository scheduleSlotRepository

    @Autowired
    private SlotLockingService slotLockingService

    @Autowired
    private RedisTemplate<String, Object> redisTemplate

    private User ownerUser
    private Shop testShop
    private Employee testEmployee
    private Service testService
    private ScheduleSlot mondaySchedule

    @BeforeEach
    void setUp() {
        // Clean up all repositories
        appointmentRepository.deleteAll()
        scheduleSlotRepository.deleteAll()
        serviceRepository.deleteAll()
        employeeRepository.deleteAll()
        shopRepository.deleteAll()
        userRepository.deleteAll()

        // Clean Redis
        redisTemplate.getConnectionFactory().getConnection().flushAll()

        // Create owner user
        ownerUser = new User()
        ownerUser.email = "<EMAIL>"
        ownerUser.password = "hashedPassword123"
        ownerUser.firstName = "Jane"
        ownerUser.lastName = "Owner"
        ownerUser.role = UserRole.OWNER
        ownerUser.enabled = true
        ownerUser.emailVerified = true
        ownerUser = userRepository.save(ownerUser)

        // Create employee user
        User employeeUser = new User()
        employeeUser.email = "<EMAIL>"
        employeeUser.password = "hashedPassword123"
        employeeUser.firstName = "Sarah"
        employeeUser.lastName = "Stylist"
        employeeUser.role = UserRole.EMPLOYEE
        employeeUser.enabled = true
        employeeUser.emailVerified = true
        employeeUser = userRepository.save(employeeUser)

        // Create test shop with Stripe enabled
        testShop = new Shop()
        testShop.owner = ownerUser
        testShop.name = "Elite Beauty Salon"
        testShop.description = "Premium beauty services"
        testShop.businessTypes = [BusinessType.HAIRDRESSER, BusinessType.BEAUTY_SALON] as Set
        testShop.address = "123 Beauty Street"
        testShop.city = "New York"
        testShop.state = "NY"
        testShop.postalCode = "10001"
        testShop.country = "USA"
        testShop.phone = "******-0123"
        testShop.email = "<EMAIL>"
        testShop.active = true
        testShop.acceptsCardPayments = true
        testShop.subscriptionActive = true
        testShop.stripeOnboardingCompleted = true
        testShop.stripeAccountId = "acct_test123"
        testShop = shopRepository.save(testShop)

        // Create test employee
        testEmployee = new Employee()
        testEmployee.user = employeeUser
        testEmployee.shop = testShop
        testEmployee.bio = "Experienced hair stylist with 10+ years"
        testEmployee.specialties = "Hair Cutting, Hair Coloring, Styling"
        testEmployee.yearsExperience = 10
        testEmployee.hourlyRate = new BigDecimal("75.00")
        testEmployee.commissionRate = new BigDecimal("0.20")
        testEmployee.active = true
        testEmployee.hireDate = LocalDateTime.now().minusYears(3)
        testEmployee = employeeRepository.save(testEmployee)

        // Create test service
        testService = new Service()
        testService.employee = testEmployee
        testService.shop = testShop
        testService.name = "Premium Haircut & Style"
        testService.description = "Professional haircut with wash, cut, and styling"
        testService.price = new BigDecimal("75.00")
        testService.durationMinutes = 90
        testService.category = "Hair"
        testService.active = true
        testService.onlineBookingEnabled = true
        testService.requiresDeposit = true
        testService.depositAmount = new BigDecimal("25.00")
        testService = serviceRepository.save(testService)

        // Create schedule slot for Monday
        mondaySchedule = new ScheduleSlot()
        mondaySchedule.employee = testEmployee
        mondaySchedule.dayOfWeek = com.ddimitko.beautyhub.enums.DayOfWeek.MONDAY
        mondaySchedule.startTime = LocalTime.of(9, 0)
        mondaySchedule.endTime = LocalTime.of(18, 0)
        mondaySchedule.active = true
        mondaySchedule = scheduleSlotRepository.save(mondaySchedule)
    }

    @Test
    @DisplayName("Should create payment intent for appointment")
    void shouldCreatePaymentIntentForAppointment() throws Exception {
        PaymentIntentRequest paymentRequest = new PaymentIntentRequest()
        paymentRequest.shopId = testShop.id
        paymentRequest.serviceId = testService.id
        paymentRequest.amount = testService.price
        paymentRequest.currency = "usd"
        paymentRequest.description = "Payment for ${testService.name}"

        mockMvc.perform(post("/api/payments/create-payment-intent")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(paymentRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.clientSecret').exists())
                .andExpect(jsonPath('$.amount').value(7500)) // Amount in cents
                .andExpect(jsonPath('$.currency').value("usd"))
    }

    @Test
    @DisplayName("Should create appointment with card payment")
    void shouldCreateAppointmentWithCardPayment() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 14, 0)

        // First, lock the slot
        SlotLockRequest lockRequest = new SlotLockRequest()
        lockRequest.shopId = testShop.id
        lockRequest.serviceId = testService.id
        lockRequest.employeeId = testEmployee.id
        lockRequest.dateTime = appointmentTime

        MvcResult lockResult = mockMvc.perform(post("/api/appointments/lock-slot")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lockRequest)))
                .andExpect(status().isOk())
                .andReturn()

        String lockResponseContent = lockResult.getResponse().getContentAsString()
        Map<String, Object> lockResponse = objectMapper.readValue(lockResponseContent, Map.class)
        String lockToken = (String) lockResponse.get("lockToken")

        // Create appointment with card payment
        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.notes = "Card payment appointment"
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "John"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567890"
        appointmentRequest.slotLockToken = lockToken
        appointmentRequest.paymentMethodId = "pm_test_card"

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.message').value("Appointment created successfully"))
                .andExpect(jsonPath('$.appointment.paymentType').value("CARD"))
                .andExpect(jsonPath('$.appointment.totalAmount').value(75.00))
                .andExpect(jsonPath('$.appointment.depositAmount').value(25.00))

        // Verify appointment exists in database
        Optional<Appointment> savedAppointment = appointmentRepository.findById(UUID.fromString(lockToken))
        assertTrue(savedAppointment.isPresent())
        assertEquals(PaymentType.CARD, savedAppointment.get().paymentType)
    }

    @Test
    @DisplayName("Should create appointment with cash payment")
    void shouldCreateAppointmentWithCashPayment() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 15, 0)

        // First, lock the slot
        SlotLockRequest lockRequest = new SlotLockRequest()
        lockRequest.shopId = testShop.id
        lockRequest.serviceId = testService.id
        lockRequest.employeeId = testEmployee.id
        lockRequest.dateTime = appointmentTime

        MvcResult lockResult = mockMvc.perform(post("/api/appointments/lock-slot")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lockRequest)))
                .andExpect(status().isOk())
                .andReturn()

        String lockResponseContent = lockResult.getResponse().getContentAsString()
        Map<String, Object> lockResponse = objectMapper.readValue(lockResponseContent, Map.class)
        String lockToken = (String) lockResponse.get("lockToken")

        // Create appointment with cash payment
        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CASH
        appointmentRequest.notes = "Cash payment appointment"
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "Jane"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567891"
        appointmentRequest.slotLockToken = lockToken

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.message').value("Appointment created successfully"))
                .andExpect(jsonPath('$.appointment.paymentType').value("CASH"))
                .andExpect(jsonPath('$.appointment.totalAmount').value(75.00))

        // Verify appointment exists in database
        Optional<Appointment> savedAppointment = appointmentRepository.findById(UUID.fromString(lockToken))
        assertTrue(savedAppointment.isPresent())
        assertEquals(PaymentType.CASH, savedAppointment.get().paymentType)
    }

    @Test
    @DisplayName("Should fail to create card payment for shop without Stripe")
    void shouldFailToCreateCardPaymentForShopWithoutStripe() throws Exception {
        // Disable Stripe for the shop
        testShop.acceptsCardPayments = false
        testShop.stripeOnboardingCompleted = false
        shopRepository.save(testShop)

        PaymentIntentRequest paymentRequest = new PaymentIntentRequest()
        paymentRequest.shopId = testShop.id
        paymentRequest.serviceId = testService.id
        paymentRequest.amount = testService.price
        paymentRequest.currency = "usd"

        mockMvc.perform(post("/api/payments/create-payment-intent")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(paymentRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath('$.error').value("Payment failed"))
                .andExpect(jsonPath('$.message').value("Shop does not accept card payments"))
    }

    @Test
    @DisplayName("Should handle deposit amount correctly")
    void shouldHandleDepositAmountCorrectly() throws Exception {
        LocalDateTime appointmentTime = LocalDateTime.of(2025, 6, 16, 16, 0)

        // Lock slot and create appointment with custom deposit
        SlotLockRequest lockRequest = new SlotLockRequest()
        lockRequest.shopId = testShop.id
        lockRequest.serviceId = testService.id
        lockRequest.employeeId = testEmployee.id
        lockRequest.dateTime = appointmentTime

        MvcResult lockResult = mockMvc.perform(post("/api/appointments/lock-slot")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lockRequest)))
                .andExpect(status().isOk())
                .andReturn()

        String lockToken = objectMapper.readValue(
                lockResult.getResponse().getContentAsString(), Map.class
        ).get("lockToken") as String

        AppointmentCreationRequest appointmentRequest = new AppointmentCreationRequest()
        appointmentRequest.shopId = testShop.id
        appointmentRequest.employeeId = testEmployee.id
        appointmentRequest.serviceId = testService.id
        appointmentRequest.appointmentDateTime = appointmentTime
        appointmentRequest.paymentType = PaymentType.CARD
        appointmentRequest.guestEmail = "<EMAIL>"
        appointmentRequest.guestFirstName = "Bob"
        appointmentRequest.guestLastName = "Guest"
        appointmentRequest.guestPhone = "+1234567892"
        appointmentRequest.slotLockToken = lockToken
        appointmentRequest.depositAmount = new BigDecimal("30.00") // Custom deposit

        mockMvc.perform(post("/api/appointments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(appointmentRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.appointment.depositAmount').value(30.00))
                .andExpect(jsonPath('$.appointment.totalAmount').value(75.00))

        // Verify in database
        Optional<Appointment> savedAppointment = appointmentRepository.findById(UUID.fromString(lockToken))
        assertTrue(savedAppointment.isPresent())
        assertEquals(new BigDecimal("30.00"), savedAppointment.get().depositAmount)
    }

    @Test
    @DisplayName("Should confirm payment for appointment")
    void shouldConfirmPaymentForAppointment() throws Exception {
        // Create an appointment with card payment first
        Appointment appointment = createTestAppointmentWithCardPayment()

        Map<String, Object> confirmRequest = [
            paymentIntentId: "pi_test_123",
            appointmentId: appointment.id.toString()
        ]

        mockMvc.perform(post("/api/payments/confirm-payment")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(confirmRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$.message').value("Payment confirmed successfully"))
                .andExpect(jsonPath('$.paymentStatus').value("succeeded"))

        // Verify payment information is updated
        Optional<Appointment> updated = appointmentRepository.findById(appointment.id)
        assertTrue(updated.isPresent())
        assertEquals("pi_test_123", updated.get().paymentIntentId)
        assertEquals("succeeded", updated.get().paymentStatus)
    }

    private Appointment createTestAppointmentWithCardPayment() {
        Appointment appointment = new Appointment()
        appointment.shop = testShop
        appointment.employee = testEmployee
        appointment.service = testService
        appointment.appointmentDateTime = LocalDateTime.now().plusDays(3)
        appointment.endDateTime = LocalDateTime.now().plusDays(3).plusMinutes(90)
        appointment.paymentType = PaymentType.CARD
        appointment.totalAmount = testService.price
        appointment.depositAmount = testService.depositAmount
        appointment.status = AppointmentStatus.PENDING
        appointment.guestEmail = "<EMAIL>"
        appointment.guestFirstName = "Payment"
        appointment.guestLastName = "Test"
        appointment.guestPhone = "+1234567893"
        return appointmentRepository.save(appointment)
    }
}
