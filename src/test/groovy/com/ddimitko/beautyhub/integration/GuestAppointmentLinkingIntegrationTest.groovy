package com.ddimitko.beautyhub.integration

import com.ddimitko.beautyhub.dto.*
import com.ddimitko.beautyhub.entity.*
import com.ddimitko.beautyhub.enums.*
import com.ddimitko.beautyhub.repository.*
import com.ddimitko.beautyhub.service.UserService
import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.transaction.annotation.Transactional

import java.time.LocalDateTime

import static org.junit.jupiter.api.Assertions.*
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("Guest Appointment Linking Integration Tests")
class GuestAppointmentLinkingIntegrationTest {

    @Autowired
    private MockMvc mockMvc

    @Autowired
    private ObjectMapper objectMapper

    @Autowired
    private UserRepository userRepository

    @Autowired
    private ShopRepository shopRepository

    @Autowired
    private EmployeeRepository employeeRepository

    @Autowired
    private ServiceRepository serviceRepository

    @Autowired
    private AppointmentRepository appointmentRepository

    @Autowired
    private UserService userService

    private User ownerUser
    private Shop testShop
    private Employee testEmployee
    private Service testService
    private List<Appointment> guestAppointments

    @BeforeEach
    void setUp() {
        // Clean up all repositories
        appointmentRepository.deleteAll()
        serviceRepository.deleteAll()
        employeeRepository.deleteAll()
        shopRepository.deleteAll()
        userRepository.deleteAll()

        // Create owner user
        ownerUser = new User()
        ownerUser.email = "<EMAIL>"
        ownerUser.password = "hashedPassword123"
        ownerUser.firstName = "Jane"
        ownerUser.lastName = "Owner"
        ownerUser.role = UserRole.OWNER
        ownerUser.enabled = true
        ownerUser.emailVerified = true
        ownerUser = userRepository.save(ownerUser)

        // Create employee user
        User employeeUser = new User()
        employeeUser.email = "<EMAIL>"
        employeeUser.password = "hashedPassword123"
        employeeUser.firstName = "Sarah"
        employeeUser.lastName = "Stylist"
        employeeUser.role = UserRole.EMPLOYEE
        employeeUser.enabled = true
        employeeUser.emailVerified = true
        employeeUser = userRepository.save(employeeUser)

        // Create test shop
        testShop = new Shop()
        testShop.owner = ownerUser
        testShop.name = "Elite Beauty Salon"
        testShop.description = "Premium beauty services"
        testShop.businessTypes = [BusinessType.HAIRDRESSER, BusinessType.BEAUTY_SALON] as Set
        testShop.address = "123 Beauty Street"
        testShop.city = "New York"
        testShop.state = "NY"
        testShop.postalCode = "10001"
        testShop.country = "USA"
        testShop.phone = "******-0123"
        testShop.email = "<EMAIL>"
        testShop.active = true
        testShop.acceptsCardPayments = true
        testShop.subscriptionActive = true
        testShop.stripeOnboardingCompleted = true
        testShop = shopRepository.save(testShop)

        // Create test employee
        testEmployee = new Employee()
        testEmployee.user = employeeUser
        testEmployee.shop = testShop
        testEmployee.bio = "Experienced hair stylist with 10+ years"
        testEmployee.specialties = "Hair Cutting, Hair Coloring, Styling"
        testEmployee.yearsExperience = 10
        testEmployee.hourlyRate = new BigDecimal("75.00")
        testEmployee.commissionRate = new BigDecimal("0.20")
        testEmployee.active = true
        testEmployee.hireDate = LocalDateTime.now().minusYears(3)
        testEmployee = employeeRepository.save(testEmployee)

        // Create test service
        testService = new Service()
        testService.employee = testEmployee
        testService.shop = testShop
        testService.name = "Premium Haircut & Style"
        testService.description = "Professional haircut with wash, cut, and styling"
        testService.price = new BigDecimal("75.00")
        testService.durationMinutes = 90
        testService.category = "Hair"
        testService.active = true
        testService.onlineBookingEnabled = true
        testService.requiresDeposit = true
        testService.depositAmount = new BigDecimal("25.00")
        testService = serviceRepository.save(testService)

        // Create guest appointments
        createGuestAppointments()
    }

    @Test
    @DisplayName("Should link guest appointments when user registers with same email")
    void shouldLinkGuestAppointmentsWhenUserRegistersWithSameEmail() throws Exception {
        String guestEmail = "<EMAIL>"

        // Verify guest appointments exist
        List<Appointment> beforeLinking = appointmentRepository.findByGuestEmailAndUserIsNull(guestEmail)
        assertEquals(2, beforeLinking.size())
        assertTrue(beforeLinking.all { it.user == null })

        // Register user with same email as guest appointments
        RegisterRequest registrationRequest = new RegisterRequest()
        registrationRequest.email = guestEmail
        registrationRequest.password = "SecurePass123"
        registrationRequest.firstName = "John"
        registrationRequest.lastName = "Guest"

        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registrationRequest)))
                .andExpect(status().isOk())

        // Find the newly created user
        Optional<User> newUser = userRepository.findByEmail(guestEmail)
        assertTrue(newUser.isPresent())

        // Simulate email verification (which triggers appointment linking)
        User user = newUser.get()
        user.emailVerified = true
        userRepository.save(user)

        // Manually trigger appointment linking (in real app this would be done by email verification service)
        userService.linkGuestAppointments(user)

        // Verify appointments are now linked to the user
        List<Appointment> afterLinking = appointmentRepository.findByUser(user)
        assertEquals(2, afterLinking.size())
        assertTrue(afterLinking.all { it.user.id == user.id })

        // Verify guest appointments are no longer orphaned
        List<Appointment> orphanedAppointments = appointmentRepository.findByGuestEmailAndUserIsNull(guestEmail)
        assertEquals(0, orphanedAppointments.size())
    }

    @Test
    @DisplayName("Should retrieve guest appointments by email before linking")
    void shouldRetrieveGuestAppointmentsByEmailBeforeLinking() throws Exception {
        String guestEmail = "<EMAIL>"

        mockMvc.perform(get("/api/appointments/guest")
                .param("email", guestEmail))
                .andExpect(status().isOk())
                .andExpect(jsonPath('$').isArray())
                .andExpect(jsonPath('$.length()').value(2))
                .andExpect(jsonPath('$[0].guestEmail').value(guestEmail))
                .andExpect(jsonPath('$[0].guestFirstName').value("John"))
                .andExpect(jsonPath('$[0].guestLastName').value("Guest"))
                .andExpect(jsonPath('$[0].guestAppointment').value(true))
    }

    @Test
    @DisplayName("Should not link appointments with different email")
    void shouldNotLinkAppointmentsWithDifferentEmail() throws Exception {
        String guestEmail = "<EMAIL>"
        String differentEmail = "<EMAIL>"

        // Register user with different email
        RegisterRequest registrationRequest = new RegisterRequest()
        registrationRequest.email = differentEmail
        registrationRequest.password = "SecurePass123"
        registrationRequest.firstName = "Jane"
        registrationRequest.lastName = "Different"

        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registrationRequest)))
                .andExpect(status().isOk())

        // Find the newly created user
        Optional<User> newUser = userRepository.findByEmail(differentEmail)
        assertTrue(newUser.isPresent())

        User user = newUser.get()
        user.emailVerified = true
        userRepository.save(user)

        // Manually trigger appointment linking
        userService.linkGuestAppointments(user)

        // Verify no appointments are linked to this user
        List<Appointment> linkedAppointments = appointmentRepository.findByUser(user)
        assertEquals(0, linkedAppointments.size())

        // Verify guest appointments still exist as orphaned
        List<Appointment> guestAppointments = appointmentRepository.findByGuestEmailAndUserIsNull(guestEmail)
        assertEquals(2, guestAppointments.size())
    }

    @Test
    @DisplayName("Should preserve guest appointment details after linking")
    void shouldPreserveGuestAppointmentDetailsAfterLinking() throws Exception {
        String guestEmail = "<EMAIL>"

        // Get original appointment details
        List<Appointment> originalAppointments = appointmentRepository.findByGuestEmailAndUserIsNull(guestEmail)
        Appointment originalAppointment = originalAppointments.get(0)

        // Register and verify user
        RegisterRequest registrationRequest = new RegisterRequest()
        registrationRequest.email = guestEmail
        registrationRequest.password = "SecurePass123"
        registrationRequest.firstName = "John"
        registrationRequest.lastName = "Guest"

        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registrationRequest)))
                .andExpect(status().isOk())

        User user = userRepository.findByEmail(guestEmail).get()
        user.emailVerified = true
        userRepository.save(user)

        // Link appointments
        userService.linkGuestAppointments(user)

        // Verify appointment details are preserved
        Optional<Appointment> linkedAppointment = appointmentRepository.findById(originalAppointment.id)
        assertTrue(linkedAppointment.isPresent())

        Appointment appointment = linkedAppointment.get()
        assertEquals(user.id, appointment.user.id)
        assertEquals(originalAppointment.guestEmail, appointment.guestEmail)
        assertEquals(originalAppointment.guestFirstName, appointment.guestFirstName)
        assertEquals(originalAppointment.guestLastName, appointment.guestLastName)
        assertEquals(originalAppointment.guestPhone, appointment.guestPhone)
        assertEquals(originalAppointment.appointmentDateTime, appointment.appointmentDateTime)
        assertEquals(originalAppointment.status, appointment.status)
        assertEquals(originalAppointment.totalAmount, appointment.totalAmount)
    }

    @Test
    @DisplayName("Should handle multiple guest appointments with same email")
    void shouldHandleMultipleGuestAppointmentsWithSameEmail() throws Exception {
        String guestEmail = "<EMAIL>"

        // Create additional guest appointment
        Appointment additionalAppointment = new Appointment()
        additionalAppointment.shop = testShop
        additionalAppointment.employee = testEmployee
        additionalAppointment.service = testService
        additionalAppointment.appointmentDateTime = LocalDateTime.now().plusDays(10)
        additionalAppointment.endDateTime = LocalDateTime.now().plusDays(10).plusMinutes(90)
        additionalAppointment.paymentType = PaymentType.CASH
        additionalAppointment.totalAmount = testService.price
        additionalAppointment.status = AppointmentStatus.CONFIRMED
        additionalAppointment.guestEmail = guestEmail
        additionalAppointment.guestFirstName = "John"
        additionalAppointment.guestLastName = "Guest"
        additionalAppointment.guestPhone = "+1234567890"
        appointmentRepository.save(additionalAppointment)

        // Verify 3 guest appointments exist
        List<Appointment> beforeLinking = appointmentRepository.findByGuestEmailAndUserIsNull(guestEmail)
        assertEquals(3, beforeLinking.size())

        // Register and verify user
        RegisterRequest registrationRequest = new RegisterRequest()
        registrationRequest.email = guestEmail
        registrationRequest.password = "SecurePass123"
        registrationRequest.firstName = "John"
        registrationRequest.lastName = "Guest"

        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registrationRequest)))
                .andExpect(status().isOk())

        User user = userRepository.findByEmail(guestEmail).get()
        user.emailVerified = true
        userRepository.save(user)

        // Link appointments
        userService.linkGuestAppointments(user)

        // Verify all 3 appointments are linked
        List<Appointment> afterLinking = appointmentRepository.findByUser(user)
        assertEquals(3, afterLinking.size())
        assertTrue(afterLinking.all { it.user.id == user.id })
    }

    private void createGuestAppointments() {
        guestAppointments = []

        // Create first guest appointment
        Appointment appointment1 = new Appointment()
        appointment1.shop = testShop
        appointment1.employee = testEmployee
        appointment1.service = testService
        appointment1.appointmentDateTime = LocalDateTime.now().plusDays(5)
        appointment1.endDateTime = LocalDateTime.now().plusDays(5).plusMinutes(90)
        appointment1.paymentType = PaymentType.CARD
        appointment1.totalAmount = testService.price
        appointment1.status = AppointmentStatus.PENDING
        appointment1.guestEmail = "<EMAIL>"
        appointment1.guestFirstName = "John"
        appointment1.guestLastName = "Guest"
        appointment1.guestPhone = "+1234567890"
        guestAppointments.add(appointmentRepository.save(appointment1))

        // Create second guest appointment
        Appointment appointment2 = new Appointment()
        appointment2.shop = testShop
        appointment2.employee = testEmployee
        appointment2.service = testService
        appointment2.appointmentDateTime = LocalDateTime.now().plusDays(7)
        appointment2.endDateTime = LocalDateTime.now().plusDays(7).plusMinutes(90)
        appointment2.paymentType = PaymentType.CASH
        appointment2.totalAmount = testService.price
        appointment2.status = AppointmentStatus.CONFIRMED
        appointment2.guestEmail = "<EMAIL>"
        appointment2.guestFirstName = "John"
        appointment2.guestLastName = "Guest"
        appointment2.guestPhone = "+1234567890"
        guestAppointments.add(appointmentRepository.save(appointment2))
    }
}
