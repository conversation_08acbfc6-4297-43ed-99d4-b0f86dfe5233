package com.ddimitko.beautyhub.integration

import com.ddimitko.beautyhub.config.TestConfig

import com.ddimitko.beautyhub.dto.EmployeeInvitationRequest
import com.ddimitko.beautyhub.dto.ShopCreationRequest
import com.ddimitko.beautyhub.dto.ShopCreationResponse
import com.ddimitko.beautyhub.dto.SubscriptionRequest
import com.ddimitko.beautyhub.dto.SubscriptionResponse
import com.ddimitko.beautyhub.entity.Employee
import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.enums.BusinessType
import com.ddimitko.beautyhub.enums.UserRole
import com.ddimitko.beautyhub.repository.EmployeeRepository
import com.ddimitko.beautyhub.repository.ShopRepository
import com.ddimitko.beautyhub.repository.UserRepository
import com.ddimitko.beautyhub.service.EmployeeService
import com.ddimitko.beautyhub.service.ShopService
import com.ddimitko.beautyhub.service.StripeService
import com.ddimitko.beautyhub.service.UserService
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.springframework.transaction.annotation.Transactional

import static org.junit.jupiter.api.Assertions.*

@SpringBootTest
@ActiveProfiles("test")
@Transactional
@DisplayName("Unified Account System Integration Tests")
class UnifiedAccountSystemIntegrationTest {

    @Autowired
    private UserService userService

    @Autowired
    private ShopService shopService

    @Autowired
    private EmployeeService employeeService

    @Autowired
    private StripeService stripeService

    @Autowired
    private UserRepository userRepository

    @Autowired
    private ShopRepository shopRepository

    @Autowired
    private EmployeeRepository employeeRepository

    @BeforeEach
    void setUp() {
        // Clean up database
        employeeRepository.deleteAll()
        shopRepository.deleteAll()
        userRepository.deleteAll()
    }

    @Test
    @DisplayName("Should demonstrate complete unified account system flow")
    void shouldDemonstrateCompleteUnifiedAccountSystemFlow() {
        // Step 1: Create regular user
        User regularUser = userService.createUser(
            "<EMAIL>",
            "Password123",
            "John",
            "Doe",
            UserRole.USER
        )
        assertEquals(UserRole.USER, regularUser.role)
        assertFalse(regularUser.isOwner())

        // Step 2: User creates a shop (should upgrade to OWNER)
        ShopCreationRequest shopRequest = new ShopCreationRequest()
        shopRequest.name = "John's Beauty Salon"
        shopRequest.description = "A premium beauty salon"
        shopRequest.businessTypes = [BusinessType.HAIRDRESSER, BusinessType.BEAUTY_SALON] as Set
        shopRequest.address = "123 Beauty Street"
        shopRequest.city = "New York"
        shopRequest.state = "NY"
        shopRequest.postalCode = "10001"
        shopRequest.country = "USA"
        shopRequest.phone = "******-BEAUTY"
        shopRequest.email = "<EMAIL>"
        shopRequest.acceptsCardPayments = true

        ShopCreationResponse shopResponse = shopService.createShopWithResponse(regularUser.id, shopRequest)
        assertNotNull(shopResponse)
        assertEquals("Shop created successfully!", shopResponse.message)
        assertTrue(shopResponse.requiresSubscription)
        assertEquals("subscription_setup", shopResponse.nextStep)

        // Verify user was upgraded to OWNER
        User upgradedUser = userService.findById(regularUser.id)
        assertEquals(UserRole.OWNER, upgradedUser.role)
        assertTrue(upgradedUser.isOwner())

        // Step 3: Complete subscription setup
        SubscriptionRequest subscriptionRequest = new SubscriptionRequest()
        subscriptionRequest.planName = "Premium Plan"
        subscriptionRequest.amount = 4999
        subscriptionRequest.billingEmail = "<EMAIL>"

        SubscriptionResponse subscriptionResponse = stripeService.createSubscription(
            shopResponse.shopId, subscriptionRequest)
        assertNotNull(subscriptionResponse)
        assertEquals("active", subscriptionResponse.status)
        assertTrue(subscriptionResponse.subscriptionId.startsWith("sub_fake_"))

        // Verify shop has active subscription
        Shop createdShop = shopService.findById(shopResponse.shopId)
        assertTrue(createdShop.subscriptionActive)
        assertTrue(createdShop.canAcceptPayments())

        // Step 4: Create another user to be an employee
        User employeeUser = userService.createUser(
            "<EMAIL>",
            "Password123",
            "Jane",
            "Smith",
            UserRole.USER
        )
        assertEquals(UserRole.USER, employeeUser.role)

        // Step 5: Owner invites employee
        EmployeeInvitationRequest employeeRequest = new EmployeeInvitationRequest()
        employeeRequest.email = "<EMAIL>"
        employeeRequest.bio = "Experienced hairdresser"
        employeeRequest.specialties = "Hair cutting, coloring"
        employeeRequest.yearsExperience = 5
        employeeRequest.hourlyRate = new BigDecimal("25.00")

        Employee employee = employeeService.inviteUserAsEmployee(
            shopResponse.shopId, employeeUser.email, employeeRequest)
        assertNotNull(employee)
        assertEquals(employeeUser.id, employee.user.id)
        assertEquals(createdShop.id, employee.shop.id)

        // Verify employee user was upgraded to EMPLOYEE role
        User upgradedEmployeeUser = userService.findById(employeeUser.id)
        assertEquals(UserRole.EMPLOYEE, upgradedEmployeeUser.role)

        // Step 6: Verify employee cannot book at their own shop
        assertThrows(IllegalArgumentException.class, () -> {
            employeeService.validateEmployeeCannotBookAtOwnShop(employeeUser.id, createdShop.id)
        })

        // Step 7: Create third user who can book at the shop
        User customerUser = userService.createUser(
            "<EMAIL>",
            "Password123",
            "Bob",
            "Johnson",
            UserRole.USER
        )

        // Customer should be able to book at the shop (no exception thrown)
        try {
            employeeService.validateEmployeeCannotBookAtOwnShop(customerUser.id, createdShop.id)
            // If we reach here, no exception was thrown, which is what we want
            assertTrue(true)
        } catch (Exception e) {
            fail("Customer should be able to book at shop: ${e.getMessage()}")
        }

        // Step 8: Verify dual functionality - employee can still be a customer at other shops
        // Create another shop owner
        User anotherOwner = userService.createUser(
            "<EMAIL>",
            "Password123",
            "Alice",
            "Brown",
            UserRole.USER
        )

        ShopCreationRequest anotherShopRequest = new ShopCreationRequest()
        anotherShopRequest.name = "Alice's Spa"
        anotherShopRequest.businessTypes = [BusinessType.SPA] as Set
        anotherShopRequest.address = "456 Spa Avenue"
        anotherShopRequest.city = "Los Angeles"
        anotherShopRequest.state = "CA"
        anotherShopRequest.postalCode = "90210"
        anotherShopRequest.country = "USA"
        anotherShopRequest.phone = "+1234567890"
        anotherShopRequest.acceptsCardPayments = false

        ShopCreationResponse anotherShopResponse = shopService.createShopWithResponse(
            anotherOwner.id, anotherShopRequest)
        
        // Employee from first shop should be able to book at second shop
        try {
            employeeService.validateEmployeeCannotBookAtOwnShop(employeeUser.id, anotherShopResponse.shopId)
            // If we reach here, no exception was thrown, which is what we want
            assertTrue(true)
        } catch (Exception e) {
            fail("Employee should be able to book at other shops: ${e.getMessage()}")
        }

        // Step 9: Verify system state
        // Check total users
        List<User> allUsers = userRepository.findAll()
        assertEquals(4, allUsers.size())

        // Check user roles
        long ownerCount = allUsers.count { it.role == UserRole.OWNER }
        long employeeCount = allUsers.count { it.role == UserRole.EMPLOYEE }
        long userCount = allUsers.count { it.role == UserRole.USER }
        assertEquals(2, ownerCount) // Two shop owners
        assertEquals(1, employeeCount) // One employee
        assertEquals(1, userCount) // One regular customer

        // Check shops
        List<Shop> allShops = shopRepository.findAll()
        assertEquals(2, allShops.size())

        // Check employees
        List<Employee> allEmployees = employeeRepository.findAll()
        assertEquals(1, allEmployees.size())

        // Verify subscription
        assertTrue(stripeService.validateSubscription(shopResponse.shopId))
        assertFalse(stripeService.validateSubscription(anotherShopResponse.shopId)) // No subscription needed

        println("✅ Unified Account System Integration Test Completed Successfully!")
        println("📊 Final State:")
        println("   - Users: ${allUsers.size()} (${ownerCount} owners, ${employeeCount} employees, ${userCount} customers)")
        println("   - Shops: ${allShops.size()} (1 with subscription, 1 cash-only)")
        println("   - Employees: ${allEmployees.size()}")
        println("   - Dual functionality verified: employees can book at other shops")
    }

    @Test
    @DisplayName("Should prevent multiple shops per owner")
    void shouldPreventMultipleShopsPerOwner() {
        // Create user and first shop
        User user = userService.createUser("<EMAIL>", "Password123", "John", "Doe", UserRole.USER)
        
        ShopCreationRequest firstShopRequest = new ShopCreationRequest()
        firstShopRequest.name = "First Shop"
        firstShopRequest.businessTypes = [BusinessType.HAIRDRESSER] as Set
        firstShopRequest.address = "123 First St"
        firstShopRequest.city = "City"
        firstShopRequest.state = "ST"
        firstShopRequest.postalCode = "12345"
        firstShopRequest.country = "USA"
        firstShopRequest.phone = "+1234567890"

        shopService.createShopWithResponse(user.id, firstShopRequest)

        // Try to create second shop (should fail)
        ShopCreationRequest secondShopRequest = new ShopCreationRequest()
        secondShopRequest.name = "Second Shop"
        secondShopRequest.businessTypes = [BusinessType.BEAUTY_SALON] as Set
        secondShopRequest.address = "456 Second St"
        secondShopRequest.city = "City"
        secondShopRequest.state = "ST"
        secondShopRequest.postalCode = "12345"
        secondShopRequest.country = "USA"
        secondShopRequest.phone = "+1234567890"

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            shopService.createShopWithResponse(user.id, secondShopRequest)
        })
        assertEquals("You already have a shop. Multiple shops per owner not currently supported.", exception.getMessage())
    }

    @Test
    @DisplayName("Should handle employee working at multiple shops")
    void shouldHandleEmployeeWorkingAtMultipleShops() {
        // Create two shop owners
        User owner1 = userService.createUser("<EMAIL>", "Password123", "Owner", "One", UserRole.USER)
        User owner2 = userService.createUser("<EMAIL>", "Password123", "Owner", "Two", UserRole.USER)

        // Create employee user
        User employeeUser = userService.createUser("<EMAIL>", "Password123", "Employee", "User", UserRole.USER)

        // Create two shops
        ShopCreationRequest shop1Request = new ShopCreationRequest()
        shop1Request.name = "Shop One"
        shop1Request.businessTypes = [BusinessType.HAIRDRESSER] as Set
        shop1Request.address = "123 Shop One St"
        shop1Request.city = "City"
        shop1Request.state = "ST"
        shop1Request.postalCode = "12345"
        shop1Request.country = "USA"
        shop1Request.phone = "+1234567890"

        ShopCreationResponse shop1Response = shopService.createShopWithResponse(owner1.id, shop1Request)

        ShopCreationRequest shop2Request = new ShopCreationRequest()
        shop2Request.name = "Shop Two"
        shop2Request.businessTypes = [BusinessType.BEAUTY_SALON] as Set
        shop2Request.address = "456 Shop Two St"
        shop2Request.city = "City"
        shop2Request.state = "ST"
        shop2Request.postalCode = "12345"
        shop2Request.country = "USA"
        shop2Request.phone = "+1234567890"

        ShopCreationResponse shop2Response = shopService.createShopWithResponse(owner2.id, shop2Request)

        // Add employee to first shop
        EmployeeInvitationRequest employeeRequest1 = new EmployeeInvitationRequest()
        employeeRequest1.email = "<EMAIL>"
        employeeRequest1.bio = "Experienced stylist"
        employeeRequest1.hourlyRate = new BigDecimal("20.00")

        Employee employee1 = employeeService.inviteUserAsEmployee(
            shop1Response.shopId, employeeUser.email, employeeRequest1)

        // Add same employee to second shop
        EmployeeInvitationRequest employeeRequest2 = new EmployeeInvitationRequest()
        employeeRequest2.email = "<EMAIL>"
        employeeRequest2.bio = "Multi-talented stylist"
        employeeRequest2.hourlyRate = new BigDecimal("25.00")

        Employee employee2 = employeeService.inviteUserAsEmployee(
            shop2Response.shopId, employeeUser.email, employeeRequest2)

        // Verify employee works at both shops
        assertTrue(employeeService.isEmployeeAtShop(employeeUser.id, shop1Response.shopId))
        assertTrue(employeeService.isEmployeeAtShop(employeeUser.id, shop2Response.shopId))

        // Verify employee cannot book at either shop they work at
        assertThrows(IllegalArgumentException.class, () -> {
            employeeService.validateEmployeeCannotBookAtOwnShop(employeeUser.id, shop1Response.shopId)
        })
        assertThrows(IllegalArgumentException.class, () -> {
            employeeService.validateEmployeeCannotBookAtOwnShop(employeeUser.id, shop2Response.shopId)
        })

        // Verify employee shops
        List<Shop> employeeShops = employeeService.getShopsWhereUserIsEmployee(employeeUser.id)
        assertEquals(2, employeeShops.size())

        // Verify total employee records
        List<Employee> allEmployees = employeeRepository.findAll()
        assertEquals(2, allEmployees.size()) // Same user, but 2 employee records for different shops
    }
}
