package com.ddimitko.beautyhub

import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.enums.UserRole
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName

import static org.junit.jupiter.api.Assertions.*

@DisplayName("Simple Unit Tests")
class SimpleTest {

    @Test
    @DisplayName("Should create user entity with correct properties")
    void shouldCreateUserEntityWithCorrectProperties() {
        // Arrange & Act
        User user = new User()
        user.email = "<EMAIL>"
        user.password = "password123"
        user.firstName = "John"
        user.lastName = "Doe"
        user.role = UserRole.USER

        // Assert
        assertEquals("<EMAIL>", user.email)
        assertEquals("password123", user.password)
        assertEquals("John", user.firstName)
        assertEquals("Doe", user.lastName)
        assertEquals(UserRole.USER, user.role)
        assertEquals("<PERSON>", user.getFullName())
        assertTrue(user.isUser())
        assertFalse(user.isOwner())
        assertFalse(user.isEmployee())
    }

    @Test
    @DisplayName("Should handle user role checks correctly")
    void shouldHandleUserRoleChecksCorrectly() {
        User user = new User()
        
        // Test USER role
        user.role = UserRole.USER
        assertTrue(user.isUser())
        assertFalse(user.isOwner())
        assertFalse(user.isEmployee())

        // Test OWNER role
        user.role = UserRole.OWNER
        assertFalse(user.isUser())
        assertTrue(user.isOwner())
        assertFalse(user.isEmployee())

        // Test EMPLOYEE role
        user.role = UserRole.EMPLOYEE
        assertFalse(user.isUser())
        assertFalse(user.isOwner())
        assertTrue(user.isEmployee())
    }

    @Test
    @DisplayName("Should handle user default values correctly")
    void shouldHandleUserDefaultValuesCorrectly() {
        User user = new User()
        
        assertEquals(UserRole.USER, user.role)
        assertFalse(user.emailVerified)
        assertTrue(user.enabled)
    }

    @Test
    @DisplayName("Should handle user full name generation")
    void shouldHandleUserFullNameGeneration() {
        User user = new User()
        
        user.firstName = "John"
        user.lastName = "Doe"
        assertEquals("John Doe", user.getFullName())
        
        user.firstName = "Jane"
        user.lastName = "Smith"
        assertEquals("Jane Smith", user.getFullName())
        
        user.firstName = ""
        user.lastName = "Doe"
        assertEquals("Doe", user.getFullName().trim())
    }

    @Test
    @DisplayName("Should handle user roles enum correctly")
    void shouldHandleUserRolesEnumCorrectly() {
        assertEquals("USER", UserRole.USER.toString())
        assertEquals("OWNER", UserRole.OWNER.toString())
        assertEquals("EMPLOYEE", UserRole.EMPLOYEE.toString())
    }

    @Test
    @DisplayName("Basic test framework should work")
    void basicTestFrameworkShouldWork() {
        // This test verifies that the test framework is working correctly
        assertTrue(true, "Test framework is working")
    }

    @Test
    @DisplayName("Should handle null values gracefully")
    void shouldHandleNullValuesGracefully() {
        User user = new User()
        
        // Test null firstName and lastName
        user.firstName = null
        user.lastName = null
        assertEquals("null null", user.getFullName())
        
        // Test with one null value
        user.firstName = "John"
        user.lastName = null
        assertEquals("John null", user.getFullName())

        user.firstName = null
        user.lastName = "Doe"
        assertEquals("null Doe", user.getFullName())
    }

    @Test
    @DisplayName("Should handle user properties correctly")
    void shouldHandleUserPropertiesCorrectly() {
        User user = new User()
        
        // Test phone number
        user.phone = "+**********"
        assertEquals("+**********", user.phone)
        
        // Test avatar
        user.avatar = "https://example.com/avatar.jpg"
        assertEquals("https://example.com/avatar.jpg", user.avatar)
        
        // Test OAuth provider fields
        user.provider = "google"
        user.providerId = "google123"
        assertEquals("google", user.provider)
        assertEquals("google123", user.providerId)
    }

    @Test
    @DisplayName("Should handle boolean flags correctly")
    void shouldHandleBooleanFlagsCorrectly() {
        User user = new User()
        
        // Test default values
        assertFalse(user.emailVerified)
        assertTrue(user.enabled)
        
        // Test setting values
        user.emailVerified = true
        user.enabled = false
        
        assertTrue(user.emailVerified)
        assertFalse(user.enabled)
    }

    @Test
    @DisplayName("Should create user with all required fields")
    void shouldCreateUserWithAllRequiredFields() {
        User user = new User()
        user.email = "<EMAIL>"
        user.password = "securePassword123"
        user.firstName = "Complete"
        user.lastName = "User"
        user.role = UserRole.OWNER
        user.phone = "+**********"
        user.avatar = "https://example.com/avatar.jpg"
        user.emailVerified = true
        user.enabled = true
        user.provider = "local"
        user.providerId = "local123"
        
        // Verify all fields are set correctly
        assertEquals("<EMAIL>", user.email)
        assertEquals("securePassword123", user.password)
        assertEquals("Complete", user.firstName)
        assertEquals("User", user.lastName)
        assertEquals(UserRole.OWNER, user.role)
        assertEquals("+**********", user.phone)
        assertEquals("https://example.com/avatar.jpg", user.avatar)
        assertTrue(user.emailVerified)
        assertTrue(user.enabled)
        assertEquals("local", user.provider)
        assertEquals("local123", user.providerId)
        assertEquals("Complete User", user.getFullName())
        assertTrue(user.isOwner())
    }
}
