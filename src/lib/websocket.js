import SockJS from 'sockjs-client'

class WebSocketService {
  constructor() {
    this.socket = null
    this.connected = false
    this.messageHandlers = new Map()
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 1000
    this.connectionPromise = null // Performance: Prevent multiple connection attempts
    this.subscribedTopics = new Set() // Track subscribed topics for cleanup
  }

  connect(token = null, useSockJS = true) {
    // Performance optimization: Prevent multiple connection attempts
    if (this.connected) {
      return Promise.resolve()
    }

    if (this.connectionPromise) {
      return this.connectionPromise
    }

    this.connectionPromise = new Promise((resolve, reject) => {
      // WebSocket should connect to base URL without /api prefix
      const baseUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080'
      const apiUrl = baseUrl.replace('/api', '')

      // Store token for reconnection attempts and authentication
      this.token = token

      try {
        if (useSockJS) {
          // Use SockJS for better compatibility
          // Use /ws-notifications to avoid conflict with /api/notifications REST endpoints
          this.socket = new SockJS(`${apiUrl}/ws-notifications`)
          console.log('Using SockJS WebSocket connection to:', `${apiUrl}/ws-notifications`)
        } else {
          // Use native WebSocket
          const wsUrl = `${apiUrl.replace('http', 'ws')}/ws`
          this.socket = new WebSocket(wsUrl)
          console.log('Using native WebSocket connection to:', wsUrl)
        }

        this.socket.onopen = () => {
          console.log('WebSocket connected successfully')
          this.connected = true
          this.reconnectAttempts = 0
          this.connectionPromise = null // Reset connection promise

          // Authenticate if token is available
          if (this.token) {
            this.authenticate(this.token)
          }

          // Send pending subscriptions
          console.log('📡 Sending pending subscriptions:', this.messageHandlers.size, 'topics')
          this.messageHandlers.forEach((callback, topic) => {
            this.subscribeToTopic(topic)
          })

          resolve()
        }

        this.socket.onmessage = (event) => {
          console.log('🔌 WebSocket RAW message received:', event.data)
          console.log('🔌 Message type:', typeof event.data)

          // Check if this is a subscription confirmation
          if (event.data.includes('subscription_confirmed')) {
            console.log('🎯 SUBSCRIPTION CONFIRMATION DETECTED:', event.data)
          }

          this.handleMessage(event.data)
        }

        this.socket.onerror = (error) => {
          console.error('WebSocket error:', error)
          this.connected = false
          this.connectionPromise = null
          reject(error)
        }

        this.socket.onclose = (event) => {
          console.log('WebSocket connection closed:', event.code, event.reason)
          this.connected = false
          this.connectionPromise = null
          this.handleReconnect()
        }

      } catch (error) {
        console.error('Failed to create WebSocket connection:', error)
        this.connectionPromise = null
        reject(error)
      }
    })

    return this.connectionPromise
  }

  disconnect() {
    if (this.socket) {
      console.log('🔌 Disconnecting WebSocket...')

      // Performance optimization: Unsubscribe from all topics before disconnecting
      this.subscribedTopics.forEach(topic => {
        this.unsubscribeFromTopic(topic)
      })

      this.messageHandlers.clear()
      this.subscribedTopics.clear()

      // Properly close the socket
      this.socket.close(1000, 'Client disconnect')
      this.socket = null
      this.connected = false
      this.connectionPromise = null

      console.log('✅ WebSocket disconnected')
    }
  }

  subscribe(topic, callback) {
    console.log('🎯 Frontend subscribing to topic:', topic, 'Connected:', this.connected)
    this.messageHandlers.set(topic, callback)

    // Performance optimization: Send subscription request to backend
    if (this.connected) {
      this.subscribeToTopic(topic)
    } else {
      console.warn('⚠️ WebSocket not connected, subscription will be sent when connected')
    }

    console.log('✅ Frontend subscription registered for topic:', topic)
    return topic
  }

  unsubscribe(topic) {
    this.messageHandlers.delete(topic)

    // Performance optimization: Send unsubscription request to backend
    if (this.connected) {
      this.unsubscribeFromTopic(topic)
    }

    console.log('Unsubscribed from topic:', topic)
  }

  // Performance optimization: Send subscription request to backend
  subscribeToTopic(topic) {
    if (!this.connected) {
      console.warn('⚠️ Cannot subscribe to topic - WebSocket not connected:', topic)
      return
    }

    console.log('📡 Sending subscription request to backend for topic:', topic)
    console.log('📡 WebSocket connection state:', this.socket?.readyState)
    console.log('📡 Current subscribed topics:', Array.from(this.subscribedTopics))

    this.subscribedTopics.add(topic)

    const subscriptionMessage = {
      type: 'subscribe',
      topic: topic
    }

    console.log('📡 Subscription message:', subscriptionMessage)
    this.send(subscriptionMessage)
    console.log('✅ Subscription request sent to backend for topic:', topic)
  }

  // Performance optimization: Send unsubscription request to backend
  unsubscribeFromTopic(topic) {
    if (!this.connected) return

    this.subscribedTopics.delete(topic)
    this.send({
      type: 'unsubscribe',
      topic: topic
    })
  }

  authenticate(token) {
    if (!this.connected) {
      console.warn('WebSocket not connected. Cannot authenticate.')
      return
    }

    console.log('🔐 Authenticating WebSocket session...')
    this.send({
      type: 'authenticate',
      token: token
    })
  }

  send(message) {
    if (!this.connected) {
      console.warn('WebSocket not connected. Cannot send message:', message)
      return
    }

    const messageStr = typeof message === 'string' ? message : JSON.stringify(message)
    this.socket.send(messageStr)
    console.log('WebSocket message sent:', messageStr)
  }

  handleMessage(data) {
    let message

    // Handle both string and object data
    if (typeof data === 'string') {
      try {
        // Try to parse as JSON
        message = JSON.parse(data)
        console.log('✅ Successfully parsed JSON from string:', message)

        // Special check for subscription confirmations
        if (message.type === 'subscription_confirmed') {
          console.log('🎯 SUBSCRIPTION CONFIRMATION PARSED:', message)
        }
      } catch (error) {
        // Handle as plain text - notify all handlers
        console.log('📝 Handling as plain text (parse failed):', data)
        this.messageHandlers.forEach((callback, topic) => {
          callback(data)
        })
        return
      }
    } else if (typeof data === 'object') {
      // Already an object
      message = data
      console.log('✅ Received object data:', message)
    } else {
      console.log('📝 Unknown data type:', typeof data, data)
      this.messageHandlers.forEach((callback, topic) => {
        callback(data)
      })
      return
    }

    try {
      // Handle authentication confirmations
      if (message.type === 'authentication_confirmed') {
        console.log('✅ WebSocket authentication confirmed:', message.authenticated)
        return
      }

      // Handle subscription confirmations
      if (message.type === 'subscription_confirmed') {
        console.log('🎯 SUBSCRIPTION CONFIRMATION RECEIVED!')
        console.log('✅ Backend subscription confirmed for topic:', message.topic)
        console.log('✅ Frontend subscriptions:', Array.from(this.subscribedTopics))
        console.log('✅ Frontend handlers:', Array.from(this.messageHandlers.keys()))
        console.log('🎯 Full confirmation message:', message)
        return
      }

      if (message.type === 'subscription_denied') {
        console.warn('❌ Backend subscription denied for topic:', message.topic, 'Reason:', message.reason)
        return
      }

      if (message.type === 'unsubscription_confirmed') {
        console.log('✅ Backend unsubscription confirmed for topic:', message.topic)
        return
      }

      // Handle different message formats for backward compatibility
      let actualMessage = message
      let topicToMatch = null

      // Check if this is a topic-based message
      if (message.topic && message.data) {
        actualMessage = message
        topicToMatch = message.topic
      }
      // Check if this is a wrapped message from broadcastToAll (backward compatibility)
      else if (message.message) {
        try {
          const innerMessage = JSON.parse(message.message)
          console.log('🔍 Parsed inner message:', innerMessage)

          if (innerMessage.topic && innerMessage.data) {
            // Topic-based message
            actualMessage = innerMessage
            topicToMatch = innerMessage.topic
            console.log('🎯 Found topic-based inner message:', topicToMatch)
          } else if (innerMessage.type) {
            // Direct message type (like SLOT_UPDATE)
            actualMessage = innerMessage
            console.log('🎯 Found direct message type:', innerMessage.type)

            // For slot updates, we don't have a topic but we should still process it
            // Let it fall through to broadcast to all handlers
          }
        } catch (e) {
          console.log('Failed to parse inner message:', e)
          // Not a nested topic message, treat as regular message
        }
      }

      // Performance optimization: Topic-based message filtering
      if (topicToMatch) {
        console.log('🎯 Processing topic message:', topicToMatch)
        console.log('📋 Available subscriptions:', Array.from(this.messageHandlers.keys()))

        let handlerFound = false

        // Only notify handlers subscribed to this specific topic
        const handler = this.messageHandlers.get(topicToMatch)
        if (handler) {
          console.log('✅ Found exact topic handler for:', topicToMatch)
          handler(actualMessage)
          handlerFound = true
        }

        // Also check for wildcard subscriptions (e.g., slots.* for any slot updates)
        this.messageHandlers.forEach((callback, subscribedTopic) => {
          if (subscribedTopic !== topicToMatch && this.topicMatches(subscribedTopic, topicToMatch)) {
            console.log('✅ Found wildcard topic handler:', subscribedTopic, 'for message topic:', topicToMatch)
            callback(actualMessage)
            handlerFound = true
          }
        })

        if (!handlerFound) {
          console.warn('⚠️ No handler found for topic:', topicToMatch)
          console.log('Available handlers:', Array.from(this.messageHandlers.keys()))
        }
      } else {
        // For messages without topics, notify all handlers (backward compatibility)
        console.log('📢 Broadcasting message to all handlers (no topic):', actualMessage)
        this.messageHandlers.forEach((callback, topic) => {
          callback(actualMessage)
        })
      }
    } catch (error) {
      console.error('Error processing message:', error)
      console.log('Original data:', data)
      // Still try to notify handlers with the original message
      this.messageHandlers.forEach((callback, topic) => {
        callback(message || data)
      })
    }
  }

  // Performance helper: Check if topic patterns match
  topicMatches(subscribedTopic, messageTopic) {
    if (subscribedTopic.includes('*')) {
      const pattern = subscribedTopic.replace(/\*/g, '.*')
      return new RegExp(`^${pattern}$`).test(messageTopic)
    }
    return false
  }

  // Enhanced slot updates subscription for appointment booking workflow
  subscribeToSlotUpdates(shopId, serviceId, employeeId, date, callback) {
    // Create the most specific topic for this slot selection context
    const topic = employeeId
      ? `slots.${shopId}.${serviceId}.${employeeId}.${date}`
      : `slots.${shopId}.${serviceId}.${date}`

    console.log('🎯 WebSocket subscribing to slot updates:', topic)
    const subscriptionTopic = this.subscribe(topic, callback)

    // Return unsubscribe function
    return () => {
      console.log('🔌 WebSocket unsubscribing from slot updates:', topic)
      this.unsubscribe(subscriptionTopic)
    }
  }

  // Legacy method for backward compatibility
  subscribeToSlotUpdatesWithEmployee(shopId, serviceId, employeeId, date, callback) {
    return this.subscribeToSlotUpdates(shopId, serviceId, employeeId, date, callback)
  }

  // Subscribe to appointment updates for a user
  subscribeToAppointmentUpdates(userId, callback) {
    const topic = `appointments.${userId}`
    return this.subscribe(topic, callback)
  }

  // Subscribe to notifications for a user
  subscribeToNotifications(callback) {
    const topic = `notifications`
    return this.subscribe(topic, callback)
  }

  // Subscribe to notification count updates
  subscribeToNotificationCount(callback) {
    const topic = `notification-count`
    return this.subscribe(topic, callback)
  }

  // Subscribe to admin messages
  subscribeToAdminMessages(callback) {
    const topic = `admin-message`
    return this.subscribe(topic, callback)
  }

  // Subscribe to broadcast messages
  subscribeToBroadcast(callback) {
    const topic = `broadcast`
    return this.subscribe(topic, callback)
  }

  // Subscribe to WebSocket monitoring stats (admin only)
  subscribeToMonitoringStats(callback) {
    const topic = `admin.websocket-stats`
    return this.subscribe(topic, callback)
  }

  // Subscribe to pong responses
  subscribeToPong(callback) {
    const topic = `pong`
    return this.subscribe(topic, callback)
  }

  // Send slot lock request
  lockSlot(slotData) {
    this.send({ type: 'slot.lock', data: slotData })
  }

  // Send slot unlock request
  unlockSlot(slotData) {
    this.send({ type: 'slot.unlock', data: slotData })
  }

  // Request connection stats (admin)
  requestStats() {
    this.send({ type: 'admin.stats' })
  }

  // Send admin broadcast message
  sendAdminBroadcast(message) {
    this.send({ type: 'admin.broadcast', message })
  }

  // Send message to specific user (admin)
  sendAdminUserMessage(targetUser, message) {
    this.send({ type: 'admin.user-message', targetUser, message })
  }

  // Send ping to test connection
  sendPing() {
    this.send({ type: 'ping', timestamp: Date.now() })
  }

  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)

      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

      setTimeout(() => {
        const token = localStorage.getItem('token')
        if (token) {
          this.connect(token).catch((error) => {
            console.error('Reconnection failed:', error)
          })
        } else {
          console.log('No token available for reconnection, skipping attempt')
        }
      }, delay)
    } else {
      console.error('Max reconnection attempts reached')
    }
  }

  isConnected() {
    return this.connected
  }
}

// Create a singleton instance
const webSocketService = new WebSocketService()

export default webSocketService
