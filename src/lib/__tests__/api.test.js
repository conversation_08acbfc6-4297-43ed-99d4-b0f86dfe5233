import axios from 'axios';
import { api, authAPI, userAPI } from '../api';

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
global.localStorage = localStorageMock;

describe('API Configuration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  test('should create axios instance with correct base URL', () => {
    expect(mockedAxios.create).toHaveBeenCalledWith({
      baseURL: 'http://localhost:8080/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });

  test('should add authorization header when token exists', () => {
    const mockToken = 'mock-jwt-token';
    localStorageMock.getItem.mockReturnValue(mockToken);

    // Simulate request interceptor
    const config = { headers: {} };
    const interceptor = mockedAxios.create().interceptors.request.use.mock.calls[0][0];
    const result = interceptor(config);

    expect(result.headers.Authorization).toBe(`Bearer ${mockToken}`);
  });

  test('should not add authorization header when no token exists', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const config = { headers: {} };
    const interceptor = mockedAxios.create().interceptors.request.use.mock.calls[0][0];
    const result = interceptor(config);

    expect(result.headers.Authorization).toBeUndefined();
  });

  test('should handle request interceptor errors', () => {
    const error = new Error('Request error');
    const interceptor = mockedAxios.create().interceptors.request.use.mock.calls[0][1];
    
    expect(() => interceptor(error)).rejects.toThrow('Request error');
  });

  test('should handle successful responses', () => {
    const response = { data: { message: 'Success' }, status: 200 };
    const interceptor = mockedAxios.create().interceptors.response.use.mock.calls[0][0];
    
    const result = interceptor(response);
    expect(result).toBe(response);
  });

  test('should handle 401 unauthorized responses', () => {
    const error = {
      response: { status: 401 },
      config: { url: '/some-endpoint' },
    };
    
    const interceptor = mockedAxios.create().interceptors.response.use.mock.calls[0][1];
    
    expect(() => interceptor(error)).rejects.toThrow();
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth-token');
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth-user');
  });

  test('should not redirect on 401 for auth endpoints', () => {
    const error = {
      response: { status: 401 },
      config: { url: '/auth/login' },
    };
    
    const interceptor = mockedAxios.create().interceptors.response.use.mock.calls[0][1];
    
    expect(() => interceptor(error)).rejects.toThrow();
    expect(localStorageMock.removeItem).not.toHaveBeenCalled();
  });

  test('should handle network errors', () => {
    const error = new Error('Network Error');
    error.code = 'NETWORK_ERROR';
    
    const interceptor = mockedAxios.create().interceptors.response.use.mock.calls[0][1];
    
    expect(() => interceptor(error)).rejects.toThrow('Network Error');
  });
});

describe('Auth API', () => {
  const mockApi = {
    post: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock the api instance
    jest.doMock('../api', () => ({
      api: mockApi,
      authAPI: {
        login: (credentials) => mockApi.post('/auth/login', credentials),
        register: (userData) => mockApi.post('/auth/register', userData),
        registerOwner: (userData) => mockApi.post('/auth/register/owner', userData),
        logout: () => mockApi.post('/auth/logout'),
      },
    }));
  });

  test('should call login endpoint with credentials', async () => {
    const credentials = { email: '<EMAIL>', password: 'password123' };
    const mockResponse = { data: { token: 'mock-token' } };
    
    mockApi.post.mockResolvedValue(mockResponse);
    
    const { authAPI } = require('../api');
    const result = await authAPI.login(credentials);
    
    expect(mockApi.post).toHaveBeenCalledWith('/auth/login', credentials);
    expect(result).toBe(mockResponse);
  });

  test('should call register endpoint with user data', async () => {
    const userData = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'John',
      lastName: 'Doe',
    };
    const mockResponse = {
      data: {
        token: 'mock-jwt-token',
        type: 'Bearer',
        id: 'user-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        roles: ['ROLE_USER']
      }
    };
    
    mockApi.post.mockResolvedValue(mockResponse);
    
    const { authAPI } = require('../api');
    const result = await authAPI.register(userData);
    
    expect(mockApi.post).toHaveBeenCalledWith('/auth/register', userData);
    expect(result).toBe(mockResponse);
  });

  test('should call register owner endpoint with user data', async () => {
    const userData = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Jane',
      lastName: 'Doe',
    };
    const mockResponse = { data: { message: 'Owner registered successfully!' } };
    
    mockApi.post.mockResolvedValue(mockResponse);
    
    const { authAPI } = require('../api');
    const result = await authAPI.registerOwner(userData);
    
    expect(mockApi.post).toHaveBeenCalledWith('/auth/register/owner', userData);
    expect(result).toBe(mockResponse);
  });

  test('should call logout endpoint', async () => {
    const mockResponse = { data: { message: 'Logged out successfully' } };
    
    mockApi.post.mockResolvedValue(mockResponse);
    
    const { authAPI } = require('../api');
    const result = await authAPI.logout();
    
    expect(mockApi.post).toHaveBeenCalledWith('/auth/logout');
    expect(result).toBe(mockResponse);
  });
});

describe('User API', () => {
  const mockApi = {
    get: jest.fn(),
    put: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.doMock('../api', () => ({
      api: mockApi,
      userAPI: {
        getProfile: () => mockApi.get('/user/profile'),
        updateProfile: (userData) => mockApi.put('/user/profile', userData),
        changePassword: (passwordData) => mockApi.put('/user/change-password', passwordData),
      },
    }));
  });

  test('should call get profile endpoint', async () => {
    const mockResponse = {
      data: {
        id: '123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
      },
    };
    
    mockApi.get.mockResolvedValue(mockResponse);
    
    const { userAPI } = require('../api');
    const result = await userAPI.getProfile();
    
    expect(mockApi.get).toHaveBeenCalledWith('/user/profile');
    expect(result).toBe(mockResponse);
  });

  test('should call update profile endpoint with user data', async () => {
    const userData = {
      firstName: 'Jane',
      lastName: 'Smith',
      phone: '+1234567890',
    };
    const mockResponse = { data: { message: 'Profile updated successfully' } };
    
    mockApi.put.mockResolvedValue(mockResponse);
    
    const { userAPI } = require('../api');
    const result = await userAPI.updateProfile(userData);
    
    expect(mockApi.put).toHaveBeenCalledWith('/user/profile', userData);
    expect(result).toBe(mockResponse);
  });

  test('should call change password endpoint with password data', async () => {
    const passwordData = {
      currentPassword: 'oldPassword123',
      newPassword: 'newPassword123',
    };
    const mockResponse = { data: { message: 'Password changed successfully' } };
    
    mockApi.put.mockResolvedValue(mockResponse);
    
    const { userAPI } = require('../api');
    const result = await userAPI.changePassword(passwordData);
    
    expect(mockApi.put).toHaveBeenCalledWith('/user/change-password', passwordData);
    expect(result).toBe(mockResponse);
  });
});

describe('API Error Handling', () => {
  test('should handle timeout errors', () => {
    const error = new Error('timeout of 10000ms exceeded');
    error.code = 'ECONNABORTED';
    
    const interceptor = mockedAxios.create().interceptors.response.use.mock.calls[0][1];
    
    expect(() => interceptor(error)).rejects.toThrow('timeout of 10000ms exceeded');
  });

  test('should handle server errors (5xx)', () => {
    const error = {
      response: { 
        status: 500,
        data: { message: 'Internal Server Error' }
      },
      config: { url: '/some-endpoint' },
    };
    
    const interceptor = mockedAxios.create().interceptors.response.use.mock.calls[0][1];
    
    expect(() => interceptor(error)).rejects.toThrow();
  });

  test('should handle client errors (4xx)', () => {
    const error = {
      response: { 
        status: 400,
        data: { error: 'Bad Request' }
      },
      config: { url: '/some-endpoint' },
    };
    
    const interceptor = mockedAxios.create().interceptors.response.use.mock.calls[0][1];
    
    expect(() => interceptor(error)).rejects.toThrow();
  });

  test('should handle requests without response', () => {
    const error = new Error('Request failed');
    error.request = {};
    
    const interceptor = mockedAxios.create().interceptors.response.use.mock.calls[0][1];
    
    expect(() => interceptor(error)).rejects.toThrow('Request failed');
  });
});
