package com.ddimitko.beautyhub.service

import com.ddimitko.beautyhub.dto.PaymentIntentRequest
import com.ddimitko.beautyhub.dto.PaymentIntentResponse
import com.ddimitko.beautyhub.entity.Appointment
import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.repository.AppointmentRepository
import com.ddimitko.beautyhub.repository.ShopRepository
import com.stripe.Stripe
import com.stripe.model.PaymentIntent
import com.stripe.param.PaymentIntentCreateParams
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service as SpringService

@SpringService
class PaymentService {

    @Autowired
    private ShopRepository shopRepository

    @Autowired
    private AppointmentRepository appointmentRepository

    @Value('${stripe.api.key:}')
    private String stripeSecretKey

    /**
     * Creates a payment intent for appointment booking using real Stripe API
     */
    PaymentIntentResponse createPaymentIntent(PaymentIntentRequest request) {
        Shop shop = shopRepository.findById(request.shopId)
                .orElseThrow { new RuntimeException("Shop not found") }

        if (!shop.acceptsCardPayments) {
            throw new IllegalArgumentException("Shop does not accept card payments")
        }

        // For testing: allow shops without connected accounts (direct payments)
        boolean useConnectedAccount = shop.stripeOnboardingCompleted && shop.stripeAccountId

        try {
            // Set Stripe API key
            Stripe.apiKey = stripeSecretKey

            // Convert amount to cents for Stripe
            Long amountInCents = Math.round(request.amount * 100)

            // Create payment intent with Stripe
            PaymentIntentCreateParams.Builder paramsBuilder = PaymentIntentCreateParams.builder()
                .setAmount(amountInCents)
                .setCurrency(request.currency ?: "usd")
                .setDescription(request.description)
                .putMetadata("shop_id", request.shopId.toString())
                .putMetadata("service_id", request.serviceId.toString())
                .putMetadata("customer_email", request.customerEmail ?: "")
                .putMetadata("customer_name", request.customerName ?: "")

            // Only add connected account details if shop has completed onboarding
            if (useConnectedAccount) {
                paramsBuilder
                    .setApplicationFeeAmount(Math.round(amountInCents * 0.05)) // 5% platform fee
                    .setTransferData(
                        PaymentIntentCreateParams.TransferData.builder()
                            .setDestination(shop.stripeAccountId)
                            .build()
                    )
            }

            PaymentIntentCreateParams params = paramsBuilder.build()

            PaymentIntent paymentIntent = PaymentIntent.create(params)

            PaymentIntentResponse response = new PaymentIntentResponse()
            response.paymentIntentId = paymentIntent.getId()
            response.clientSecret = paymentIntent.getClientSecret()
            response.amount = request.amount
            response.currency = paymentIntent.getCurrency()
            response.status = paymentIntent.getStatus()
            response.shopName = shop.name
            response.description = request.description

            return response

        } catch (Exception e) {
            throw new RuntimeException("Failed to create payment intent: ${e.getMessage()}", e)
        }
    }

    /**
     * Confirms a payment intent after successful payment
     */
    PaymentIntentResponse confirmPaymentIntent(String paymentIntentId, String paymentMethodId) {
        try {
            // Set Stripe API key
            Stripe.apiKey = stripeSecretKey

            // Retrieve the payment intent from Stripe
            PaymentIntent paymentIntent = PaymentIntent.retrieve(paymentIntentId)

            PaymentIntentResponse response = new PaymentIntentResponse()
            response.paymentIntentId = paymentIntent.getId()
            response.status = paymentIntent.getStatus()
            response.paymentMethodId = paymentMethodId
            response.amount = paymentIntent.getAmount() / 100.0 // Convert from cents

            return response

        } catch (Exception e) {
            throw new RuntimeException("Failed to confirm payment intent: ${e.getMessage()}", e)
        }
    }

    /**
     * Processes refund for cancelled appointments
     */
    void processRefund(String paymentIntentId, BigDecimal amount, String reason) {
        // Mock refund processing
        // In production, integrate with Stripe refunds API
    }

    /**
     * Updates appointment with payment information
     */
    void updateAppointmentPayment(UUID appointmentId, String paymentIntentId, String status) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow { new RuntimeException("Appointment not found") }

        appointment.paymentIntentId = paymentIntentId
        appointment.paymentStatus = status

        appointmentRepository.save(appointment)
    }

    /**
     * Validates payment amount matches service price
     */
    boolean validatePaymentAmount(UUID serviceId, BigDecimal amount) {
        // This would validate against the service price
        // For now, return true as mock implementation
        return true
    }
}
