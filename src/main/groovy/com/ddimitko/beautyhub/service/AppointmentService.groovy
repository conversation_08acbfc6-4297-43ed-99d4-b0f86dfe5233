package com.ddimitko.beautyhub.service

import com.ddimitko.beautyhub.dto.*
import com.ddimitko.beautyhub.entity.*
import com.ddimitko.beautyhub.enums.AppointmentStatus
import com.ddimitko.beautyhub.enums.DayOfWeek
import com.ddimitko.beautyhub.enums.PaymentType
import com.ddimitko.beautyhub.repository.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service as SpringService
import org.springframework.transaction.annotation.Transactional

import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

@SpringService
@Transactional
class AppointmentService {

    @Autowired
    private AppointmentRepository appointmentRepository

    @Autowired
    private UserService userService

    @Autowired
    private ShopService shopService

    @Autowired
    private EmployeeService employeeService

    @Autowired
    private ServiceRepository serviceRepository

    @Autowired
    private EmployeeRepository employeeRepository

    @Autowired
    private ScheduleSlotRepository scheduleSlotRepository

    @Autowired
    private SlotLockingService slotLockingService

    @Autowired
    private SlotUpdateService slotUpdateService

    @Autowired
    private StripeService stripeService

    @Autowired
    private PaymentService paymentService

    /**
     * Creates a new appointment (supports both authenticated and guest users)
     */
    AppointmentResponse createAppointment(AppointmentCreationRequest request, UUID authenticatedUserId = null) {
        // Validate request
        validateAppointmentRequest(request, authenticatedUserId)

        // Validate slot lock token is provided
        if (!request.slotLockToken) {
            throw new IllegalArgumentException("Slot lock token is required for appointment creation")
        }

        // Parse and validate the lock token as UUID
        UUID lockTokenUUID
        try {
            lockTokenUUID = UUID.fromString(request.slotLockToken)
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid slot lock token format")
        }

        // Get entities
        Shop shop = shopService.findById(request.shopId)
        Employee employee = employeeRepository.findById(request.employeeId)
                .orElseThrow { new RuntimeException("Employee not found") }
        com.ddimitko.beautyhub.entity.Service service = serviceRepository.findById(request.serviceId)
                .orElseThrow { new RuntimeException("Service not found") }

        User user = null
        if (authenticatedUserId) {
            user = userService.findById(authenticatedUserId)
        }

        // Validate slot availability and lock ownership
        validateSlotAvailabilityAndLock(request, employee, service, lockTokenUUID)

        // Create appointment with a unique ID (don't use lock token as ID to prevent overwrites)
        Appointment appointment = new Appointment()
        appointment.id = UUID.randomUUID()  // Generate unique appointment ID
        appointment.user = user
        appointment.shop = shop
        appointment.employee = employee
        appointment.service = service
        appointment.appointmentDateTime = request.appointmentDateTime
        appointment.paymentType = request.paymentType
        appointment.totalAmount = service.price
        appointment.depositAmount = request.depositAmount ?: service.depositAmount ?: BigDecimal.ZERO
        appointment.notes = request.notes
        appointment.status = AppointmentStatus.CONFIRMED

        // Set guest information if not authenticated
        if (!user && request.isGuestBooking()) {
            appointment.guestEmail = request.guestEmail
            appointment.guestFirstName = request.guestFirstName
            appointment.guestLastName = request.guestLastName
            appointment.guestPhone = request.guestPhone
        }

        // Handle payment if required
        if (request.requiresPayment()) {
            handlePayment(appointment, request)
        }

        // Save appointment first
        appointment = appointmentRepository.save(appointment)

        // If appointment was successfully created, remove the slot lock from Redis
        // This migrates the slot from "locked" state to "booked" state in the database
        try {
            slotLockingService.unlockSlotIfOwnedByUser(
                    request.shopId, request.serviceId, request.employeeId,
                    request.appointmentDateTime, lockTokenUUID
            )
        } catch (Exception e) {
            // Log the error but don't fail the appointment creation
            // The slot lock will expire automatically after 5 minutes
            println("Warning: Failed to remove slot lock after successful appointment creation: ${e.getMessage()}")
        }

        // Broadcast slot booked event to notify other users via WebSocket
        try {
            slotUpdateService.broadcastSlotBooked(
                    request.shopId, request.serviceId, request.employeeId,
                    request.appointmentDateTime, user.id
            )
        } catch (Exception e) {
            // Log the error but don't fail the appointment creation
            println("Warning: Failed to broadcast slot booked event: ${e.getMessage()}")
        }

        return convertToResponse(appointment)
    }

    /**
     * Updates an existing appointment
     */
    AppointmentResponse updateAppointment(UUID appointmentId, AppointmentUpdateRequest request, UUID userId) {
        Appointment appointment = findAppointmentById(appointmentId)
        
        // Validate user can update this appointment
        validateUserCanModifyAppointment(appointment, userId)
        
        // Validate appointment can be modified
        if (!appointment.canBeModified()) {
            throw new IllegalArgumentException("Appointment cannot be modified")
        }

        // If rescheduling, validate new slot
        if (request.appointmentDateTime && !request.appointmentDateTime.equals(appointment.appointmentDateTime)) {
            validateReschedule(appointment, request.appointmentDateTime)
        }

        // Update fields
        if (request.appointmentDateTime) {
            appointment.appointmentDateTime = request.appointmentDateTime
        }
        if (request.notes != null) {
            appointment.notes = request.notes
        }

        appointment = appointmentRepository.save(appointment)
        return convertToResponse(appointment)
    }

    /**
     * Cancels an appointment
     */
    AppointmentResponse cancelAppointment(UUID appointmentId, String reason, UUID userId) {
        Appointment appointment = findAppointmentById(appointmentId)
        
        // Validate user can cancel this appointment
        validateUserCanModifyAppointment(appointment, userId)
        
        // Validate appointment can be cancelled
        if (!appointment.canBeCancelled()) {
            throw new IllegalArgumentException("Appointment cannot be cancelled")
        }

        appointment.status = AppointmentStatus.CANCELLED
        appointment.cancellationReason = reason
        appointment.cancelledAt = LocalDateTime.now()
        appointment.cancelledBy = userId?.toString() ?: "guest"

        // Handle refund if payment was made
        if (appointment.paymentIntentId) {
            // TODO: Implement refund logic with Stripe
        }

        appointment = appointmentRepository.save(appointment)
        return convertToResponse(appointment)
    }

    /**
     * Gets available time slots for a specific employee and service on a given date
     */
    List<AvailableSlotResponse> getAvailableSlots(UUID shopId, UUID employeeId, UUID serviceId, LocalDate date) {
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow { new RuntimeException("Employee not found") }
        com.ddimitko.beautyhub.entity.Service service = serviceRepository.findById(serviceId)
                .orElseThrow { new RuntimeException("Service not found") }

        // Get employee's schedule for the day
        DayOfWeek dayOfWeek = DayOfWeek.valueOf(date.dayOfWeek.name())
        List<ScheduleSlot> scheduleSlots = scheduleSlotRepository
                .findByEmployeeAndDayOfWeekAndActiveTrue(employee, dayOfWeek)

        // Get existing appointments for the day
        LocalDateTime startOfDay = date.atStartOfDay()
        LocalDateTime endOfDay = date.atTime(23, 59, 59)
        List<Appointment> existingAppointments = appointmentRepository
                .findByEmployeeAndAppointmentDateTimeBetween(employee, startOfDay, endOfDay)

        // Generate available slots
        List<AvailableSlotResponse> availableSlots = []
        
        LocalDateTime now = LocalDateTime.now()
        boolean isToday = date.equals(now.toLocalDate())

        scheduleSlots.each { slot ->
            LocalTime effectiveStartTime = slot.startTime

            // If booking for today and current time is after the schedule start time,
            // adjust the start time to the next available slot after current time
            if (isToday && now.toLocalTime().isAfter(slot.startTime)) {
                // Round up to the next slot boundary
                int minutesSinceStart = (int) Duration.between(slot.startTime, now.toLocalTime()).toMinutes()
                int slotsToSkip = (int) Math.ceil((double) minutesSinceStart / service.durationMinutes)
                effectiveStartTime = slot.startTime.plusMinutes(slotsToSkip * service.durationMinutes)

                // If the effective start time is beyond the end time, skip this slot entirely
                if (effectiveStartTime.plusMinutes(service.durationMinutes) > slot.endTime) {
                    return // Skip this schedule slot
                }
            }

            List<LocalTime> timeSlots = generateTimeSlots(effectiveStartTime, slot.endTime, service.durationMinutes)

            timeSlots.each { startTime ->
                LocalDateTime slotDateTime = date.atTime(startTime)
                LocalTime endTime = startTime.plusMinutes(service.durationMinutes)

                // Check if slot is available (must be in the future and not conflicting)
                boolean isAvailable = !hasConflictingAppointment(existingAppointments, slotDateTime, endTime) &&
                                     slotDateTime.isAfter(now)

                // Check if slot is locked
                boolean isLocked = slotLockingService.isSlotLocked(shopId, serviceId, employeeId, slotDateTime)

                AvailableSlotResponse slotResponse = new AvailableSlotResponse()
                slotResponse.dateTime = slotDateTime
                slotResponse.startTime = startTime
                slotResponse.endTime = endTime
                slotResponse.available = isAvailable
                slotResponse.locked = isLocked
                slotResponse.employeeId = employeeId
                slotResponse.employeeName = employee.fullName
                slotResponse.serviceId = serviceId
                slotResponse.serviceName = service.name
                slotResponse.durationMinutes = service.durationMinutes
                slotResponse.price = service.price

                if (isLocked) {
                    slotResponse.lockedBy = slotLockingService.getSlotLockOwner(shopId, serviceId, employeeId, slotDateTime)
                }

                availableSlots.add(slotResponse)
            }
        }
        
        return availableSlots.sort { it.dateTime }
    }

    /**
     * Links guest appointments to a registered user after email verification
     */
    void linkGuestAppointmentsToUser(String email, UUID userId) {
        List<Appointment> guestAppointments = appointmentRepository.findByGuestEmailAndUserIsNull(email)
        User user = userService.findById(userId)
        
        guestAppointments.each { appointment ->
            appointment.user = user
            // Keep guest information for audit trail
            appointmentRepository.save(appointment)
        }
    }

    /**
     * Gets appointments for a user (including linked guest appointments)
     */
    Page<AppointmentResponse> getUserAppointments(UUID userId, Pageable pageable) {
        User user = userService.findById(userId)
        Page<Appointment> appointments = appointmentRepository.findByUser(user, pageable)
        return appointments.map { convertToResponse(it) }
    }

    /**
     * Gets guest appointments by email (before registration)
     */
    List<AppointmentResponse> getGuestAppointments(String email) {
        List<Appointment> appointments = appointmentRepository.findByGuestEmailAndUserIsNull(email)
        return appointments.collect { convertToResponse(it) }
    }

    /**
     * Gets appointments for a specific shop
     */
    Page<AppointmentResponse> getShopAppointments(UUID shopId, UUID userId, Pageable pageable) {
        Shop shop = shopService.findById(shopId)

        // Validate user has access to this shop (owner or employee)
        User user = userService.findById(userId)
        boolean hasAccess = shop.owner.id.equals(userId) ||
                           employeeService.isEmployeeAtShop(userId, shopId)

        if (!hasAccess) {
            throw new IllegalArgumentException("Access denied to shop appointments")
        }

        Page<Appointment> appointments = appointmentRepository.findByShop(shop, pageable)
        return appointments.map { convertToResponse(it) }
    }

    /**
     * Gets appointments for a specific employee
     */
    Page<AppointmentResponse> getEmployeeAppointments(UUID employeeId, UUID userId, Pageable pageable) {
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow { new RuntimeException("Employee not found") }

        // Validate user has access to this employee's appointments (shop owner or the employee themselves)
        User user = userService.findById(userId)
        boolean hasAccess = employee.shop.owner.id.equals(userId) ||
                           employee.user.id.equals(userId)

        if (!hasAccess) {
            throw new IllegalArgumentException("Access denied to employee appointments")
        }

        Page<Appointment> appointments = appointmentRepository.findByEmployee(employee, pageable)
        return appointments.map { convertToResponse(it) }
    }

    /**
     * Updates appointment status
     */
    AppointmentResponse updateAppointmentStatus(UUID appointmentId, String statusString, UUID userId) {
        Appointment appointment = findAppointmentById(appointmentId)

        // Validate user has permission to update status (shop owner or employee)
        User user = userService.findById(userId)
        boolean hasAccess = appointment.shop.owner.id.equals(userId) ||
                           appointment.employee.user.id.equals(userId)

        if (!hasAccess) {
            throw new IllegalArgumentException("Access denied to update appointment status")
        }

        // Parse and validate status
        AppointmentStatus newStatus
        try {
            newStatus = AppointmentStatus.valueOf(statusString.toUpperCase())
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid appointment status: ${statusString}")
        }

        // Validate status transition
        validateStatusTransition(appointment, newStatus)

        appointment.status = newStatus
        appointment = appointmentRepository.save(appointment)

        return convertToResponse(appointment)
    }

    /**
     * Gets appointments by status
     */
    Page<AppointmentResponse> getAppointmentsByStatus(String statusString, Pageable pageable) {
        AppointmentStatus status
        try {
            status = AppointmentStatus.valueOf(statusString.toUpperCase())
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid appointment status: ${statusString}")
        }

        Page<Appointment> appointments = appointmentRepository.findByStatus(status, pageable)
        return appointments.map { convertToResponse(it) }
    }

    // Private helper methods

    private void validateAppointmentRequest(AppointmentCreationRequest request, UUID authenticatedUserId) {
        // Validate guest information if not authenticated
        if (!authenticatedUserId && !request.isGuestBooking()) {
            throw new IllegalArgumentException("Guest information is required for unauthenticated bookings")
        }

        // Validate employee self-booking prevention
        if (authenticatedUserId) {
            employeeService.validateEmployeeCannotBookAtOwnShop(authenticatedUserId, request.shopId)
        }

        // Validate appointment is in the future
        if (!request.appointmentDateTime.isAfter(LocalDateTime.now())) {
            throw new IllegalArgumentException("Appointment must be in the future")
        }
    }

    private void validateSlotAvailabilityAndLock(AppointmentCreationRequest request, Employee employee, Service service, UUID lockTokenUUID) {
        // Check for conflicting appointments
        LocalDateTime endTime = request.appointmentDateTime.plusMinutes(service.durationMinutes)
        List<Appointment> conflicts = appointmentRepository.findConflictingAppointments(
                employee, request.appointmentDateTime, endTime
        )

        if (!conflicts.isEmpty()) {
            throw new IllegalArgumentException("Time slot is not available")
        }

        // Validate slot is within employee's schedule
        DayOfWeek dayOfWeek = DayOfWeek.valueOf(request.appointmentDateTime.dayOfWeek.name())
        LocalTime appointmentTime = request.appointmentDateTime.toLocalTime()

        List<ScheduleSlot> scheduleSlots = scheduleSlotRepository
                .findByEmployeeAndDayOfWeekAndActiveTrue(employee, dayOfWeek)

        boolean withinSchedule = scheduleSlots.any { slot ->
            appointmentTime >= slot.startTime &&
            appointmentTime.plusMinutes(service.durationMinutes) <= slot.endTime
        }

        if (!withinSchedule) {
            throw new IllegalArgumentException("Appointment time is outside employee's working hours")
        }

        // Validate that the slot is locked by the provided lock token
        if (!slotLockingService.isSlotLockedByUser(
                request.shopId, request.serviceId, request.employeeId,
                request.appointmentDateTime, lockTokenUUID)) {
            throw new IllegalArgumentException("Invalid or expired slot lock token")
        }
    }

    private void handlePayment(Appointment appointment, AppointmentCreationRequest request) {
        if (request.paymentMethodId) {
            // Create payment intent request
            PaymentIntentRequest paymentRequest = new PaymentIntentRequest()
            paymentRequest.shopId = appointment.shop.id
            paymentRequest.serviceId = appointment.service.id
            paymentRequest.amount = appointment.totalAmount
            paymentRequest.description = "Appointment: ${appointment.service.name} at ${appointment.shop.name}"
            paymentRequest.customerEmail = appointment.user?.email ?: appointment.guestEmail
            paymentRequest.customerName = appointment.user?.fullName ?: "${appointment.guestFirstName} ${appointment.guestLastName}".trim()

            // Create payment intent
            PaymentIntentResponse paymentResponse = paymentService.createPaymentIntent(paymentRequest)
            appointment.paymentIntentId = paymentResponse.paymentIntentId
            appointment.paymentStatus = paymentResponse.status
        }
    }

    private void validateUserCanModifyAppointment(Appointment appointment, UUID userId) {
        if (appointment.user?.id != userId) {
            throw new IllegalArgumentException("You can only modify your own appointments")
        }
    }

    private void validateReschedule(Appointment appointment, LocalDateTime newDateTime) {
        // Validate new time slot availability
        LocalDateTime endTime = newDateTime.plusMinutes(appointment.service.durationMinutes)
        List<Appointment> conflicts = appointmentRepository.findConflictingAppointments(
                appointment.employee, newDateTime, endTime
        )

        // Exclude current appointment from conflict check
        conflicts = conflicts.findAll { it.id != appointment.id }

        if (!conflicts.isEmpty()) {
            throw new IllegalArgumentException("New time slot is not available")
        }
    }

    private Appointment findAppointmentById(UUID appointmentId) {
        return appointmentRepository.findById(appointmentId)
                .orElseThrow { new RuntimeException("Appointment not found") }
    }

    private List<LocalTime> generateTimeSlots(LocalTime startTime, LocalTime endTime, Integer durationMinutes) {
        List<LocalTime> slots = []
        LocalTime current = startTime

        while (current.plusMinutes(durationMinutes) <= endTime) {
            slots.add(current)
            current = current.plusMinutes(durationMinutes)
        }

        return slots
    }

    private boolean hasConflictingAppointment(List<Appointment> appointments, LocalDateTime startTime, LocalTime endTime) {
        LocalDateTime slotEndTime = startTime.toLocalDate().atTime(endTime)

        return appointments.any { appointment ->
            appointment.appointmentDateTime < slotEndTime &&
            appointment.endDateTime > startTime &&
            appointment.status in [AppointmentStatus.PENDING, AppointmentStatus.CONFIRMED]
        }
    }

    private void validateStatusTransition(Appointment appointment, AppointmentStatus newStatus) {
        AppointmentStatus currentStatus = appointment.status

        // Cannot change status of cancelled or completed appointments
        if (currentStatus == AppointmentStatus.CANCELLED) {
            throw new IllegalArgumentException("Cannot update status of cancelled appointment")
        }

        if (currentStatus == AppointmentStatus.COMPLETED) {
            throw new IllegalArgumentException("Cannot update status of completed appointment")
        }

        // Validate specific transitions
        switch (newStatus) {
            case AppointmentStatus.CONFIRMED:
                if (currentStatus != AppointmentStatus.PENDING) {
                    throw new IllegalArgumentException("Can only confirm pending appointments")
                }
                break
            case AppointmentStatus.COMPLETED:
                if (currentStatus != AppointmentStatus.CONFIRMED) {
                    throw new IllegalArgumentException("Can only complete confirmed appointments")
                }
                break
            case AppointmentStatus.NO_SHOW:
                if (currentStatus != AppointmentStatus.CONFIRMED) {
                    throw new IllegalArgumentException("Can only mark confirmed appointments as no-show")
                }
                // Validate appointment time has passed
                if (appointment.appointmentDateTime.isAfter(LocalDateTime.now())) {
                    throw new IllegalArgumentException("Cannot mark future appointments as no-show")
                }
                break
            case AppointmentStatus.CANCELLED:
                throw new IllegalArgumentException("Use cancelAppointment method to cancel appointments")
        }
    }

    private AppointmentResponse convertToResponse(Appointment appointment) {
        AppointmentResponse response = new AppointmentResponse()

        // Basic appointment info
        response.id = appointment.id
        response.appointmentDateTime = appointment.appointmentDateTime
        response.endDateTime = appointment.endDateTime
        response.status = appointment.status
        response.paymentType = appointment.paymentType
        response.totalAmount = appointment.totalAmount
        response.depositAmount = appointment.depositAmount
        response.notes = appointment.notes
        response.paymentIntentId = appointment.paymentIntentId
        response.paymentStatus = appointment.paymentStatus
        response.cancellationReason = appointment.cancellationReason
        response.cancelledAt = appointment.cancelledAt
        response.cancelledBy = appointment.cancelledBy
        response.reminderSent = appointment.reminderSent
        response.confirmationSent = appointment.confirmationSent
        response.createdAt = appointment.createdAt
        response.updatedAt = appointment.updatedAt

        // Shop info
        response.shopId = appointment.shop.id
        response.shopName = appointment.shop.name
        response.shopAddress = appointment.shop.getFullAddress()
        response.shopPhone = appointment.shop.phone

        // Employee info
        response.employeeId = appointment.employee.id
        response.employeeName = appointment.employee.fullName
        response.employeeSpecialties = appointment.employee.specialties

        // Service info
        response.serviceId = appointment.service.id
        response.serviceName = appointment.service.name
        response.serviceDescription = appointment.service.description
        response.serviceDurationMinutes = appointment.service.durationMinutes
        response.servicePrice = appointment.service.price

        // User/Guest info
        if (appointment.user) {
            response.userId = appointment.user.id
            response.userName = appointment.user.fullName
            response.userEmail = appointment.user.email
        } else {
            response.guestEmail = appointment.guestEmail
            response.guestFirstName = appointment.guestFirstName
            response.guestLastName = appointment.guestLastName
            response.guestPhone = appointment.guestPhone
        }

        return response
    }
}
