package com.ddimitko.beautyhub.service

import com.ddimitko.beautyhub.entity.Notification
import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.enums.NotificationType
import com.ddimitko.beautyhub.repository.NotificationRepository
import com.ddimitko.beautyhub.repository.UserRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.messaging.simp.SimpMessagingTemplate
import com.ddimitko.beautyhub.service.RabbitMQMessageService
import org.springframework.stereotype.Service as SpringService
import org.springframework.transaction.annotation.Transactional

import java.time.LocalDateTime

@SpringService
@Transactional
class NotificationService {

    @Autowired
    private NotificationRepository notificationRepository

    @Autowired
    private UserRepository userRepository

    @Autowired(required = false)
    private SimpMessagingTemplate messagingTemplate

    @Autowired
    private RabbitMQMessageService rabbitMQMessageService

    /**
     * Create and send a notification to a user
     */
    Notification createNotification(UUID userId, String title, String message, 
                                  NotificationType type = NotificationType.GENERAL, 
                                  String actionUrl = null, Map<String, Object> data = null) {
        User user = userRepository.findById(userId)
            .orElseThrow { new IllegalArgumentException("User not found") }

        Notification notification = new Notification(
            user: user,
            title: title,
            message: message,
            type: type.name(),
            actionUrl: actionUrl,
            data: data ? data.toString() : null,
            seen: false,
            pushSent: false,
            emailSent: false
        )

        notification = notificationRepository.save(notification)

        // Send real-time notification via WebSocket (direct)
        sendRealTimeNotification(userId, notification)

        // Also publish to RabbitMQ for reliable delivery
        rabbitMQMessageService.publishNotification(notification)

        return notification
    }

    /**
     * Get notifications for a user with pagination (last 30 days only)
     */
    Page<Notification> getUserNotifications(UUID userId, int page = 0, int size = 20) {
        User user = userRepository.findById(userId)
            .orElseThrow { new IllegalArgumentException("User not found") }

        // Only get notifications from the last 30 days
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30)

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"))
        return notificationRepository.findByUserAndCreatedAtAfter(user, thirtyDaysAgo, pageable)
    }

    /**
     * Get unread notifications for a user (last 30 days only)
     */
    List<Notification> getUnreadNotifications(UUID userId) {
        User user = userRepository.findById(userId)
            .orElseThrow { new IllegalArgumentException("User not found") }

        // Only get unread notifications from the last 30 days
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30)

        return notificationRepository.findByUserAndSeenFalseAndCreatedAtAfterOrderByCreatedAtDesc(user, thirtyDaysAgo)
    }

    /**
     * Get unread notification count for a user
     */
    long getUnreadCount(UUID userId) {
        User user = userRepository.findById(userId)
            .orElseThrow { new IllegalArgumentException("User not found") }

        return notificationRepository.countUnreadByUser(user)
    }

    /**
     * Mark a notification as read
     */
    Notification markAsRead(UUID notificationId, UUID userId) {
        Notification notification = notificationRepository.findById(notificationId)
            .orElseThrow { new IllegalArgumentException("Notification not found") }

        // Verify the notification belongs to the user
        if (!notification.user.id.equals(userId)) {
            throw new IllegalArgumentException("Access denied")
        }

        if (!notification.seen) {
            notification.markAsSeen()
            notification = notificationRepository.save(notification)

            // Send updated count via WebSocket
            sendUnreadCountUpdate(userId)
        }

        return notification
    }

    /**
     * Mark all notifications as read for a user
     */
    void markAllAsRead(UUID userId) {
        User user = userRepository.findById(userId)
            .orElseThrow { new IllegalArgumentException("User not found") }

        List<Notification> unreadNotifications = notificationRepository.findByUserAndSeenFalse(user)
        
        unreadNotifications.each { notification ->
            notification.markAsSeen()
        }

        if (!unreadNotifications.isEmpty()) {
            notificationRepository.saveAll(unreadNotifications)
            
            // Send updated count via WebSocket
            sendUnreadCountUpdate(userId)
        }
    }

    /**
     * Auto-mark all unread notifications as seen when user opens notification menu
     * This is triggered when user hovers over or opens the notification dropdown
     */
    void autoMarkNotificationsAsSeen(UUID userId) {
        User user = userRepository.findById(userId)
            .orElseThrow { new IllegalArgumentException("User not found") }

        // Only get unread notifications from the last 30 days
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30)
        List<Notification> unreadNotifications = notificationRepository
            .findByUserAndSeenFalseAndCreatedAtAfterOrderByCreatedAtDesc(user, thirtyDaysAgo)

        if (!unreadNotifications.isEmpty()) {
            unreadNotifications.each { notification ->
                notification.markAsSeen()
            }
            notificationRepository.saveAll(unreadNotifications)

            // Send updated unread count via WebSocket
            sendUnreadCountUpdate(userId)
        }
    }

    /**
     * Delete old notifications (older than 30 days)
     */
    void cleanupOldNotifications() {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(30)
        List<Notification> oldNotifications = notificationRepository.findOldNotifications(cutoffDate)
        
        if (!oldNotifications.isEmpty()) {
            notificationRepository.deleteAll(oldNotifications)
        }
    }

    /**
     * Send real-time notification via WebSocket
     */
    private void sendRealTimeNotification(UUID userId, Notification notification) {
        if (messagingTemplate == null) {
            // WebSocket is not available, skip real-time notification
            return
        }

        try {
            Map<String, Object> notificationData = [
                id: notification.id,
                title: notification.title,
                message: notification.message,
                type: notification.type,
                actionUrl: notification.actionUrl,
                createdAt: notification.createdAt,
                unreadCount: getUnreadCount(userId)
            ]

            messagingTemplate.convertAndSendToUser(
                userId.toString(),
                "/queue/notifications",
                notificationData
            )
        } catch (Exception e) {
            // Log error but don't fail notification creation
            println("Failed to send real-time notification: ${e.getMessage()}")
        }
    }

    /**
     * Send updated unread count via WebSocket
     */
    private void sendUnreadCountUpdate(UUID userId) {
        if (messagingTemplate == null) {
            // WebSocket is not available, skip real-time update
            return
        }

        try {
            Map<String, Object> countData = [
                unreadCount: getUnreadCount(userId)
            ]

            messagingTemplate.convertAndSendToUser(
                userId.toString(),
                "/queue/notification-count",
                countData
            )
        } catch (Exception e) {
            // Log error but don't fail the operation
            println("Failed to send unread count update: ${e.getMessage()}")
        }
    }

    /**
     * Create appointment-related notifications
     */
    void createAppointmentNotification(UUID userId, String appointmentId, NotificationType type, Map<String, String> details) {
        String title = ""
        String message = ""
        String actionUrl = "/appointments/${appointmentId}"

        switch (type) {
            case NotificationType.APPOINTMENT_CONFIRMED:
                title = "Appointment Confirmed"
                message = "Your appointment for ${details.serviceName} at ${details.shopName} has been confirmed."
                break
            case NotificationType.APPOINTMENT_CANCELLED:
                title = "Appointment Cancelled"
                message = "Your appointment for ${details.serviceName} at ${details.shopName} has been cancelled."
                break
            case NotificationType.APPOINTMENT_REMINDER:
                title = "Appointment Reminder"
                message = "You have an upcoming appointment for ${details.serviceName} at ${details.shopName} tomorrow."
                break
            case NotificationType.NEW_BOOKING:
                title = "New Booking"
                message = "You have a new booking for ${details.serviceName} from ${details.customerName}."
                actionUrl = "/dashboard/appointments"
                break
            default:
                title = "Appointment Update"
                message = "Your appointment has been updated."
        }

        createNotification(userId, title, message, type, actionUrl, details)
    }

    /**
     * Create payment-related notifications
     */
    void createPaymentNotification(UUID userId, String appointmentId, NotificationType type, Map<String, String> details) {
        String title = ""
        String message = ""
        String actionUrl = "/appointments/${appointmentId}"

        switch (type) {
            case NotificationType.PAYMENT_RECEIVED:
                title = "Payment Received"
                message = "Payment of ${details.amount} has been received for your appointment."
                break
            case NotificationType.PAYMENT_FAILED:
                title = "Payment Failed"
                message = "Payment for your appointment failed. Please try again."
                break
            default:
                title = "Payment Update"
                message = "Your payment status has been updated."
        }

        createNotification(userId, title, message, type, actionUrl, details)
    }
}
