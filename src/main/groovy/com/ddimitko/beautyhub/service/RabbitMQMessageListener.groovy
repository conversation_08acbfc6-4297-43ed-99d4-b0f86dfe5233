package com.ddimitko.beautyhub.service

import com.ddimitko.beautyhub.config.RabbitMQConfig
import groovy.util.logging.Slf4j
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
@Slf4j
class RabbitMQMessageListener {

    @Autowired
    private WebSocketMonitoringService monitoringService

    /**
     * Listen for notification messages and send them via WebSocket
     */
    @RabbitListener(queues = RabbitMQConfig.NOTIFICATION_QUEUE)
    void handleNotificationMessage(Map message) {
        try {
            log.info("Received notification message from RabbitMQ: ${message.id}")

            String userId = message.userId.toString()

            // Check if user is connected before sending
            if (monitoringService.isUserConnected(userId)) {
                // Send notification to specific user via WebSocket
                monitoringService.sendToUser(userId, [
                    type: 'notification',
                    data: message
                ])

                // Also send unread count update
                // Note: In a real implementation, you'd calculate the actual unread count
                monitoringService.sendToUser(userId, [
                    type: 'notification-count',
                    unreadCount: 1 // Placeholder - should be calculated
                ])

                log.info("Sent notification via WebSocket to user: ${userId}")
            } else {
                log.info("User ${userId} not connected, notification queued in RabbitMQ")
                // Message will remain in RabbitMQ queue for later delivery
            }
        } catch (Exception e) {
            log.error("Failed to handle notification message from RabbitMQ", e)
            // Re-throw to trigger RabbitMQ retry mechanism
            throw e
        }
    }

    /**
     * Listen for appointment notification messages
     */
    @RabbitListener(queues = RabbitMQConfig.APPOINTMENT_NOTIFICATION_QUEUE)
    void handleAppointmentNotificationMessage(Map message) {
        try {
            log.info("Received appointment notification from RabbitMQ: ${message.appointmentId}")

            // Send appointment notification to relevant users
            if (message.userId) {
                monitoringService.sendToUser(message.userId.toString(), [
                    type: 'appointment',
                    data: message
                ])
            }

            if (message.employeeId) {
                monitoringService.sendToUser(message.employeeId.toString(), [
                    type: 'appointment',
                    data: message
                ])
            }

            log.info("Sent appointment notification via WebSocket")
        } catch (Exception e) {
            log.error("Failed to handle appointment notification from RabbitMQ", e)
        }
    }

    /**
     * Listen for email notification messages
     */
    @RabbitListener(queues = RabbitMQConfig.EMAIL_NOTIFICATION_QUEUE)
    void handleEmailNotificationMessage(Map message) {
        try {
            log.info("Received email notification from RabbitMQ: ${message.to}")

            // Here you would integrate with an email service
            // For now, just log the message
            log.info("Would send email to: ${message.to}, subject: ${message.subject}")

            // You could also send a WebSocket message to confirm email was sent
            if (message.userId) {
                monitoringService.sendToUser(message.userId.toString(), [
                    type: 'email-status',
                    status: 'sent',
                    emailId: message.id
                ])
            }
        } catch (Exception e) {
            log.error("Failed to handle email notification from RabbitMQ", e)
        }
    }
}
