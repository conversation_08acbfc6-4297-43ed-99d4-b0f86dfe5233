package com.ddimitko.beautyhub.service

import com.ddimitko.beautyhub.dto.SubscriptionRequest
import com.ddimitko.beautyhub.dto.SubscriptionResponse
import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.repository.ShopRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class StripeService {

    @Autowired
    private ShopRepository shopRepository

    /**
     * Creates a fake Stripe subscription for testing purposes
     * In a real implementation, this would integrate with Stripe API
     */
    SubscriptionResponse createSubscription(UUID shopId, SubscriptionRequest request) {
        Shop shop = shopRepository.findById(shopId)
                .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

        // Validate that the shop requires a subscription
        if (!shop.acceptsCardPayments) {
            throw new IllegalArgumentException("Shop does not accept card payments, subscription not required")
        }

        if (shop.subscriptionActive) {
            throw new IllegalArgumentException("Shop already has an active subscription")
        }

        // Generate fake Stripe IDs for testing
        String fakeSubscriptionId = "sub_fake_" + UUID.randomUUID().toString().replace("-", "")[0..15]
        String fakeStripeAccountId = "acct_fake_" + UUID.randomUUID().toString().replace("-", "")[0..15]

        // Update shop with fake Stripe information
        shop.subscriptionId = fakeSubscriptionId
        shop.subscriptionActive = true
        shop.stripeAccountId = fakeStripeAccountId
        shop.stripeOnboardingCompleted = true

        shopRepository.save(shop)

        // Create response
        SubscriptionResponse response = new SubscriptionResponse()
        response.subscriptionId = fakeSubscriptionId
        response.stripeAccountId = fakeStripeAccountId
        response.status = "active"
        response.message = "Subscription created successfully (fake for testing)"
        response.shopId = shopId
        response.planName = request.planName ?: "Basic Plan"
        response.amount = request.amount ?: 2999 // $29.99 in cents
        response.currency = "usd"
        response.interval = "month"
        response.nextBillingDate = new Date(System.currentTimeMillis() + (30L * 24 * 60 * 60 * 1000)) // 30 days from now

        return response
    }

    /**
     * Validates an existing subscription
     */
    boolean validateSubscription(UUID shopId) {
        Shop shop = shopRepository.findById(shopId)
                .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

        return shop.isSubscriptionValid()
    }

    /**
     * Cancels a subscription (fake implementation)
     */
    void cancelSubscription(UUID shopId) {
        Shop shop = shopRepository.findById(shopId)
                .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

        if (!shop.subscriptionActive) {
            throw new IllegalArgumentException("Shop does not have an active subscription")
        }

        shop.subscriptionActive = false
        shop.subscriptionId = null
        shopRepository.save(shop)
    }

    /**
     * Gets subscription details for a shop
     */
    SubscriptionResponse getSubscriptionDetails(UUID shopId) {
        Shop shop = shopRepository.findById(shopId)
                .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

        if (!shop.subscriptionActive || !shop.subscriptionId) {
            throw new RuntimeException("Shop does not have an active subscription")
        }

        SubscriptionResponse response = new SubscriptionResponse()
        response.subscriptionId = shop.subscriptionId
        response.stripeAccountId = shop.stripeAccountId
        response.status = shop.subscriptionActive ? "active" : "inactive"
        response.message = "Subscription details retrieved successfully"
        response.shopId = shopId
        response.planName = "Basic Plan"
        response.amount = 2999
        response.currency = "usd"
        response.interval = "month"
        response.nextBillingDate = new Date(System.currentTimeMillis() + (30L * 24 * 60 * 60 * 1000))

        return response
    }

    /**
     * Simulates Stripe webhook processing for subscription events
     */
    void processWebhookEvent(String eventType, Map<String, Object> eventData) {
        // This would handle real Stripe webhook events in production
        // For testing, we'll just log the event
        println("Processing fake Stripe webhook event: $eventType with data: $eventData")
    }
}
