package com.ddimitko.beautyhub.service

import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.repository.AppointmentRepository
import com.ddimitko.beautyhub.repository.ShopRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class AnalyticsService {

    @Autowired
    private ShopRepository shopRepository

    @Autowired
    private AppointmentRepository appointmentRepository

    @Autowired
    private UserService userService

    Map<String, Object> getOwnerAnalytics(UUID ownerId) {
        User owner = userService.findById(ownerId)
        List<Shop> shops = shopRepository.findByOwner(owner)
        
        if (shops.isEmpty()) {
            return [
                totalRevenue: '$0',
                totalAppointments: 0,
                totalCustomers: 0,
                averageRating: 0.0,
                totalShops: 0,
                revenueGrowth: null,
                appointmentGrowth: null,
                customerGrowth: null
            ]
        }

        // Get shop IDs for queries
        List<UUID> shopIds = shops.collect { it.id }

        // Calculate total appointments across all shops
        long totalAppointments = appointmentRepository.countByShopIdIn(shopIds)

        // Calculate unique customers across all shops
        long totalCustomers = appointmentRepository.countDistinctCustomersByShopIds(shopIds)

        // For now, return mock data for revenue and rating
        // TODO: Implement real calculations when payment system is integrated
        return [
            totalRevenue: '$0', // TODO: Calculate from completed appointments with payments
            totalAppointments: totalAppointments,
            totalCustomers: totalCustomers,
            averageRating: 4.5, // TODO: Calculate from reviews
            totalShops: shops.size(),
            revenueGrowth: null, // TODO: Calculate month-over-month growth
            appointmentGrowth: null, // TODO: Calculate month-over-month growth
            customerGrowth: null // TODO: Calculate month-over-month growth
        ]
    }

    Map<String, Object> getShopAnalytics(UUID shopId, UUID userId) {
        // Verify user has access to this shop
        Shop shop = shopRepository.findById(shopId).orElseThrow {
            new RuntimeException("Shop not found")
        }

        User user = userService.findById(userId)
        
        // Check if user is owner or employee of this shop
        boolean hasAccess = shop.owner.id == userId || 
                           shop.employees?.any { it.user.id == userId }
        
        if (!hasAccess) {
            throw new RuntimeException("Access denied to shop analytics")
        }

        // Calculate shop-specific metrics
        long totalAppointments = appointmentRepository.countByShopId(shopId)
        long totalCustomers = appointmentRepository.countDistinctCustomersByShopId(shopId)

        return [
            shopId: shopId,
            shopName: shop.name,
            totalRevenue: '$0', // TODO: Calculate from payments
            totalAppointments: totalAppointments,
            totalCustomers: totalCustomers,
            averageRating: 4.5, // TODO: Calculate from reviews
            revenueGrowth: null,
            appointmentGrowth: null,
            customerGrowth: null
        ]
    }

    Map<String, Object> getAppointmentStats(UUID shopId, String period, UUID userId) {
        // Verify access (same as above)
        Shop shop = shopRepository.findById(shopId).orElseThrow {
            new RuntimeException("Shop not found")
        }

        User user = userService.findById(userId)
        boolean hasAccess = shop.owner.id == userId || 
                           shop.employees?.any { it.user.id == userId }
        
        if (!hasAccess) {
            throw new RuntimeException("Access denied to shop analytics")
        }

        // TODO: Implement period-based filtering (today, week, month, year)
        long totalAppointments = appointmentRepository.countByShopId(shopId)
        long confirmedAppointments = appointmentRepository.countByShopIdAndStatus(shopId, 'CONFIRMED')
        long completedAppointments = appointmentRepository.countByShopIdAndStatus(shopId, 'COMPLETED')
        long cancelledAppointments = appointmentRepository.countByShopIdAndStatus(shopId, 'CANCELLED')

        return [
            period: period ?: 'all-time',
            totalAppointments: totalAppointments,
            confirmedAppointments: confirmedAppointments,
            completedAppointments: completedAppointments,
            cancelledAppointments: cancelledAppointments,
            completionRate: totalAppointments > 0 ? (completedAppointments / totalAppointments * 100).round(2) : 0
        ]
    }

    Map<String, Object> getRevenueStats(UUID shopId, String period, UUID userId) {
        // Verify access
        Shop shop = shopRepository.findById(shopId).orElseThrow {
            new RuntimeException("Shop not found")
        }

        User user = userService.findById(userId)
        boolean hasAccess = shop.owner.id == userId || 
                           shop.employees?.any { it.user.id == userId }
        
        if (!hasAccess) {
            throw new RuntimeException("Access denied to shop analytics")
        }

        // TODO: Implement real revenue calculations when payment system is integrated
        return [
            period: period ?: 'all-time',
            totalRevenue: '$0',
            averageBookingValue: '$0',
            revenueGrowth: 0,
            topServices: []
        ]
    }

    Map<String, Object> getCustomerStats(UUID shopId, String period, UUID userId) {
        // Verify access
        Shop shop = shopRepository.findById(shopId).orElseThrow {
            new RuntimeException("Shop not found")
        }

        User user = userService.findById(userId)
        boolean hasAccess = shop.owner.id == userId || 
                           shop.employees?.any { it.user.id == userId }
        
        if (!hasAccess) {
            throw new RuntimeException("Access denied to shop analytics")
        }

        long totalCustomers = appointmentRepository.countDistinctCustomersByShopId(shopId)
        long newCustomers = 0 // TODO: Calculate based on first appointment date
        long returningCustomers = 0 // TODO: Calculate customers with multiple appointments

        return [
            period: period ?: 'all-time',
            totalCustomers: totalCustomers,
            newCustomers: newCustomers,
            returningCustomers: returningCustomers,
            customerRetentionRate: 0, // TODO: Calculate retention rate
            averageVisitsPerCustomer: 0 // TODO: Calculate average visits
        ]
    }
}
