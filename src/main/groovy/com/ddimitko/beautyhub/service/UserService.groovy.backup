package com.ddimitko.beautyhub.service

import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.entity.Appointment
import com.ddimitko.beautyhub.enums.UserRole
import com.ddimitko.beautyhub.repository.UserRepository
import com.ddimitko.beautyhub.repository.AppointmentRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class UserService {

    @Autowired
    private UserRepository userRepository

    @Autowired
    private PasswordEncoder passwordEncoder

    @Autowired
    private AppointmentRepository appointmentRepository

    User createUser(String email, String password, String firstName, String lastName, UserRole role = UserRole.USER) {
        if (userRepository.existsByEmail(email)) {
            throw new IllegalArgumentException("User with email $email already exists")
        }

        User user = new User()
        user.email = email
        user.password = passwordEncoder.encode(password)
        user.firstName = firstName
        user.lastName = lastName
        // Assign placeholder avatar using initials
        user.avatar = generatePlaceholderAvatar(firstName, lastName)
        user.role = role
        // Assign placeholder avatar using initials
        user.avatar = generatePlaceholderAvatar(firstName, lastName)

        return userRepository.save(user)
    }

    private String generatePlaceholderAvatar(String firstName, String lastName) {
        String initials = "${firstName?.charAt(0) ?: ''}${lastName?.charAt(0) ?: ''}".toUpperCase()
        // Using a simple avatar service that generates avatars based on initials
        return "https://ui-avatars.com/api/?name=${initials}&background=6366f1&color=ffffff&size=128"
    }

    User findByEmail(String email) {
        return userRepository.findByEmail(email)
                .orElseThrow { new RuntimeException("User not found with email: $email") }
    }

    User findById(UUID id) {
        return userRepository.findById(id)
                .orElseThrow { new RuntimeException("User not found with id: $id") }
    }

    User updateUser(UUID id, User updatedUser) {
        User existingUser = findById(id)
        
        if (updatedUser.firstName) existingUser.firstName = updatedUser.firstName
        if (updatedUser.lastName) existingUser.lastName = updatedUser.lastName
        if (updatedUser.phone) existingUser.phone = updatedUser.phone
        if (updatedUser.avatar) existingUser.avatar = updatedUser.avatar

        return userRepository.save(existingUser)
    }

    User changePassword(UUID id, String oldPassword, String newPassword) {
        User user = findById(id)
        
        if (!passwordEncoder.matches(oldPassword, user.password)) {
            throw new IllegalArgumentException("Current password is incorrect")
        }

        user.password = passwordEncoder.encode(newPassword)
        return userRepository.save(user)
    }

    void deleteUser(UUID id) {
        User user = findById(id)
        user.enabled = false
        userRepository.save(user)
    }

    List<User> findAllUsers() {
        return userRepository.findAll()
    }

    List<User> findUsersByRole(UserRole role) {
        return userRepository.findByRole(role)
    }

    boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email)
    }

    User verifyEmail(UUID id) {
        User user = findById(id)
        user.emailVerified = true
        return userRepository.save(user)
    }

    User upgradeToOwner(UUID id) {
        User user = findById(id)
        user.role = UserRole.OWNER
        return userRepository.save(user)
    }

    User upgradeToEmployee(UUID id) {
        User user = findById(id)
        if (user.role == UserRole.USER) {
            user.role = UserRole.EMPLOYEE
        }
        return userRepository.save(user)
    }

    User enableUser(UUID id) {
        User user = findById(id)
        user.enabled = true
        return userRepository.save(user)
    }

    User disableUser(UUID id) {
        User user = findById(id)
        user.enabled = false
        return userRepository.save(user)
    }

    /**
     * Links guest appointments to a user after email verification
     */
    void linkGuestAppointments(User user) {
        List<Appointment> guestAppointments = appointmentRepository.findByGuestEmailAndUserIsNull(user.email)

        guestAppointments.each { appointment ->
            appointment.user = user
            // Keep guest information for audit trail
            appointmentRepository.save(appointment)
        }
    }
}

    private String generatePlaceholderAvatar(String firstName, String lastName) {
        String initials = "${firstName?.charAt(0) ?: ''}${lastName?.charAt(0) ?: ''}".toUpperCase()
        // Using a simple avatar service that generates avatars based on initials
        return "https://ui-avatars.com/api/?name=${initials}&background=6366f1&color=ffffff&size=128"
    }

    void changePassword(UUID userId, String currentPassword, String newPassword) {
        User user = findById(userId)
        
        if (!passwordEncoder.matches(currentPassword, user.password)) {
            throw new IllegalArgumentException("Current password is incorrect")
        }
        
        user.password = passwordEncoder.encode(newPassword)
        userRepository.save(user)
    }
