package com.ddimitko.beautyhub.service

import com.ddimitko.beautyhub.dto.EmployeeInvitationRequest
import com.ddimitko.beautyhub.dto.EmployeeCreationRequest
import com.ddimitko.beautyhub.dto.EmployeeCreationWithUserRequest
import com.ddimitko.beautyhub.entity.Employee
import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.enums.UserRole
import com.ddimitko.beautyhub.repository.EmployeeRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
@Transactional
class EmployeeService {

    @Autowired
    private EmployeeRepository employeeRepository

    @Autowired
    private UserService userService

    @Autowired
    private ShopService shopService

    /**
     * Creates an employee profile for an existing user
     */
    Employee createEmployee(UUID shopId, UUID userId, EmployeeCreationRequest request) {
        Shop shop = shopService.findById(shopId)
        User user = userService.findById(userId)

        // Check if user is already an employee at this shop
        if (employeeRepository.existsByUserAndShop(user, shop)) {
            throw new IllegalArgumentException("User is already an employee at this shop")
        }

        // Upgrade user to employee role if they're just a regular user
        if (user.role == UserRole.USER) {
            user = userService.upgradeToEmployee(userId)
        }

        Employee employee = new Employee()
        employee.user = user
        employee.shop = shop
        employee.bio = request.bio
        employee.specialties = request.specialties
        employee.yearsExperience = request.yearsExperience
        employee.hourlyRate = request.hourlyRate
        employee.commissionRate = request.commissionRate
        employee.active = true
        employee.hireDate = LocalDateTime.now()

        return employeeRepository.save(employee)
    }

    /**
     * Invites an existing user to become an employee by email
     */
    Employee inviteUserAsEmployee(UUID shopId, String userEmail, EmployeeCreationRequest request) {
        try {
            User user = userService.findByEmail(userEmail)
            return createEmployee(shopId, user.id, request)
        } catch (RuntimeException e) {
            throw new IllegalArgumentException("User with email $userEmail not found. They must register first.")
        }
    }

    /**
     * Creates a new user and employee profile in one operation
     */
    Employee createEmployeeWithNewUser(UUID shopId, EmployeeCreationWithUserRequest request) {
        // Check if user with this email already exists
        try {
            userService.findByEmail(request.email)
            throw new IllegalArgumentException("User with email ${request.email} already exists. Use invite existing user instead.")
        } catch (RuntimeException e) {
            // User doesn't exist, which is what we want for creating new user
        }

        // Create new user
        User newUser = userService.createUser(
            request.email,
            request.password,
            request.firstName,
            request.lastName
        )

        // Create employee profile
        EmployeeCreationRequest employeeRequest = new EmployeeCreationRequest()
        employeeRequest.bio = request.bio
        employeeRequest.specialties = request.specialties
        employeeRequest.yearsExperience = request.yearsExperience
        employeeRequest.hourlyRate = request.hourlyRate
        employeeRequest.commissionRate = request.commissionRate

        return createEmployee(shopId, newUser.id, employeeRequest)
    }

    /**
     * Gets all employees for a shop
     */
    List<Employee> getEmployeesByShop(UUID shopId) {
        Shop shop = shopService.findById(shopId)
        return employeeRepository.findByShop(shop)
    }

    /**
     * Gets active employees for a shop
     */
    List<Employee> getActiveEmployeesByShop(UUID shopId) {
        Shop shop = shopService.findById(shopId)
        return employeeRepository.findActiveEmployeesByShop(shop)
    }

    /**
     * Gets employee profiles for a user (can work at multiple shops)
     */
    List<Employee> getEmployeesByUser(UUID userId) {
        User user = userService.findById(userId)
        return employeeRepository.findByUser(user)
    }

    /**
     * Gets employee profile for a user at a specific shop
     */
    Employee getEmployeeByUserAndShop(UUID userId, UUID shopId) {
        User user = userService.findById(userId)
        Shop shop = shopService.findById(shopId)
        return employeeRepository.findByUserAndShop(user, shop)
                .orElseThrow { new RuntimeException("Employee profile not found for user at this shop") }
    }

    /**
     * Updates employee information
     */
    Employee updateEmployee(UUID employeeId, EmployeeCreationRequest request) {
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow { new RuntimeException("Employee not found with id: $employeeId") }

        if (request.bio != null) employee.bio = request.bio
        if (request.specialties != null) employee.specialties = request.specialties
        if (request.yearsExperience != null) employee.yearsExperience = request.yearsExperience
        if (request.hourlyRate != null) employee.hourlyRate = request.hourlyRate
        if (request.commissionRate != null) employee.commissionRate = request.commissionRate

        return employeeRepository.save(employee)
    }

    /**
     * Deactivates an employee (soft delete)
     */
    void deactivateEmployee(UUID employeeId) {
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow { new RuntimeException("Employee not found with id: $employeeId") }
        
        employee.active = false
        employeeRepository.save(employee)
    }

    /**
     * Checks if a user is an employee at a specific shop
     */
    boolean isEmployeeAtShop(UUID userId, UUID shopId) {
        try {
            User user = userService.findById(userId)
            Shop shop = shopService.findById(shopId)
            return employeeRepository.existsByUserAndShop(user, shop)
        } catch (RuntimeException e) {
            return false
        }
    }

    /**
     * Gets all shops where a user is an employee
     */
    List<Shop> getShopsWhereUserIsEmployee(UUID userId) {
        User user = userService.findById(userId)
        return employeeRepository.findByUser(user)
                .collect { it.shop }
                .findAll { it.active }
    }

    /**
     * Validates that an employee cannot book appointments at their own shop
     */
    void validateEmployeeCannotBookAtOwnShop(UUID userId, UUID shopId) {
        if (isEmployeeAtShop(userId, shopId)) {
            throw new IllegalArgumentException("Employees cannot book appointments at their own shop")
        }
    }

    /**
     * Gets employee count for a shop
     */
    long getEmployeeCountByShop(UUID shopId) {
        Shop shop = shopService.findById(shopId)
        return employeeRepository.countActiveEmployeesByShop(shop)
    }

    /**
     * Checks if user has any employee profiles
     */
    boolean hasEmployeeProfile(UUID userId) {
        User user = userService.findById(userId)
        return !employeeRepository.findByUser(user).isEmpty()
    }
}
