package com.ddimitko.beautyhub.service

import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.entity.Appointment
import com.ddimitko.beautyhub.enums.UserRole
import com.ddimitko.beautyhub.repository.UserRepository
import com.ddimitko.beautyhub.repository.AppointmentRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
@Transactional
class UserService {

    @Autowired
    private UserRepository userRepository

    @Autowired
    private PasswordEncoder passwordEncoder

    @Autowired
    private AppointmentRepository appointmentRepository

    User createUser(String email, String password, String firstName, String lastName, UserRole role = UserRole.USER) {
        if (userRepository.existsByEmail(email)) {
            throw new IllegalArgumentException("User with email $email already exists")
        }

        User user = new User()
        user.email = email
        user.password = passwordEncoder.encode(password)
        user.firstName = firstName
        user.lastName = lastName
        // Assign placeholder avatar using initials
        user.avatar = generatePlaceholderAvatar(firstName, lastName)
        user.role = role
        // Assign placeholder avatar using initials
        user.avatar = generatePlaceholderAvatar(firstName, lastName)

        return userRepository.save(user)
    }

    private String generatePlaceholderAvatar(String firstName, String lastName) {
        String initials = "${firstName?.charAt(0) ?: ''}${lastName?.charAt(0) ?: ''}".toUpperCase()
        // Using a simple avatar service that generates avatars based on initials
        return "https://ui-avatars.com/api/?name=${initials}&background=6366f1&color=ffffff&size=128"
    }

    User updateUser(UUID userId, User updatedUser) {
        User user = findById(userId)
        
        if (updatedUser.firstName != null) {
            user.firstName = updatedUser.firstName
        }
        if (updatedUser.lastName != null) {
            user.lastName = updatedUser.lastName
        }
        if (updatedUser.phone != null) {
            user.phone = updatedUser.phone
        }
        if (updatedUser.avatar != null) {
            user.avatar = updatedUser.avatar
        }
        
        user.updatedAt = LocalDateTime.now()
        return userRepository.save(user)
    }

    User findById(UUID userId) {
        return userRepository.findById(userId)
            .orElseThrow(() -> new IllegalArgumentException("User not found with id: ${userId}"))
    }

    User findByEmail(String email) {
        return userRepository.findByEmail(email)
            .orElse(null)
    }

    boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email)
    }

    User upgradeToOwner(UUID userId) {
        User user = findById(userId)
        user.role = UserRole.OWNER
        return userRepository.save(user)
    }

    User upgradeToEmployee(UUID userId) {
        User user = findById(userId)
        if (user.role == UserRole.USER) {
            user.role = UserRole.EMPLOYEE
        }
        return userRepository.save(user)
    }

    void changePassword(UUID userId, String currentPassword, String newPassword) {
        User user = findById(userId)
        
        if (!passwordEncoder.matches(currentPassword, user.password)) {
            throw new IllegalArgumentException("Current password is incorrect")
        }
        
        user.password = passwordEncoder.encode(newPassword)
        userRepository.save(user)
    }


    /**
     * Links guest appointments to a user after email verification
     */
    void linkGuestAppointments(User user) {
        List<Appointment> guestAppointments = appointmentRepository.findByGuestEmailAndUserIsNull(user.email)

        guestAppointments.each { appointment ->
            appointment.user = user
            // Keep guest information for audit trail
            appointmentRepository.save(appointment)
        }
    }
}
