package com.ddimitko.beautyhub.controller

import com.ddimitko.beautyhub.dto.UserProfileUpdateRequest
import com.ddimitko.beautyhub.dto.ChangePasswordRequest
import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.security.CustomUserPrincipal
import com.ddimitko.beautyhub.service.UserService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

import jakarta.validation.Valid

@RestController
@RequestMapping("/api/user")
@CrossOrigin(origins = "*", maxAge = 3600)
class UserController {

    @Autowired
    private UserService userService

    @GetMapping("/profile")
    @PreAuthorize("hasAnyRole('USER', 'EMPLOYEE', 'OWNER')")
    ResponseEntity<?> getUserProfile(@AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            User user = userService.findById(userPrincipal.getId())
            
            return ResponseEntity.ok([
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                phone: user.phone,
                avatar: user.avatar,
                role: user.role.name(),
                emailVerified: user.emailVerified,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve user profile",
                message: e.getMessage()
            ])
        }
    }

    @PutMapping("/profile")
    @PreAuthorize("hasAnyRole('USER', 'EMPLOYEE', 'OWNER')")
    ResponseEntity<?> updateUserProfile(
            @Valid @RequestBody UserProfileUpdateRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            User updatedUser = new User()
            updatedUser.firstName = request.firstName
            updatedUser.lastName = request.lastName
            updatedUser.phone = request.phone
            updatedUser.avatar = request.avatar

            User user = userService.updateUser(userPrincipal.getId(), updatedUser)
            
            return ResponseEntity.ok([
                message: "Profile updated successfully",
                user: [
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    phone: user.phone,
                    avatar: user.avatar,
                    role: user.role.name(),
                    emailVerified: user.emailVerified,
                    updatedAt: user.updatedAt
                ]
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Profile update failed",
                message: e.getMessage()
            ])
        }
    }

    @PutMapping("/change-password")
    @PreAuthorize("hasAnyRole('USER', 'EMPLOYEE', 'OWNER')")
    ResponseEntity<?> changePassword(
            @Valid @RequestBody ChangePasswordRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            userService.changePassword(
                userPrincipal.getId(),
                request.currentPassword,
                request.newPassword
            )
            
            return ResponseEntity.ok([
                message: "Password changed successfully"
            ])
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Password change failed",
                message: e.getMessage()
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Password change failed",
                message: "An unexpected error occurred"
            ])
        }
    }
}
