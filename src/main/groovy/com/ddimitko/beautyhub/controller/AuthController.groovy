package com.ddimitko.beautyhub.controller

import com.ddimitko.beautyhub.config.JwtConfig
import com.ddimitko.beautyhub.dto.LoginRequest
import com.ddimitko.beautyhub.dto.LoginResponse
import com.ddimitko.beautyhub.dto.RegisterRequest
import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.enums.UserRole
import com.ddimitko.beautyhub.security.CustomUserPrincipal
import com.ddimitko.beautyhub.service.UserService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.ResponseEntity
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*

import jakarta.servlet.http.HttpServletRequest
import jakarta.validation.Valid

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*", maxAge = 3600)
class AuthController {

    @Autowired
    private AuthenticationManager authenticationManager

    @Autowired
    private UserService userService

    @Autowired
    private JwtConfig jwtConfig

    @PostMapping("/login")
    ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    loginRequest.email,
                    loginRequest.password
                )
            )

            SecurityContextHolder.getContext().setAuthentication(authentication)
            CustomUserPrincipal userPrincipal = (CustomUserPrincipal) authentication.getPrincipal()
            String jwt = jwtConfig.generateToken(userPrincipal)

            User user = userService.findById(userPrincipal.getId())
            return ResponseEntity.ok(new LoginResponse(
                token: jwt,
                type: "Bearer",
                id: userPrincipal.getId(),
                email: userPrincipal.getEmail(),
                firstName: userPrincipal.getFirstName(),
                lastName: userPrincipal.getLastName(),
                avatar: user.avatar,
                roles: userPrincipal.getAuthorities().collect { it.getAuthority() }
            ))
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Invalid credentials",
                message: e.getMessage()
            ])
        }
    }

    @PostMapping("/register")
    ResponseEntity<?> registerUser(@Valid @RequestBody RegisterRequest registerRequest) {
        try {
            if (userService.existsByEmail(registerRequest.email)) {
                return ResponseEntity.badRequest().body([
                    error: "Email is already taken!"
                ])
            }

            User user = userService.createUser(
                registerRequest.email,
                registerRequest.password,
                registerRequest.firstName,
                registerRequest.lastName,
                UserRole.USER
            )

            // Automatically authenticate the user after registration
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    registerRequest.email,
                    registerRequest.password
                )
            )

            SecurityContextHolder.getContext().setAuthentication(authentication)
            CustomUserPrincipal userPrincipal = (CustomUserPrincipal) authentication.getPrincipal()
            String jwt = jwtConfig.generateToken(userPrincipal)

            return ResponseEntity.ok(new LoginResponse(
                token: jwt,
                type: "Bearer",
                id: userPrincipal.getId(),
                email: userPrincipal.getEmail(),
                firstName: userPrincipal.getFirstName(),
                lastName: userPrincipal.getLastName(),
                avatar: user.avatar,
                roles: userPrincipal.getAuthorities().collect { it.getAuthority() }
            ))
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Registration failed",
                message: e.getMessage()
            ])
        }
    }



    @PostMapping("/refresh")
    ResponseEntity<?> refreshToken(HttpServletRequest request) {
        try {
            String jwt = getJwtFromRequest(request)

            if (!jwt) {
                return ResponseEntity.status(401).body([
                    error: "No token provided"
                ])
            }

            // Try to extract username even from expired token
            String userEmail
            try {
                userEmail = jwtConfig.extractUsernameIgnoreExpiration(jwt)
            } catch (Exception e) {
                // If we can't extract username, token is malformed
                return ResponseEntity.status(401).body([
                    error: "Invalid token format"
                ])
            }

            if (!userEmail) {
                return ResponseEntity.status(401).body([
                    error: "Invalid token - no user email"
                ])
            }

            User user = userService.findByEmail(userEmail)
            if (!user) {
                return ResponseEntity.status(401).body([
                    error: "User not found"
                ])
            }

            // Create new token
            CustomUserPrincipal userPrincipal = CustomUserPrincipal.create(user)
            String newJwt = jwtConfig.generateToken(userPrincipal)

            return ResponseEntity.ok(new LoginResponse(
                token: newJwt,
                type: "Bearer",
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                avatar: user.avatar,
                roles: ["ROLE_${user.role.name()}".toString()]
            ))
        } catch (Exception e) {
            return ResponseEntity.status(401).body([
                error: "Token refresh failed",
                message: e.getMessage()
            ])
        }
    }

    @PostMapping("/validate")
    ResponseEntity<?> validateToken(HttpServletRequest request) {
        try {
            String jwt = getJwtFromRequest(request)

            if (!jwt) {
                return ResponseEntity.status(401).body([
                    valid: false,
                    error: "No token provided"
                ])
            }

            // First check if token is structurally valid and not expired
            if (!jwtConfig.validateToken(jwt)) {
                return ResponseEntity.status(401).body([
                    valid: false,
                    error: "Invalid or expired token"
                ])
            }

            String userEmail = jwtConfig.extractUsername(jwt)
            User user
            try {
                user = userService.findByEmail(userEmail)
            } catch (RuntimeException e) {
                return ResponseEntity.status(401).body([
                    valid: false,
                    error: "User not found"
                ])
            }

            if (!user.enabled) {
                return ResponseEntity.status(401).body([
                    valid: false,
                    error: "User account is disabled"
                ])
            }

            return ResponseEntity.ok([
                valid: true,
                user: [
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    avatar: user.avatar,
                    roles: ["ROLE_${user.role.name()}".toString()]
                ]
            ])
        } catch (Exception e) {
            return ResponseEntity.status(401).body([
                valid: false,
                error: "Token validation failed",
                message: e.getMessage()
            ])
        }
    }

    @PostMapping("/logout")
    ResponseEntity<?> logoutUser() {
        SecurityContextHolder.clearContext()
        return ResponseEntity.ok([
            message: "User logged out successfully!"
        ])
    }

    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization")
        if (bearerToken && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7)
        }
        return null
    }
}
