package com.ddimitko.beautyhub.controller

import com.ddimitko.beautyhub.dto.ScheduleSlotRequest
import com.ddimitko.beautyhub.dto.ScheduleSlotResponse
import com.ddimitko.beautyhub.entity.Employee
import com.ddimitko.beautyhub.entity.ScheduleSlot
import com.ddimitko.beautyhub.enums.DayOfWeek
import com.ddimitko.beautyhub.repository.EmployeeRepository
import com.ddimitko.beautyhub.repository.ScheduleSlotRepository
import com.ddimitko.beautyhub.security.CustomUserPrincipal
import com.ddimitko.beautyhub.service.EmployeeService
import jakarta.validation.Valid
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Pageable
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

import java.time.LocalTime

@RestController
@RequestMapping("/api/schedules")
@CrossOrigin(origins = "*", maxAge = 3600)
class ScheduleController {

    @Autowired
    private ScheduleSlotRepository scheduleSlotRepository

    @Autowired
    private EmployeeRepository employeeRepository

    @Autowired
    private EmployeeService employeeService

    /**
     * Get employee schedule
     */
    @GetMapping("/employee/{employeeId}")
    @PreAuthorize("hasAnyRole('OWNER', 'EMPLOYEE')")
    ResponseEntity<?> getEmployeeSchedule(
            @PathVariable("employeeId") UUID employeeId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            Employee employee = employeeRepository.findByIdWithUserAndShop(employeeId)
                    .orElseThrow { new RuntimeException("Employee not found") }

            // Verify access - owner of shop or the employee themselves
            boolean hasAccess = employee.shop.owner.id.equals(userPrincipal.getId()) ||
                               employee.user.id.equals(userPrincipal.getId())

            if (!hasAccess) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only view schedules for your own employees or your own schedule"
                ])
            }

            List<ScheduleSlot> scheduleSlots = scheduleSlotRepository.findByEmployeeAndActiveTrue(employee)
            
            // Group by day of week
            Map<String, List<Map<String, Object>>> scheduleByDay = [:]
            DayOfWeek.values().each { day ->
                scheduleByDay[day.name()] = []
            }

            scheduleSlots.each { slot ->
                scheduleByDay[slot.dayOfWeek.name()].add([
                    id: slot.id,
                    dayOfWeek: slot.dayOfWeek.name(),
                    startTime: slot.startTime.toString(),
                    endTime: slot.endTime.toString(),
                    formattedTimeRange: slot.getFormattedTimeRange(),
                    durationMinutes: slot.getDurationMinutes(),
                    active: slot.active
                ])
            }

            return ResponseEntity.ok([
                employeeId: employee.id,
                employeeName: employee.fullName,
                schedule: scheduleByDay
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve schedule",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Create or update employee schedule slots
     */
    @PostMapping("/employee/{employeeId}")
    @PreAuthorize("hasAnyRole('OWNER', 'EMPLOYEE')")
    ResponseEntity<?> updateEmployeeSchedule(
            @PathVariable("employeeId") UUID employeeId,
            @Valid @RequestBody List<ScheduleSlotRequest> scheduleRequests,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            Employee employee = employeeRepository.findByIdWithUserAndShop(employeeId)
                    .orElseThrow { new RuntimeException("Employee not found") }

            // Verify access - owner of shop or the employee themselves
            boolean hasAccess = employee.shop.owner.id.equals(userPrincipal.getId()) ||
                               employee.user.id.equals(userPrincipal.getId())

            if (!hasAccess) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only manage schedules for your own employees or your own schedule"
                ])
            }

            // Deactivate existing schedule slots
            List<ScheduleSlot> existingSlots = scheduleSlotRepository.findByEmployeeAndActiveTrue(employee)
            existingSlots.each { slot ->
                slot.active = false
                scheduleSlotRepository.save(slot)
            }

            // Create new schedule slots
            List<ScheduleSlot> newSlots = []
            scheduleRequests.each { request ->
                ScheduleSlot slot = new ScheduleSlot()
                slot.employee = employee
                slot.dayOfWeek = DayOfWeek.valueOf(request.dayOfWeek)
                slot.startTime = LocalTime.parse(request.startTime)
                slot.endTime = LocalTime.parse(request.endTime)
                slot.active = true
                
                newSlots.add(scheduleSlotRepository.save(slot))
            }

            return ResponseEntity.ok([
                message: "Schedule updated successfully",
                employeeId: employee.id,
                employeeName: employee.fullName,
                slotsCreated: newSlots.size()
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to update schedule",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Delete a specific schedule slot
     */
    @DeleteMapping("/slot/{slotId}")
    @PreAuthorize("hasAnyRole('OWNER', 'EMPLOYEE')")
    ResponseEntity<?> deleteScheduleSlot(
            @PathVariable("slotId") UUID slotId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            ScheduleSlot slot = scheduleSlotRepository.findByIdWithEmployeeAndShop(slotId)
                    .orElseThrow { new RuntimeException("Schedule slot not found") }

            // Verify access
            boolean hasAccess = slot.employee.shop.owner.id.equals(userPrincipal.getId()) ||
                               slot.employee.user.id.equals(userPrincipal.getId())

            if (!hasAccess) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only delete your own schedule slots"
                ])
            }

            slot.active = false
            scheduleSlotRepository.save(slot)

            return ResponseEntity.ok([
                message: "Schedule slot deleted successfully"
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to delete schedule slot",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Get employee schedule for public appointment booking (no authentication required)
     */
    @GetMapping("/public/employee/{employeeId}")
    ResponseEntity<?> getPublicEmployeeSchedule(@PathVariable("employeeId") UUID employeeId) {
        try {
            Employee employee = employeeRepository.findByIdWithUserAndShop(employeeId)
                    .orElseThrow { new RuntimeException("Employee not found") }

            List<ScheduleSlot> scheduleSlots = scheduleSlotRepository.findByEmployeeAndActiveTrue(employee)

            // Return simplified schedule data for public use
            List<Map<String, Object>> publicSchedule = scheduleSlots.collect { slot ->
                [
                    dayOfWeek: slot.dayOfWeek.name(),
                    startTime: slot.startTime.toString(),
                    endTime: slot.endTime.toString(),
                    active: slot.active
                ]
            }

            return ResponseEntity.ok([
                employeeId: employee.id,
                schedule: publicSchedule
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve employee schedule",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Get my schedule (for employees)
     */
    @GetMapping("/my-schedule")
    @PreAuthorize("hasRole('EMPLOYEE')")
    ResponseEntity<?> getMySchedule(@AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            List<Employee> employeeProfiles = employeeService.getEmployeesByUser(userPrincipal.getId())
            
            List<Map<String, Object>> schedules = []
            employeeProfiles.each { employee ->
                List<ScheduleSlot> scheduleSlots = scheduleSlotRepository.findByEmployeeAndActiveTrue(employee)
                
                Map<String, List<Map<String, Object>>> scheduleByDay = [:]
                DayOfWeek.values().each { day ->
                    scheduleByDay[day.name()] = []
                }

                scheduleSlots.each { slot ->
                    scheduleByDay[slot.dayOfWeek.name()].add([
                        id: slot.id,
                        dayOfWeek: slot.dayOfWeek.name(),
                        startTime: slot.startTime.toString(),
                        endTime: slot.endTime.toString(),
                        formattedTimeRange: slot.getFormattedTimeRange(),
                        durationMinutes: slot.getDurationMinutes(),
                        active: slot.active
                    ])
                }

                schedules.add([
                    employeeId: employee.id,
                    shopId: employee.shop.id,
                    shopName: employee.shop.name,
                    schedule: scheduleByDay
                ])
            }

            return ResponseEntity.ok(schedules)
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve your schedule",
                message: e.getMessage()
            ])
        }
    }
}
