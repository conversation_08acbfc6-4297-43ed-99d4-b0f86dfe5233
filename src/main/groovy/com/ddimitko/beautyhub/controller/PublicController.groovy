package com.ddimitko.beautyhub.controller

import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.entity.Employee
import com.ddimitko.beautyhub.entity.Service
import com.ddimitko.beautyhub.entity.ScheduleSlot
import com.ddimitko.beautyhub.dto.ShopSummaryDTO
import com.ddimitko.beautyhub.service.ShopService
import com.ddimitko.beautyhub.service.EmployeeService
import com.ddimitko.beautyhub.repository.ServiceRepository
import com.ddimitko.beautyhub.repository.ScheduleSlotRepository
import com.ddimitko.beautyhub.enums.DayOfWeek

import java.time.LocalTime
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.transaction.annotation.Transactional

@RestController
@RequestMapping("/api/public")
@CrossOrigin(origins = "*", maxAge = 3600)
class PublicController {

    @Autowired
    private ShopService shopService

    @Autowired
    private EmployeeService employeeService

    @Autowired
    private ServiceRepository serviceRepository

    @Autowired
    private ScheduleSlotRepository scheduleSlotRepository

    @GetMapping("/shops")
    @Transactional(readOnly = true)
    ResponseEntity<?> getShops(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "sortBy", defaultValue = "name") String sortBy,
            @RequestParam(value = "sortDir", defaultValue = "asc") String sortDir,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "city", required = false) String city,
            @RequestParam(value = "minRating", required = false) Double minRating,
            @RequestParam(value = "acceptsCard", required = false) Boolean acceptsCard) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending()
        Pageable pageable = PageRequest.of(page, size, sort)
        
        // For now, let's just get all active shops without filtering
        Page<Shop> shops = shopService.findActiveShops(pageable)

        // Convert to DTOs to avoid Hibernate proxy serialization issues
        List<ShopSummaryDTO> shopDTOs = shops.content.collect { shop ->
            ShopSummaryDTO.fromShop(shop)
        }

        Map<String, Object> response = [
            content: shopDTOs,
            totalElements: shops.totalElements,
            totalPages: shops.totalPages,
            size: shops.size,
            number: shops.number,
            first: shops.first,
            last: shops.last,
            empty: shops.empty
        ]

        return ResponseEntity.ok(response)
    }

    @GetMapping("/shops/{id}")
    @Transactional(readOnly = true)
    ResponseEntity<?> getShop(@PathVariable("id") UUID id) {
        try {
            Shop shop = shopService.findById(id)
            ShopSummaryDTO shopDTO = ShopSummaryDTO.fromShop(shop)
            return ResponseEntity.ok(shopDTO)
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build()
        }
    }

    @GetMapping("/shops/{id}/employees")
    @Transactional(readOnly = true)
    ResponseEntity<?> getShopEmployees(@PathVariable("id") UUID id) {
        try {
            List<Employee> employees = employeeService.getActiveEmployeesByShop(id)
            return ResponseEntity.ok([
                data: employees.collect { employee ->
                    [
                        id: employee.id,
                        name: employee.fullName,
                        bio: employee.bio,
                        specialties: employee.specialties,
                        yearsExperience: employee.yearsExperience,
                        services: employee.services?.collect { service ->
                            [
                                id: service.id,
                                name: service.name,
                                price: service.price,
                                durationMinutes: service.durationMinutes
                            ]
                        } ?: []
                    ]
                }
            ])
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build()
        }
    }

    @GetMapping("/shops/{id}/services")
    @Transactional(readOnly = true)
    ResponseEntity<?> getShopServices(@PathVariable("id") UUID id) {
        try {
            Shop shop = shopService.findById(id)
            // Use repository method instead of lazy-loaded collection
            List<Service> services = serviceRepository.findByShopAndActiveTrue(shop)
            return ResponseEntity.ok([
                data: services.collect { service ->
                    [
                        id: service.id,
                        name: service.name,
                        description: service.description,
                        price: service.price,
                        durationMinutes: service.durationMinutes,
                        depositAmount: service.depositAmount,
                        category: service.category,
                        active: service.active,
                        employeeId: service.employee?.id,
                        employeeName: service.employee?.fullName,
                        shopId: service.shop?.id,
                        shopName: service.shop?.name,
                        formattedPrice: service.formattedPrice,
                        formattedDuration: service.formattedDuration,
                        createdAt: service.createdAt
                    ]
                }
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve shop services",
                message: e.getMessage()
            ])
        }
    }

    @GetMapping("/shops/{id}/hours")
    @Transactional(readOnly = true)
    ResponseEntity<?> getShopHours(@PathVariable("id") UUID id) {
        try {
            Shop shop = shopService.findById(id)

            // Use the same method as the employees endpoint to ensure consistency
            List<Employee> employees = employeeService.getActiveEmployeesByShop(shop.id)

            if (employees.isEmpty()) {
                return ResponseEntity.ok([data: []])
            }

            // Aggregate schedule slots by day of week
            Map<DayOfWeek, List<ScheduleSlot>> schedulesByDay = [:]

            employees.each { employee ->
                // Get schedule slots for each employee using repository
                List<ScheduleSlot> employeeSlots = scheduleSlotRepository.findByEmployeeAndActiveTrue(employee)
                employeeSlots.each { slot ->
                    if (!schedulesByDay[slot.dayOfWeek]) {
                        schedulesByDay[slot.dayOfWeek] = []
                    }
                    schedulesByDay[slot.dayOfWeek].add(slot)
                }
            }

            // Create shop hours by finding earliest start and latest end for each day
            List<Map> shopHours = []
            DayOfWeek.values().each { day ->
                List<ScheduleSlot> daySlots = schedulesByDay[day] ?: []

                if (daySlots.isEmpty()) {
                    shopHours.add([
                        dayOfWeek: day.name(),
                        closed: true
                    ])
                } else {
                    LocalTime earliestStart = daySlots.collect { it.startTime }.min()
                    LocalTime latestEnd = daySlots.collect { it.endTime }.max()

                    shopHours.add([
                        dayOfWeek: day.name(),
                        closed: false,
                        openTime: earliestStart.toString(),
                        closeTime: latestEnd.toString()
                    ])
                }
            }

            return ResponseEntity.ok([
                data: shopHours,
                debug: [
                    employeeCount: employees.size(),
                    employees: employees.collect { [id: it.id, name: it.fullName] },
                    schedulesByDay: schedulesByDay.collectEntries { k, v -> [k.name(), v.size()] }
                ]
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve shop hours",
                message: e.getMessage()
            ])
        }
    }
}
