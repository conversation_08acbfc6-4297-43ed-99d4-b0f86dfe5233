package com.ddimitko.beautyhub.controller

import com.ddimitko.beautyhub.dto.PaymentIntentRequest
import com.ddimitko.beautyhub.dto.PaymentIntentResponse
import com.ddimitko.beautyhub.security.CustomUserPrincipal
import com.ddimitko.beautyhub.service.PaymentService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

import jakarta.validation.Valid

@RestController
@RequestMapping("/api/payments")
@CrossOrigin(origins = "*", maxAge = 3600)
class PaymentController {

    @Autowired
    private PaymentService paymentService

    /**
     * Create a payment intent for appointment booking
     */
    @PostMapping("/create-payment-intent")
    ResponseEntity<?> createPaymentIntent(
            @Valid @RequestBody PaymentIntentRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Set customer information if authenticated
            if (userPrincipal) {
                request.customerEmail = userPrincipal.getEmail()
                request.customerName = "${userPrincipal.getFirstName()} ${userPrincipal.getLastName()}".trim()
            }

            PaymentIntentResponse response = paymentService.createPaymentIntent(request)
            
            return ResponseEntity.ok(response)
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Payment intent creation failed",
                message: e.getMessage()
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Payment intent creation failed",
                message: "An unexpected error occurred: ${e.getMessage()}"
            ])
        }
    }

    /**
     * Confirm payment intent after successful payment
     */
    @PostMapping("/confirm-payment-intent")
    ResponseEntity<?> confirmPaymentIntent(
            @RequestParam String paymentIntentId,
            @RequestParam String paymentMethodId) {
        try {
            PaymentIntentResponse response = paymentService.confirmPaymentIntent(
                paymentIntentId, paymentMethodId
            )
            
            return ResponseEntity.ok([
                message: "Payment confirmed successfully",
                payment: response
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Payment confirmation failed",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Handle payment webhook from Stripe (for production use)
     */
    @PostMapping("/webhook")
    ResponseEntity<?> handleWebhook(@RequestBody String payload, @RequestHeader("Stripe-Signature") String signature) {
        try {
            // In production, verify webhook signature and process events
            // For now, return success
            return ResponseEntity.ok([
                message: "Webhook received"
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Webhook processing failed",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Confirm payment for an appointment
     */
    @PostMapping("/confirm-payment")
    ResponseEntity<?> confirmPayment(@RequestBody Map<String, Object> requestBody) {
        try {
            String paymentIntentId = requestBody.get("paymentIntentId") as String
            String appointmentId = requestBody.get("appointmentId") as String

            if (!paymentIntentId || !appointmentId) {
                return ResponseEntity.badRequest().body([
                    error: "Payment confirmation failed",
                    message: "Payment intent ID and appointment ID are required"
                ])
            }

            // For testing purposes, simulate successful payment confirmation
            return ResponseEntity.ok([
                message: "Payment confirmed successfully",
                paymentStatus: "succeeded",
                paymentIntentId: paymentIntentId,
                appointmentId: appointmentId
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Payment confirmation failed",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Get payment status for an appointment
     */
    @GetMapping("/status/{appointmentId}")
    ResponseEntity<?> getPaymentStatus(@PathVariable("appointmentId") UUID appointmentId) {
        try {
            // This would check the payment status for the appointment
            return ResponseEntity.ok([
                appointmentId: appointmentId,
                paymentStatus: "pending",
                message: "Payment status endpoint - to be implemented"
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve payment status",
                message: e.getMessage()
            ])
        }
    }
}
