package com.ddimitko.beautyhub.controller

import com.ddimitko.beautyhub.dto.*
import com.ddimitko.beautyhub.security.CustomUserPrincipal
import com.ddimitko.beautyhub.service.AppointmentService
import com.ddimitko.beautyhub.service.SlotLockingService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

import jakarta.validation.Valid
import java.time.LocalDate

@RestController
@RequestMapping("/api/appointments")
@CrossOrigin(origins = "*", maxAge = 3600)
class AppointmentController {

    @Autowired
    private AppointmentService appointmentService

    @Autowired
    private SlotLockingService slotLockingService

    /**
     * Create a new appointment (supports both authenticated and guest users)
     */
    @PostMapping
    ResponseEntity<?> createAppointment(
            @Valid @RequestBody AppointmentCreationRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            UUID userId = userPrincipal?.getId()
            AppointmentResponse response = appointmentService.createAppointment(request, userId)
            
            return ResponseEntity.ok([
                message: "Appointment created successfully",
                appointment: response
            ])
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Appointment creation failed",
                message: e.getMessage()
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Appointment creation failed",
                message: "An unexpected error occurred: ${e.getMessage()}"
            ])
        }
    }

    /**
     * Update an existing appointment
     */
    @PutMapping("/{appointmentId}")
    @PreAuthorize("hasAnyRole('USER', 'EMPLOYEE', 'OWNER')")
    ResponseEntity<?> updateAppointment(
            @PathVariable("appointmentId") UUID appointmentId,
            @Valid @RequestBody AppointmentUpdateRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            AppointmentResponse response = appointmentService.updateAppointment(
                appointmentId, request, userPrincipal.getId()
            )
            
            return ResponseEntity.ok([
                message: "Appointment updated successfully",
                appointment: response
            ])
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Appointment update failed",
                message: e.getMessage()
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Appointment update failed",
                message: "An unexpected error occurred"
            ])
        }
    }

    /**
     * Cancel an appointment
     */
    @PutMapping("/{appointmentId}/cancel")
    @PreAuthorize("hasAnyRole('USER', 'EMPLOYEE', 'OWNER')")
    ResponseEntity<?> cancelAppointment(
            @PathVariable("appointmentId") UUID appointmentId,
            @RequestBody Map<String, String> requestBody,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            String reason = requestBody.get("reason") ?: "No reason provided"
            AppointmentResponse response = appointmentService.cancelAppointment(
                appointmentId, reason, userPrincipal.getId()
            )
            
            return ResponseEntity.ok([
                message: "Appointment cancelled successfully",
                appointment: response
            ])
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Appointment cancellation failed",
                message: e.getMessage()
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Appointment cancellation failed",
                message: "An unexpected error occurred"
            ])
        }
    }

    /**
     * Get available time slots for booking
     */
    @GetMapping("/available-slots")
    ResponseEntity<?> getAvailableSlots(
            @RequestParam("shopId") UUID shopId,
            @RequestParam("employeeId") UUID employeeId,
            @RequestParam("serviceId") UUID serviceId,
            @RequestParam("date") String date) {
        try {
            LocalDate localDate = LocalDate.parse(date)
            List<AvailableSlotResponse> slots = appointmentService.getAvailableSlots(
                shopId, employeeId, serviceId, localDate
            )
            
            return ResponseEntity.ok(slots)
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve available slots",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Lock a time slot temporarily
     */
    @PostMapping("/lock-slot")
    ResponseEntity<?> lockSlot(
            @Valid @RequestBody SlotLockRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            UUID userId = userPrincipal?.getId() ?: UUID.randomUUID() // Generate temp ID for guests
            
            boolean locked = slotLockingService.lockSlot(
                request.shopId, request.serviceId, request.employeeId, 
                request.dateTime, userId
            )
            
            if (locked) {
                return ResponseEntity.ok([
                    message: "Slot locked successfully",
                    lockToken: userId.toString(),
                    expiresIn: slotLockingService.getLockDuration().toMinutes()
                ])
            } else {
                return ResponseEntity.badRequest().body([
                    error: "Slot lock failed",
                    message: "Slot is already locked by another user"
                ])
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Slot lock failed",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Unlock a time slot
     */
    @PostMapping("/unlock-slot")
    ResponseEntity<?> unlockSlot(
            @Valid @RequestBody SlotLockRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            UUID userId

            // Determine user ID from different sources
            if (userPrincipal?.getId()) {
                userId = userPrincipal.getId()
            } else if (request.lockToken) {
                // Use lockToken as userId for guest users
                userId = UUID.fromString(request.lockToken)
            } else if (request.userId) {
                userId = request.userId
            } else {
                return ResponseEntity.badRequest().body([
                    error: "Slot unlock failed",
                    message: "User identification required"
                ])
            }

            slotLockingService.unlockSlotIfOwnedByUser(
                request.shopId, request.serviceId, request.employeeId,
                request.dateTime, userId
            )

            return ResponseEntity.ok([
                message: "Slot unlocked successfully"
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Slot unlock failed",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Release slot lock when payment fails
     */
    @PostMapping("/release-payment-lock")
    ResponseEntity<?> releasePaymentLock(
            @Valid @RequestBody SlotLockRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            UUID userId

            // Determine user ID from different sources
            if (userPrincipal?.getId()) {
                userId = userPrincipal.getId()
            } else if (request.lockToken) {
                // Use lockToken as userId for guest users
                userId = UUID.fromString(request.lockToken)
            } else if (request.userId) {
                userId = request.userId
            } else {
                return ResponseEntity.badRequest().body([
                    error: "Payment lock release failed",
                    message: "User identification required"
                ])
            }

            // Release the specific slot lock
            slotLockingService.unlockSlotIfOwnedByUser(
                request.shopId, request.serviceId, request.employeeId,
                request.dateTime, userId
            )

            return ResponseEntity.ok([
                message: "Payment lock released successfully"
            ])
        } catch (Exception e) {
            log.error("Failed to release payment lock: ${e.getMessage()}", e)
            return ResponseEntity.badRequest().body([
                error: "Payment lock release failed",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Validate existing lock token for a slot
     */
    @PostMapping("/get-lock-token")
    ResponseEntity<?> getLockToken(
            @Valid @RequestBody SlotLockRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            UUID userId

            // Determine user ID for validation
            if (userPrincipal?.getId()) {
                // Authenticated user
                userId = userPrincipal.getId()
            } else if (request.lockToken) {
                // Guest user - use provided lockToken as userId
                try {
                    userId = UUID.fromString(request.lockToken)
                } catch (IllegalArgumentException e) {
                    return ResponseEntity.badRequest().body([
                        error: "Lock token validation failed",
                        message: "Invalid lock token format"
                    ])
                }
            } else {
                return ResponseEntity.badRequest().body([
                    error: "Lock token validation failed",
                    message: "Lock token is required for validation"
                ])
            }

            String lockOwner = slotLockingService.getSlotLockOwner(
                request.shopId, request.serviceId, request.employeeId, request.dateTime
            )

            if (lockOwner && lockOwner == userId.toString()) {
                return ResponseEntity.ok([
                    message: "Lock token validated successfully",
                    lockToken: lockOwner,
                    expiresIn: slotLockingService.getLockDuration().toMinutes(),
                    valid: true
                ])
            } else if (lockOwner) {
                return ResponseEntity.badRequest().body([
                    error: "Lock token validation failed",
                    message: "Slot is locked by another user",
                    valid: false
                ])
            } else {
                return ResponseEntity.badRequest().body([
                    error: "Lock token validation failed",
                    message: "Slot is not locked or lock has expired",
                    valid: false
                ])
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Lock token validation failed",
                message: e.getMessage(),
                valid: false
            ])
        }
    }

    /**
     * Get user's appointments
     */
    @GetMapping("/my-appointments")
    @PreAuthorize("hasAnyRole('USER', 'EMPLOYEE', 'OWNER')")
    ResponseEntity<?> getMyAppointments(
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal,
            Pageable pageable) {
        try {
            Page<AppointmentResponse> appointments = appointmentService.getUserAppointments(
                userPrincipal.getId(), pageable
            )
            
            return ResponseEntity.ok(appointments)
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve appointments",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Get guest appointments by email (for preview before registration)
     */
    @GetMapping("/guest")
    ResponseEntity<?> getGuestAppointments(@RequestParam("email") String email) {
        try {
            List<AppointmentResponse> appointments = appointmentService.getGuestAppointments(email)

            // Return full information for guest appointments
            return ResponseEntity.ok(appointments)
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve guest appointments",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Get shop appointments (for owners and employees)
     */
    @GetMapping("/shop/{shopId}")
    @PreAuthorize("hasAnyRole('OWNER', 'EMPLOYEE')")
    ResponseEntity<?> getShopAppointments(
            @PathVariable("shopId") UUID shopId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal,
            Pageable pageable) {
        try {
            Page<AppointmentResponse> appointments = appointmentService.getShopAppointments(
                shopId, userPrincipal.getId(), pageable
            )

            return ResponseEntity.ok(appointments)
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve shop appointments",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Get employee appointments
     */
    @GetMapping("/employee/{employeeId}")
    @PreAuthorize("hasAnyRole('OWNER', 'EMPLOYEE')")
    ResponseEntity<?> getEmployeeAppointments(
            @PathVariable("employeeId") UUID employeeId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal,
            Pageable pageable) {
        try {
            Page<AppointmentResponse> appointments = appointmentService.getEmployeeAppointments(
                employeeId, userPrincipal.getId(), pageable
            )

            return ResponseEntity.ok(appointments)
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve employee appointments",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Update appointment status
     */
    @PutMapping("/{appointmentId}/status")
    @PreAuthorize("hasAnyRole('OWNER', 'EMPLOYEE')")
    ResponseEntity<?> updateAppointmentStatus(
            @PathVariable("appointmentId") UUID appointmentId,
            @RequestBody Map<String, String> requestBody,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            String status = requestBody.get("status")
            if (!status) {
                return ResponseEntity.badRequest().body([
                    error: "Status update failed",
                    message: "Status is required"
                ])
            }

            AppointmentResponse response = appointmentService.updateAppointmentStatus(
                appointmentId, status, userPrincipal.getId()
            )

            return ResponseEntity.ok([
                message: "Appointment status updated successfully",
                appointment: response
            ])
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Status update failed",
                message: e.getMessage()
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Status update failed",
                message: "An unexpected error occurred"
            ])
        }
    }

    /**
     * Get appointments by status
     */
    @GetMapping("/by-status")
    ResponseEntity<?> getAppointmentsByStatus(
            @RequestParam("status") String status,
            Pageable pageable) {
        try {
            Page<AppointmentResponse> appointments = appointmentService.getAppointmentsByStatus(
                status, pageable
            )

            return ResponseEntity.ok(appointments)
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve appointments by status",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Get a specific appointment by ID
     */
    @GetMapping("/{appointmentId}")
    @PreAuthorize("hasAnyRole('USER', 'EMPLOYEE', 'OWNER')")
    ResponseEntity<?> getAppointment(
            @PathVariable("appointmentId") UUID appointmentId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // This would need additional logic to verify user can access this appointment
            // For now, returning a placeholder response
            return ResponseEntity.ok([
                message: "Appointment details endpoint - to be implemented"
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve appointment",
                message: e.getMessage()
            ])
        }
    }
}
