package com.ddimitko.beautyhub.controller

import com.ddimitko.beautyhub.dto.ServiceRequest
import com.ddimitko.beautyhub.entity.Employee
import com.ddimitko.beautyhub.entity.Service
import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.repository.EmployeeRepository
import com.ddimitko.beautyhub.repository.ServiceRepository
import com.ddimitko.beautyhub.repository.ShopRepository
import com.ddimitko.beautyhub.security.CustomUserPrincipal
import jakarta.validation.Valid
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Pageable
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/services")
@CrossOrigin(origins = "*", maxAge = 3600)
class ServiceController {

    @Autowired
    private ServiceRepository serviceRepository

    @Autowired
    private ShopRepository shopRepository

    @Autowired
    private EmployeeRepository employeeRepository

    /**
     * Get services with optional filtering
     */
    @GetMapping
    @PreAuthorize("hasAnyRole('OWNER', 'EMPLOYEE')")
    ResponseEntity<?> getServices(
            @RequestParam(value = "shopId", required = false) UUID shopId,
            @RequestParam(value = "employeeId", required = false) UUID employeeId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {

        println("=== SERVICE CONTROLLER REACHED ===")
        println("Shop ID param: ${shopId}")
        println("Employee ID param: ${employeeId}")
        println("User: ${userPrincipal?.getEmail()}")

        try {
            println("=== SERVICE CONTROLLER DEBUG ===")
            println("User ID: ${userPrincipal.getId()}")
            println("User Email: ${userPrincipal.getEmail()}")
            println("User Authorities: ${userPrincipal.getAuthorities()}")
            println("Shop ID: ${shopId}")

            List<Service> services = []

            if (shopId) {
                Shop shop = shopRepository.findById(shopId)
                        .orElseThrow { new RuntimeException("Shop not found") }

                println("Shop found: ${shop.name}")
                println("Shop owner ID: ${shop.owner.id}")
                println("User ID: ${userPrincipal.getId()}")
                println("Owner match: ${shop.owner.id.equals(userPrincipal.getId())}")

                // Check if user is employee at this shop
                boolean isEmployee = employeeRepository.existsByUserIdAndShopIdAndActiveTrue(userPrincipal.getId(), shopId)
                println("Is employee: ${isEmployee}")

                // Verify access - owner of shop or employee of shop
                boolean hasAccess = shop.owner.id.equals(userPrincipal.getId()) || isEmployee

                println("Has access: ${hasAccess}")

                if (!hasAccess) {
                    println("ACCESS DENIED - returning 403")
                    return ResponseEntity.status(403).body([
                        error: "Access denied",
                        message: "You can only view services for shops you own or work at",
                        debug: [
                            userId: userPrincipal.getId(),
                            shopOwnerId: shop.owner.id,
                            isOwner: shop.owner.id.equals(userPrincipal.getId()),
                            isEmployee: isEmployee,
                            userAuthorities: userPrincipal.getAuthorities().collect { it.getAuthority() }
                        ]
                    ])
                }

                services = serviceRepository.findByShopAndActiveTrueWithEmployeeAndUser(shop)
            } else if (employeeId) {
                Employee employee = employeeRepository.findById(employeeId)
                        .orElseThrow { new RuntimeException("Employee not found") }

                // Verify access - owner of shop or the employee themselves
                boolean hasAccess = employee.shop.owner.id.equals(userPrincipal.getId()) ||
                                   employee.user.id.equals(userPrincipal.getId())

                if (!hasAccess) {
                    return ResponseEntity.status(403).body([
                        error: "Access denied",
                        message: "You can only view services for your own employees"
                    ])
                }

                services = serviceRepository.findByEmployeeAndActiveTrueWithEmployeeAndUser(employee)
            } else {
                return ResponseEntity.badRequest().body([
                    error: "Missing parameter",
                    message: "Either shopId or employeeId is required"
                ])
            }

            return ResponseEntity.ok([
                data: services.collect { service ->
                    [
                        id: service.id,
                        name: service.name,
                        description: service.description,
                        price: service.price,
                        durationMinutes: service.durationMinutes,
                        depositAmount: service.depositAmount,
                        category: service.category,
                        active: service.active,
                        employeeId: service.employee.id,
                        employeeName: service.employee.fullName,
                        shopId: service.shop.id,
                        shopName: service.shop.name,
                        formattedPrice: service.formattedPrice,
                        formattedDuration: service.formattedDuration,
                        createdAt: service.createdAt
                    ]
                }
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve services",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Get a specific service
     */
    @GetMapping("/{serviceId}")
    @PreAuthorize("hasAnyRole('OWNER', 'EMPLOYEE', 'USER')")
    ResponseEntity<?> getService(@PathVariable("serviceId") UUID serviceId) {
        try {
            Service service = serviceRepository.findById(serviceId)
                    .orElseThrow { new RuntimeException("Service not found") }

            return ResponseEntity.ok([
                id: service.id,
                name: service.name,
                description: service.description,
                price: service.price,
                durationMinutes: service.durationMinutes,
                depositAmount: service.depositAmount,
                category: service.category,
                active: service.active,
                employeeId: service.employee.id,
                employeeName: service.employee.fullName,
                shopId: service.shop.id,
                shopName: service.shop.name,
                formattedPrice: service.formattedPrice,
                formattedDuration: service.formattedDuration,
                createdAt: service.createdAt
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve service",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Create a new service
     */
    @PostMapping
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> createService(
            @Valid @RequestBody ServiceRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            Shop shop = shopRepository.findById(request.shopId)
                    .orElseThrow { new RuntimeException("Shop not found") }

            // Verify that the user owns the shop
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only create services for your own shops"
                ])
            }

            Employee employee = employeeRepository.findByIdWithUser(request.employeeId)
                    .orElseThrow { new RuntimeException("Employee not found") }

            // Verify that the employee belongs to the shop
            if (!employee.shop.id.equals(shop.id)) {
                return ResponseEntity.badRequest().body([
                    error: "Invalid employee",
                    message: "Employee does not belong to this shop"
                ])
            }

            Service service = new Service()
            service.name = request.name
            service.description = request.description
            service.price = request.price
            service.durationMinutes = request.durationMinutes
            service.depositAmount = request.depositAmount ?: BigDecimal.ZERO
            service.category = request.category
            service.active = true
            service.onlineBookingEnabled = true
            service.requiresDeposit = request.depositAmount && request.depositAmount > BigDecimal.ZERO
            service.employee = employee
            service.shop = shop

            service = serviceRepository.save(service)

            return ResponseEntity.ok([
                message: "Service created successfully",
                service: [
                    id: service.id,
                    name: service.name,
                    description: service.description,
                    price: service.price,
                    durationMinutes: service.durationMinutes,
                    depositAmount: service.depositAmount,
                    employeeId: service.employee.id,
                    employeeName: service.employee.fullName,
                    shopId: service.shop.id
                ]
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to create service",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Update a service
     */
    @PutMapping("/{serviceId}")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> updateService(
            @PathVariable("serviceId") UUID serviceId,
            @Valid @RequestBody ServiceRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            Service service = serviceRepository.findById(serviceId)
                    .orElseThrow { new RuntimeException("Service not found") }

            // Verify that the user owns the shop
            if (!service.shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only update services for your own shops"
                ])
            }

            // Update employee if provided and different
            if (request.employeeId && !request.employeeId.equals(service.employee.id)) {
                Employee employee = employeeRepository.findByIdWithUser(request.employeeId)
                        .orElseThrow { new RuntimeException("Employee not found") }

                // Verify that the employee belongs to the shop
                if (!employee.shop.id.equals(service.shop.id)) {
                    return ResponseEntity.badRequest().body([
                        error: "Invalid employee",
                        message: "Employee does not belong to this shop"
                    ])
                }

                service.employee = employee
            }

            // Update service fields
            service.name = request.name
            service.description = request.description
            service.price = request.price
            service.durationMinutes = request.durationMinutes
            service.depositAmount = request.depositAmount ?: BigDecimal.ZERO
            service.category = request.category
            service.requiresDeposit = request.depositAmount && request.depositAmount > BigDecimal.ZERO

            service = serviceRepository.save(service)

            return ResponseEntity.ok([
                message: "Service updated successfully",
                service: [
                    id: service.id,
                    name: service.name,
                    description: service.description,
                    price: service.price,
                    durationMinutes: service.durationMinutes,
                    depositAmount: service.depositAmount,
                    employeeId: service.employee.id,
                    employeeName: service.employee.fullName
                ]
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to update service",
                message: e.getMessage()
            ])
        }
    }

    /**
     * Delete (deactivate) a service
     */
    @DeleteMapping("/{serviceId}")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> deleteService(
            @PathVariable("serviceId") UUID serviceId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            Service service = serviceRepository.findById(serviceId)
                    .orElseThrow { new RuntimeException("Service not found") }

            // Verify that the user owns the shop
            if (!service.shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only delete services for your own shops"
                ])
            }

            // Soft delete by setting active to false
            service.active = false
            serviceRepository.save(service)

            return ResponseEntity.ok([
                message: "Service deleted successfully"
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to delete service",
                message: e.getMessage()
            ])
        }
    }
}
