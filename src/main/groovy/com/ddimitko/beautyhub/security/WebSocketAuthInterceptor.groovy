package com.ddimitko.beautyhub.security

import com.ddimitko.beautyhub.config.JwtConfig
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.messaging.Message
import org.springframework.messaging.MessageChannel
import org.springframework.messaging.simp.stomp.StompCommand
import org.springframework.messaging.simp.stomp.StompHeaderAccessor
import org.springframework.messaging.support.ChannelInterceptor
import org.springframework.messaging.support.MessageHeaderAccessor
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.stereotype.Component

@Component
class WebSocketAuthInterceptor implements ChannelInterceptor {

    @Autowired
    private JwtConfig jwtConfig

    @Autowired
    private CustomUserDetailsService userDetailsService

    @Override
    Message<?> preSend(Message<?> message, MessageChannel channel) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class)

        if (StompCommand.CONNECT.equals(accessor.getCommand())) {
            String authToken = accessor.getFirstNativeHeader("Authorization")

            if (authToken != null && authToken.startsWith("Bearer ")) {
                String token = authToken.substring(7)

                try {
                    String username = jwtConfig.extractUsername(token)

                    if (username != null && jwtConfig.validateToken(token)) {
                        UserDetails userDetails = userDetailsService.loadUserByUsername(username)
                        Authentication authentication = new UsernamePasswordAuthenticationToken(
                            userDetails, null, userDetails.getAuthorities()
                        )

                        accessor.setUser(authentication)
                        SecurityContextHolder.getContext().setAuthentication(authentication)

                        // Log successful authentication
                        println("WebSocket authentication successful for user: ${username}")
                    }
                } catch (Exception e) {
                    // Log the error but allow connection (for guest users)
                    println("WebSocket authentication failed: ${e.getMessage()}")
                }
            } else {
                // Log missing token
                println("WebSocket connection attempt without valid Authorization header")
            }
        }

        return message
    }
}
