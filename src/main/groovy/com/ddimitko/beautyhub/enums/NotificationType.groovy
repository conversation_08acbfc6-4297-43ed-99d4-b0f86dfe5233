package com.ddimitko.beautyhub.enums

enum NotificationType {
    APPOINTMENT_REMINDER("Appointment Reminder"),
    APPOINTMENT_CONFIRMED("Appointment Confirmed"),
    APPOINTMENT_CANCELLED("Appointment Cancelled"),
    APPOINTMENT_RESCHEDULED("Appointment Rescheduled"),
    PAYMENT_RECEIVED("Payment Received"),
    PAYMENT_FAILED("Payment Failed"),
    NEW_BOOKING("New Booking"),
    BOOKING_CANCELLED("Booking Cancelled"),
    EMPLOYEE_INVITATION("Employee Invitation"),
    SHOP_APPROVED("Shop Approved"),
    SHOP_REJECTED("Shop Rejected"),
    SUBSCRIPTION_EXPIRING("Subscription Expiring"),
    SUBSCRIPTION_RENEWED("Subscription Renewed"),
    SUBSCRIPTION_CANCELLED("Subscription Cancelled"),
    SYSTEM_MAINTENANCE("System Maintenance"),
    PROMOTIONAL("Promotional"),
    GENERAL("General")

    private final String displayName

    NotificationType(String displayName) {
        this.displayName = displayName
    }

    String getDisplayName() {
        return displayName
    }

    @Override
    String toString() {
        return displayName
    }
}
