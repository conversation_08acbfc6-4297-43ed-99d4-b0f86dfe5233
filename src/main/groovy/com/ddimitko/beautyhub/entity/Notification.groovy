package com.ddimitko.beautyhub.entity

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import lombok.AllArgsConstructor
import lombok.Builder
import lombok.Data
import lombok.NoArgsConstructor
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Entity
@Table(name = "notifications")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(excludes = ["user"])
@ToString(excludes = ["user"])
class Notification {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id")
    UUID id

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @NotNull(message = "User is required")
    User user

    @Column(name = "title", nullable = false)
    @NotBlank(message = "Title is required")
    String title

    @Column(name = "message", nullable = false, columnDefinition = "TEXT")
    @NotBlank(message = "Message is required")
    String message

    @Column(name = "type")
    String type

    @Column(name = "seen", nullable = false)
    Boolean seen = false

    @Column(name = "read_at")
    LocalDateTime readAt

    @Column(name = "data", columnDefinition = "TEXT")
    String data // JSON data for additional context

    @Column(name = "action_url")
    String actionUrl

    @Column(name = "push_sent", nullable = false)
    Boolean pushSent = false

    @Column(name = "email_sent", nullable = false)
    Boolean emailSent = false

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    LocalDateTime createdAt

    @UpdateTimestamp
    @Column(name = "updated_at")
    LocalDateTime updatedAt

    void markAsSeen() {
        if (!seen) {
            seen = true
            readAt = LocalDateTime.now()
        }
    }

    boolean isUnread() {
        return !seen
    }

    String getFormattedDate() {
        return createdAt?.format(DateTimeFormatter.ofPattern("MMM dd, yyyy 'at' h:mm a"))
    }

    String getUserName() {
        return user?.getFullName()
    }
}
