package com.ddimitko.beautyhub.entity

import com.ddimitko.beautyhub.enums.DayOfWeek
import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import lombok.AllArgsConstructor
import lombok.Builder
import lombok.Data
import lombok.NoArgsConstructor
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp

import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter

@Entity
@Table(name = "schedule_slots")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(excludes = ["employee"])
@ToString(excludes = ["employee"])
class ScheduleSlot {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id")
    UUID id

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "employee_id", nullable = false)
    @NotNull(message = "Employee is required")
    Employee employee

    @Enumerated(EnumType.STRING)
    @Column(name = "day_of_week", nullable = false)
    @NotNull(message = "Day of week is required")
    DayOfWeek dayOfWeek

    @Column(name = "start_time", nullable = false)
    @NotNull(message = "Start time is required")
    LocalTime startTime

    @Column(name = "end_time", nullable = false)
    @NotNull(message = "End time is required")
    LocalTime endTime

    @Column(name = "active", nullable = false)
    Boolean active = true

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    LocalDateTime createdAt

    @UpdateTimestamp
    @Column(name = "updated_at")
    LocalDateTime updatedAt

    @PrePersist
    @PreUpdate
    void validateTimes() {
        if (startTime && endTime && !startTime.isBefore(endTime)) {
            throw new IllegalArgumentException("Start time must be before end time")
        }
    }

    boolean isValidTimeSlot() {
        return startTime && endTime && startTime.isBefore(endTime)
    }

    String getFormattedTimeRange() {
        return "${startTime.format(DateTimeFormatter.ofPattern('h:mm a'))} - ${endTime.format(DateTimeFormatter.ofPattern('h:mm a'))}"
    }

    String getEmployeeName() {
        return employee?.getFullName()
    }

    int getDurationMinutes() {
        if (startTime && endTime) {
            return (int) java.time.Duration.between(startTime, endTime).toMinutes()
        }
        return 0
    }
}
