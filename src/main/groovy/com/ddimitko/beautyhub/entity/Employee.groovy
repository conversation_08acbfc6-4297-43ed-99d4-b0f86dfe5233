package com.ddimitko.beautyhub.entity

import com.fasterxml.jackson.annotation.JsonIgnore
import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import jakarta.persistence.*
import lombok.AllArgsConstructor
import lombok.Builder
import lombok.Data
import lombok.NoArgsConstructor
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp

import java.time.LocalDateTime

@Entity
@Table(name = "employees")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(excludes = ["user", "shop", "services", "scheduleSlots", "appointments"])
@ToString(excludes = ["user", "shop", "services", "scheduleSlots", "appointments"])
class Employee {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id")
    UUID id

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    User user

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "shop_id", nullable = false)
    Shop shop

    @Column(name = "bio", columnDefinition = "TEXT")
    String bio

    @Column(name = "specialties")
    String specialties

    @Column(name = "years_experience")
    Integer yearsExperience

    @Column(name = "hourly_rate")
    BigDecimal hourlyRate

    @Column(name = "commission_rate")
    BigDecimal commissionRate

    @Column(name = "active", nullable = false)
    Boolean active = true

    @Column(name = "hire_date")
    LocalDateTime hireDate

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    LocalDateTime createdAt

    @UpdateTimestamp
    @Column(name = "updated_at")
    LocalDateTime updatedAt

    // Relationships
    @OneToMany(mappedBy = "employee", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    Set<Service> services = new HashSet<>()

    @OneToMany(mappedBy = "employee", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    Set<ScheduleSlot> scheduleSlots = new HashSet<>()

    @OneToMany(mappedBy = "employee", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    Set<Appointment> appointments = new HashSet<>()

    String getFullName() {
        return user?.getFullName()
    }

    String getEmail() {
        return user?.email
    }

    String getPhone() {
        return user?.phone
    }

    boolean isAvailable() {
        return active && user?.enabled
    }
}
