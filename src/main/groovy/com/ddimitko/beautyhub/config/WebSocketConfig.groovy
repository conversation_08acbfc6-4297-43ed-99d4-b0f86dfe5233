package com.ddimitko.beautyhub.config

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.messaging.MessageChannel
import org.springframework.messaging.support.ExecutorSubscribableChannel
import org.springframework.web.socket.*
import org.springframework.web.socket.config.annotation.EnableWebSocket
import org.springframework.web.socket.config.annotation.WebSocketConfigurer
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry
import com.ddimitko.beautyhub.service.WebSocketMonitoringService
import groovy.util.logging.Slf4j
import com.fasterxml.jackson.databind.ObjectMapper

@Configuration
@EnableWebSocket
@Slf4j
class WebSocketConfig implements WebSocketConfigurer {

    @Value('${app.websocket.allowed-origins}')
    private String allowedOrigins

    @Autowired
    private WebSocketMonitoringService monitoringService

    @Autowired
    private ObjectMapper objectMapper

    /**
     * Create a custom message channel for our application-specific messaging.
     * This is used by our RabbitMQ integration for reliable message processing.
     */
    @Bean
    MessageChannel applicationMessageChannel() {
        ExecutorSubscribableChannel channel = new ExecutorSubscribableChannel()
        channel.setBeanName("applicationMessageChannel")
        return channel
    }

    /**
     * WebSocket handler for real-time notifications.
     * This approach avoids Spring Boot 3.5 STOMP broker relay issues.
     */
    @Bean
    WebSocketHandler notificationWebSocketHandler() {
        def monitoringServiceRef = monitoringService
        def objectMapperRef = objectMapper
        return new WebSocketHandler() {
            @Override
            void afterConnectionEstablished(WebSocketSession session) throws Exception {
                log.info("WebSocket connection established: {}", session.getId())
                monitoringServiceRef.registerSession(session)
            }

            @Override
            void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
                log.debug("WebSocket message received: {}", message.getPayload())
                monitoringServiceRef.recordMessage(session.getId())

                // Handle subscription messages for performance optimization
                def payload = message.getPayload().toString()
                try {
                    if (payload.startsWith('{')) {
                        def messageData = objectMapperRef.readValue(payload, Map.class)

                        if (messageData.type == 'authenticate' && messageData.token) {
                            // Authenticate user and associate with session
                            log.info("Authenticating session {} with token", session.getId())
                            monitoringServiceRef.authenticateSession(session.getId(), messageData.token.toString())
                            session.sendMessage(new TextMessage("""{"type":"authentication_confirmed","authenticated":true}"""))
                            return
                        } else if (messageData.type == 'subscribe' && messageData.topic) {
                            // Subscribe session to specific topic (allow both authenticated and unauthenticated users)
                            String topic = messageData.topic.toString()
                            log.info("📡 Subscribing session {} to topic: {}", session.getId(), topic)

                            // Check if this is a slot update topic - allow unauthenticated access
                            boolean isSlotTopic = topic.startsWith('slots.')
                            boolean isAuthenticated = session.getAttributes().get("authenticated") == true

                            if (isSlotTopic || isAuthenticated) {
                                monitoringServiceRef.subscribeToTopic(session.getId(), topic)

                                // Send enhanced confirmation with more details
                                def confirmationMessage = [
                                    type: "subscription_confirmed",
                                    topic: topic,
                                    sessionId: session.getId(),
                                    timestamp: System.currentTimeMillis(),
                                    authenticated: isAuthenticated
                                ]

                                session.sendMessage(new TextMessage(objectMapperRef.writeValueAsString(confirmationMessage)))
                                log.info("✅ Subscription confirmed for session {} to topic: {}", session.getId(), topic)
                            } else {
                                // Require authentication for non-slot topics
                                def denialMessage = [
                                    type: "subscription_denied",
                                    topic: topic,
                                    reason: "Authentication required",
                                    timestamp: System.currentTimeMillis()
                                ]
                                session.sendMessage(new TextMessage(objectMapperRef.writeValueAsString(denialMessage)))
                                log.warn("❌ Subscription denied for unauthenticated session {} to topic: {}", session.getId(), topic)
                            }
                            return
                        } else if (messageData.type == 'unsubscribe' && messageData.topic) {
                            // Unsubscribe session from specific topic
                            String topic = messageData.topic.toString()
                            log.info("🔌 Unsubscribing session {} from topic: {}", session.getId(), topic)
                            monitoringServiceRef.unsubscribeFromTopic(session.getId(), topic)

                            def confirmationMessage = [
                                type: "unsubscription_confirmed",
                                topic: topic,
                                sessionId: session.getId(),
                                timestamp: System.currentTimeMillis()
                            ]

                            session.sendMessage(new TextMessage(objectMapperRef.writeValueAsString(confirmationMessage)))
                            log.info("✅ Unsubscription confirmed for session {} from topic: {}", session.getId(), topic)
                            return
                        }
                    }
                } catch (Exception e) {
                    log.debug("Message not a subscription request: {}", e.getMessage())
                }

                // Don't echo subscription/unsubscription messages or any control messages
                // Only echo simple test messages
                if (!payload.startsWith('{')) {
                    session.sendMessage(new TextMessage("Echo: " + message.getPayload()))
                }
            }

            @Override
            void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
                log.error("WebSocket transport error for session {}: {}", session.getId(), exception.getMessage())
            }

            @Override
            void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
                log.info("WebSocket connection closed: {} with status: {}", session.getId(), closeStatus)
                monitoringServiceRef.unregisterSession(session)
            }

            @Override
            boolean supportsPartialMessages() {
                return false
            }
        }
    }

    // WebSocket Handler Configuration
    @Override
    void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // Register WebSocket endpoints for real-time communication
        // Use /ws-notifications to avoid conflict with /api/notifications REST endpoints
        registry.addHandler(notificationWebSocketHandler(), "/ws-notifications")
                .setAllowedOrigins(allowedOrigins.split(","))
                .withSockJS()

        registry.addHandler(notificationWebSocketHandler(), "/ws")
                .setAllowedOrigins(allowedOrigins.split(","))

        log.info("WebSocket handlers registered: /ws-notifications, /ws")
    }
}
