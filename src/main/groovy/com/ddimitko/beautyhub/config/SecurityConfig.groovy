package com.ddimitko.beautyhub.config

import com.ddimitko.beautyhub.security.CustomUserDetailsService
import com.ddimitko.beautyhub.security.JwtAuthenticationFilter
import java.util.Arrays
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.core.userdetails.UserDetailsService
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.CorsConfigurationSource
import org.springframework.web.cors.UrlBasedCorsConfigurationSource

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
class SecurityConfig {

    @Autowired
    private CustomUserDetailsService customUserDetailsService

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter

    @Value('${app.cors.allowed-origins}')
    private String allowedOrigins

    @Bean
    PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder()
    }



    /*@Bean
    DaoAuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider provider = new DaoAuthenticationProvider();
        provider.setUserDetailsService(userDetailsService());
        provider.setPasswordEncoder(passwordEncoder());
        return provider;
    }*/

    @Bean
    AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager()
    }

    @Bean
    SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(csrf -> csrf.disable())
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                // Public endpoints
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/api/test/**").permitAll()
                .requestMatchers("/ws/**").permitAll()
                .requestMatchers("/ws-notifications/**").permitAll()
                .requestMatchers("/stomp/**").permitAll()
                .requestMatchers("/notifications/**").permitAll()
                .requestMatchers("/api/shops/search").permitAll()
                .requestMatchers("/api/shops/business-types").permitAll()
                .requestMatchers("/api/shops/{id}/services").permitAll()
                .requestMatchers("/api/shops/{id}/ratings").permitAll()
                .requestMatchers("/api/webhooks/**").permitAll()

                // Appointment endpoints for guest users
                .requestMatchers("/api/appointments/available-slots").permitAll()
                .requestMatchers("/api/appointments/guest-appointments").permitAll()
                .requestMatchers("/api/appointments/lock-slot").permitAll()
                .requestMatchers("/api/appointments/unlock-slot").permitAll()
                .requestMatchers("/api/appointments").permitAll() // For guest appointment creation

                // Payment endpoints for guest users
                .requestMatchers("/api/payments/create-payment-intent").permitAll()
                .requestMatchers("/api/payments/confirm-payment-intent").permitAll()
                .requestMatchers("/api/payments/webhook").permitAll()

                // Test data endpoints (for development)
                .requestMatchers("/api/test-data/**").permitAll()

                // Admin endpoints
                .requestMatchers("/api/admin/**").hasRole("ADMIN")

                // Owner endpoints
                .requestMatchers("/api/owner/**").hasRole("OWNER")

                // Employee endpoints
                .requestMatchers("/api/employee/**").hasRole("EMPLOYEE")

                // User endpoints
                .requestMatchers("/api/user/**").hasAnyRole("USER", "OWNER", "EMPLOYEE")

                // Service endpoints
                .requestMatchers("/api/services/**").hasAnyRole("OWNER", "EMPLOYEE")
                .requestMatchers("/api/services").hasAnyRole("OWNER", "EMPLOYEE")

                // Public schedule endpoints for appointment booking
                .requestMatchers("/api/schedules/public/**").permitAll()

                // Schedule endpoints
                .requestMatchers("/api/schedules/**").hasAnyRole("OWNER", "EMPLOYEE")

                // Websocket testing
                .requestMatchers("/api/websocket/**").permitAll()

                // All other requests need authentication
                .anyRequest().authenticated()
            )
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
            .build()
    }

    @Bean
    CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration()
        configuration.setAllowedOriginPatterns(Arrays.asList(allowedOrigins.split(",")))
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"))
        configuration.setAllowedHeaders(Arrays.asList("*"))
        configuration.setExposedHeaders(Arrays.asList("Authorization", "Content-Type", "X-Requested-With", "Accept", "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers"))
        configuration.setAllowCredentials(true)
        configuration.setMaxAge(3600L) // Cache preflight response for 1 hour

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource()
        source.registerCorsConfiguration("/**", configuration)
        return source
    }
}
