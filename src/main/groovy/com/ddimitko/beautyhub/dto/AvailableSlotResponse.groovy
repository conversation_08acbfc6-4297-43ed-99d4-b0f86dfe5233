package com.ddimitko.beautyhub.dto

import lombok.Data

import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter

@Data
class AvailableSlotResponse {
    
    LocalDateTime dateTime
    LocalTime startTime
    LocalTime endTime
    boolean available
    boolean locked
    String lockedBy // User ID who locked the slot
    
    // Employee information
    UUID employeeId
    String employeeName
    
    // Service information
    UUID serviceId
    String serviceName
    Integer durationMinutes
    BigDecimal price
    
    String getFormattedTime() {
        return startTime?.format(DateTimeFormatter.ofPattern("h:mm a"))
    }

    String getFormattedTimeRange() {
        return "${startTime?.format(DateTimeFormatter.ofPattern('h:mm a'))} - ${endTime?.format(DateTimeFormatter.ofPattern('h:mm a'))}"
    }
    
    boolean isBookable() {
        return available && !locked
    }
}
