package com.ddimitko.beautyhub.dto

import jakarta.validation.constraints.*
import lombok.Data

import java.time.LocalDateTime

@Data
class AppointmentUpdateRequest {
    
    @Future(message = "Appointment must be in the future")
    LocalDateTime appointmentDateTime
    
    @Size(max = 1000, message = "Notes cannot exceed 1000 characters")
    String notes
    
    // Slot locking validation for rescheduling
    String slotLockToken
    
    // Only allow updates to these fields
    // Service, employee, and payment details cannot be changed after creation
}
