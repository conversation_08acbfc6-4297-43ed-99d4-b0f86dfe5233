package com.ddimitko.beautyhub.repository

import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.enums.BusinessType
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface ShopRepository extends JpaRepository<Shop, UUID> {

    List<Shop> findByOwner(User owner)

    List<Shop> findByActiveTrue()

    Page<Shop> findByActiveTrue(Pageable pageable)

    List<Shop> findBySubscriptionActiveTrue()

    List<Shop> findByBusinessTypesContaining(BusinessType businessType)

    List<Shop> findByBusinessTypesContainingAndActiveTrue(BusinessType businessType)

    @Query("SELECT s FROM Shop s WHERE s.active = true AND LOWER(s.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<Shop> findByNameContainingIgnoreCaseAndActiveTrue(@Param("name") String name)

    @Query("SELECT s FROM Shop s WHERE s.active = true AND LOWER(s.city) LIKE LOWER(CONCAT('%', :city, '%'))")
    List<Shop> findByCityContainingIgnoreCaseAndActiveTrue(@Param("city") String city)

    @Query("SELECT s FROM Shop s WHERE s.active = true AND s.ratingAverage >= :minRating")
    List<Shop> findByRatingAverageGreaterThanEqualAndActiveTrue(@Param("minRating") Double minRating)

    @Query("SELECT s FROM Shop s WHERE s.active = true AND s.acceptsCardPayments = :acceptsCard")
    List<Shop> findByAcceptsCardPaymentsAndActiveTrue(@Param("acceptsCard") Boolean acceptsCard)

    @Query("""
        SELECT s FROM Shop s 
        WHERE s.active = true 
        AND (:name IS NULL OR LOWER(s.name) LIKE LOWER(CONCAT('%', :name, '%')))
        AND (:city IS NULL OR LOWER(s.city) LIKE LOWER(CONCAT('%', :city, '%')))
        AND (:minRating IS NULL OR s.ratingAverage >= :minRating)
        AND (:acceptsCard IS NULL OR s.acceptsCardPayments = :acceptsCard)
        ORDER BY s.ratingAverage DESC, s.name ASC
    """)
    Page<Shop> findShopsWithFilters(
        @Param("name") String name,
        @Param("city") String city,
        @Param("minRating") Double minRating,
        @Param("acceptsCard") Boolean acceptsCard,
        Pageable pageable
    )

    @Query("SELECT COUNT(s) FROM Shop s WHERE s.active = true")
    long countActiveShops()

    @Query("SELECT COUNT(s) FROM Shop s WHERE s.owner = :owner")
    long countByOwner(@Param("owner") User owner)

    @Query("SELECT s FROM Shop s WHERE s.stripeAccountId IS NOT NULL AND s.stripeOnboardingCompleted = false")
    List<Shop> findShopsWithIncompleteStripeOnboarding()
}
