package com.ddimitko.beautyhub.repository

import com.ddimitko.beautyhub.entity.Rating
import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.entity.User
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface RatingRepository extends JpaRepository<Rating, UUID> {

    List<Rating> findByShop(Shop shop)

    List<Rating> findByUser(User user)

    Page<Rating> findByShop(Shop shop, Pageable pageable)

    @Query("SELECT r FROM Rating r WHERE r.shop = :shop ORDER BY r.createdAt DESC")
    List<Rating> findByShopOrderByCreatedAtDesc(@Param("shop") Shop shop)

    @Query("SELECT AVG(r.stars) FROM Rating r WHERE r.shop = :shop")
    Double findAverageRatingByShop(@Param("shop") Shop shop)

    @Query("SELECT COUNT(r) FROM Rating r WHERE r.shop = :shop")
    long countByShop(@Param("shop") Shop shop)

    @Query("SELECT COUNT(r) FROM Rating r WHERE r.shop = :shop AND r.stars = :stars")
    long countByShopAndStars(@Param("shop") Shop shop, @Param("stars") Integer stars)

    boolean existsByUserAndShop(User user, Shop shop)

    Optional<Rating> findByUserAndShop(User user, Shop shop)
}
