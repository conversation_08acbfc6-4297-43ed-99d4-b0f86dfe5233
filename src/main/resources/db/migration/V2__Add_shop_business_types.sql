-- Add business types table for shops
CREATE TABLE shop_business_types (
    shop_id UUID NOT NULL REFERENCES shops(id) ON DELETE CASCADE,
    business_type VARCHAR(50) NOT NULL,
    PRIMARY KEY (shop_id, business_type)
);

-- Create index for faster lookups by business type
CREATE INDEX idx_shop_business_types_business_type ON shop_business_types(business_type);
CREATE INDEX idx_shop_business_types_shop_id ON shop_business_types(shop_id);
