-- Add guest user support to appointments table
ALTER TABLE appointments 
ADD COLUMN guest_email VARCHAR(255),
ADD COLUMN guest_first_name VA<PERSON><PERSON><PERSON>(100),
ADD COLUMN guest_last_name VA<PERSON>HAR(100),
ADD COLUMN guest_phone VARCHAR(20);

-- Make user_id nullable for guest appointments
ALTER TABLE appointments 
ALTER COLUMN user_id DROP NOT NULL;

-- Add constraint: either user_id OR guest_email must be present
ALTER TABLE appointments 
ADD CONSTRAINT check_user_or_guest 
CHECK ((user_id IS NOT NULL) OR (guest_email IS NOT NULL));

-- Add index for guest email lookups
CREATE INDEX idx_appointments_guest_email ON appointments(guest_email);

-- Add index for appointment datetime queries
CREATE INDEX idx_appointments_datetime ON appointments(appointment_datetime);

-- Add index for employee and datetime queries (for availability checks)
CREATE INDEX idx_appointments_employee_datetime ON appointments(employee_id, appointment_datetime);
