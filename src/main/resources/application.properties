spring.application.name=beautyhub

# Database Configuration
spring.datasource.url=******************************************
spring.datasource.username=postgres
spring.datasource.password=admin
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.defer-datasource-initialization=true
spring.sql.init.mode=always

# Flyway Configuration (temporarily disabled)
spring.flyway.enabled=false

# Redis Configuration
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.timeout=2000ms
spring.cache.type=redis

# JWT Configuration
app.jwt.secret=aec9ff638c63b45b14744ff6f01901df027a9003d570ca922a2f406573a08d97b8ee26e2f02b36eabea9b981e8e0d44fa8eb89ddc201856cf6bb26889c7dc291e1301ef0a26c97779b563bee11e218c0a98b86430b0db1ea3220fce0d6bdfbacd5cdbad6596ce60c7cf3a871bf9024da1495669ce56992facded5194508834c2bf8535f6030b03930c2dcdb78d646c0c819ef391d377977bf64eb16683d24f0dcad619abe25a04e6341642559b8fc7d81a414d26ff036a7d600ff6882946e6b924b92e5e49a2a027f3b670a5f33c32ca8a2b5bbfa75f2c9f89826749866cd70408c79fb0f9dd92b85227625596e66446f96059178898ae5ce45219a1c4ba3ad7
app.jwt.expiration=86400000

# OAuth2 Configuration (placeholders)
spring.security.oauth2.client.registration.google.client-id=your-google-client-id
spring.security.oauth2.client.registration.google.client-secret=your-google-client-secret
spring.security.oauth2.client.registration.facebook.client-id=your-facebook-client-id
spring.security.oauth2.client.registration.facebook.client-secret=your-facebook-client-secret

# Stripe Configuration
stripe.api.key=sk_test_51QtTgOE2dBEKUmD1JktkDjXr6njWbCTBvN9PktXdU4KXug2Pq2irbD4gv9ZBSf7C1Q1KmmNzYhcK1Awrrt1EoRHI00zbCYBVeA
stripe.webhook.secret=pk_test_51QtTgOE2dBEKUmD174iW0Lk9h9YazQfOrfLeACcAlYfHoFDyUww1VP4gTILNwfZC7y1cTLO0vBY5tYPHahyCuPsz008YkKGJlv

# Firebase Configuration
firebase.config.path=classpath:firebase-service-account.json

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# CORS Configuration
app.cors.allowed-origins=http://localhost:3000

# WebSocket Configuration
app.websocket.allowed-origins=http://localhost:3000

# RabbitMQ Configuration
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.virtual-host=/
#spring.messaging.stomp.relay.enabled=true

# RabbitMQ STOMP Configuration (for WebSocket STOMP Broker Relay)
spring.rabbitmq.stomp.host=localhost
spring.rabbitmq.stomp.port=61613
spring.rabbitmq.stomp.login=guest
spring.rabbitmq.stomp.passcode=guest

# WebSocket Configuration for monitoring and debugging
logging.level.org.springframework.messaging=INFO
logging.level.org.springframework.web.socket=INFO

# Disable debug mode for cleaner output
debug=false
