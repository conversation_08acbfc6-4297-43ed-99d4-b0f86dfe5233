import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { scheduleAPI, employeeAPI } from '../../lib/api'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card'
import { Alert, AlertDescription } from '../ui/Alert'
import { Modal } from '../ui/Modal'
import { Clock, Edit, Save, X, Plus } from 'lucide-react'

const DAYS_OF_WEEK = [
  'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'
]

const DAY_LABELS = {
  'MONDAY': 'Monday',
  'TUESDAY': 'Tuesday', 
  'WEDNESDAY': 'Wednesday',
  'THURSDAY': 'Thursday',
  'FRIDAY': 'Friday',
  'SATURDAY': 'Saturday',
  'SUNDAY': 'Sunday'
}

const ScheduleManagement = ({ shopId, employeeId, isOwnSchedule = false }) => {
  const queryClient = useQueryClient()
  const [editingSchedule, setEditingSchedule] = useState(false)
  const [scheduleData, setScheduleData] = useState({})
  const [errors, setErrors] = useState({})

  // Fetch employee schedule
  const { data: schedule, isLoading, error } = useQuery({
    queryKey: ['employeeSchedule', employeeId],
    queryFn: () => scheduleAPI.getEmployeeSchedule(employeeId),
    enabled: !!employeeId
  })

  // Fetch shop employees if not viewing own schedule
  const { data: employees } = useQuery({
    queryKey: ['shopEmployees', shopId],
    queryFn: () => employeeAPI.getShopEmployees(shopId),
    enabled: !!shopId && !isOwnSchedule
  })

  // Update schedule mutation
  const updateScheduleMutation = useMutation({
    mutationFn: ({ employeeId, scheduleData }) => 
      scheduleAPI.updateEmployeeSchedule(employeeId, scheduleData),
    onSuccess: () => {
      queryClient.invalidateQueries(['employeeSchedule', employeeId])
      setEditingSchedule(false)
      setErrors({})
    },
    onError: (error) => {
      setErrors({ general: error.response?.data?.message || 'Failed to update schedule' })
    }
  })

  const initializeScheduleData = () => {
    const currentSchedule = schedule?.data?.schedule || {}
    const initialData = {}
    
    DAYS_OF_WEEK.forEach(day => {
      const daySlots = currentSchedule[day] || []
      if (daySlots.length > 0) {
        // Use the first slot for each day (assuming one slot per day for simplicity)
        const slot = daySlots[0]
        initialData[day] = {
          enabled: true,
          startTime: slot.startTime,
          endTime: slot.endTime
        }
      } else {
        initialData[day] = {
          enabled: false,
          startTime: '09:00',
          endTime: '17:00'
        }
      }
    })
    
    setScheduleData(initialData)
  }

  const handleEditClick = () => {
    initializeScheduleData()
    setEditingSchedule(true)
  }

  const handleDayToggle = (day) => {
    setScheduleData(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        enabled: !prev[day]?.enabled
      }
    }))
  }

  const handleTimeChange = (day, field, value) => {
    setScheduleData(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [field]: value
      }
    }))
  }

  const validateSchedule = () => {
    const newErrors = {}
    
    Object.entries(scheduleData).forEach(([day, data]) => {
      if (data.enabled) {
        if (!data.startTime || !data.endTime) {
          newErrors[day] = 'Start and end times are required'
        } else if (data.startTime >= data.endTime) {
          newErrors[day] = 'End time must be after start time'
        }
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = () => {
    if (!validateSchedule()) return

    // Convert to API format
    const scheduleSlots = []
    Object.entries(scheduleData).forEach(([day, data]) => {
      if (data.enabled) {
        scheduleSlots.push({
          dayOfWeek: day,
          startTime: data.startTime,
          endTime: data.endTime
        })
      }
    })

    updateScheduleMutation.mutate({
      employeeId,
      scheduleData: scheduleSlots
    })
  }

  const handleCancel = () => {
    setEditingSchedule(false)
    setScheduleData({})
    setErrors({})
  }

  if (isLoading) {
    return (
      <Card className="animate-pulse">
        <CardContent className="p-6">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="space-y-2">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="h-3 bg-gray-200 rounded w-full"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load schedule. Please try again later.
        </AlertDescription>
      </Alert>
    )
  }

  const currentSchedule = schedule?.data?.schedule || {}
  const employeeName = schedule?.data?.employeeName || 'Employee'

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {isOwnSchedule ? 'My Schedule' : `${employeeName}'s Schedule`}
          </h2>
          <p className="text-gray-600">Manage working hours and availability</p>
        </div>
        {!editingSchedule && (
          <Button onClick={handleEditClick} className="flex items-center">
            <Edit className="w-4 h-4 mr-2" />
            Edit Schedule
          </Button>
        )}
      </div>

      {errors.general && (
        <Alert variant="destructive">
          <AlertDescription>{errors.general}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Weekly Schedule
          </CardTitle>
          <CardDescription>
            {editingSchedule ? 'Edit working hours for each day' : 'Current working hours'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {DAYS_OF_WEEK.map(day => {
              const daySlots = currentSchedule[day] || []
              const hasSlots = daySlots.length > 0
              const dayData = scheduleData[day]

              return (
                <div key={day} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    {editingSchedule && (
                      <input
                        type="checkbox"
                        checked={dayData?.enabled || false}
                        onChange={() => handleDayToggle(day)}
                        className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                      />
                    )}
                    <div className="w-24 font-medium text-gray-900">
                      {DAY_LABELS[day]}
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    {editingSchedule ? (
                      dayData?.enabled ? (
                        <>
                          <Input
                            type="time"
                            value={dayData.startTime || '09:00'}
                            onChange={(e) => handleTimeChange(day, 'startTime', e.target.value)}
                            className="w-32"
                          />
                          <span className="text-gray-500">to</span>
                          <Input
                            type="time"
                            value={dayData.endTime || '17:00'}
                            onChange={(e) => handleTimeChange(day, 'endTime', e.target.value)}
                            className="w-32"
                          />
                          {errors[day] && (
                            <span className="text-red-500 text-sm">{errors[day]}</span>
                          )}
                        </>
                      ) : (
                        <span className="text-gray-500 italic">Day off</span>
                      )
                    ) : (
                      hasSlots ? (
                        <div className="text-gray-700">
                          {daySlots.map((slot, index) => (
                            <span key={index} className="mr-2">
                              {slot.formattedTimeRange}
                            </span>
                          ))}
                        </div>
                      ) : (
                        <span className="text-gray-500 italic">Day off</span>
                      )
                    )}
                  </div>
                </div>
              )
            })}
          </div>

          {editingSchedule && (
            <div className="flex justify-end gap-3 mt-6 pt-4 border-t">
              <Button variant="outline" onClick={handleCancel}>
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>
              <Button 
                onClick={handleSave}
                disabled={updateScheduleMutation.isLoading}
              >
                <Save className="w-4 h-4 mr-2" />
                Save Schedule
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {!editingSchedule && (
        <Card>
          <CardHeader>
            <CardTitle>Schedule Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Working Days:</span>
                <div className="text-gray-900">
                  {Object.values(currentSchedule).filter(slots => slots.length > 0).length} days
                </div>
              </div>
              <div>
                <span className="font-medium text-gray-700">Total Hours:</span>
                <div className="text-gray-900">
                  {Object.values(currentSchedule)
                    .flat()
                    .reduce((total, slot) => total + (slot.durationMinutes || 0), 0) / 60} hours/week
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default ScheduleManagement
