import React, { useState, useEffect } from 'react'
import { useSearchParams, Link } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { Search, MapPin, Filter, Star, Clock, DollarSign } from 'lucide-react'
import { shopAPI } from '../../lib/api'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card'
import { Badge } from '../ui/Badge'

const SearchPage = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '')
  const [location, setLocation] = useState('')
  const [selectedTypes, setSelectedTypes] = useState([])
  const [priceRange, setPriceRange] = useState('all')
  const [sortBy, setSortBy] = useState('rating')
  const shopTypes = [
    'HAIRDRESSER', 'BARBER', 'NAIL_STYLIST', 'SPA', 'MASSAGE',
    'BEAUTY_SALON', 'EYEBROW_THREADING', 'TATTOO_PARLOR', 'WELLNESS_CENTER'
  ]

  // Fetch shops from API
  const { data: shopsData, isLoading, error } = useQuery({
    queryKey: ['shops', searchQuery, selectedTypes, priceRange, sortBy],
    queryFn: () => shopAPI.getShops({
      name: searchQuery || undefined,
      sortBy: sortBy === 'rating' ? 'ratingAverage' : sortBy,
      sortDir: 'desc'
    }),
    enabled: true
  })

  const shops = shopsData?.data?.content || []

  const handleSearch = (e) => {
    e.preventDefault()
    setSearchParams({ q: searchQuery })
  }

  const handleLocationSearch = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation(`${position.coords.latitude}, ${position.coords.longitude}`)
          // Here you would typically reverse geocode to get a readable address
        },
        (error) => {
          console.error('Error getting location:', error)
          alert('Unable to get your location. Please enter manually.')
        }
      )
    }
  }

  const toggleShopType = (type) => {
    setSelectedTypes(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type)
        : [...prev, type]
    )
  }

  const filteredShops = shops.filter(shop => {
    if (selectedTypes.length > 0) {
      return shop.businessTypes?.some(type => selectedTypes.includes(type))
    }
    return true
  })

  // Helper function to format business types for display
  const formatBusinessType = (type) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  // Helper function to get price range indicator
  const getPriceRange = (shop) => {
    // This would typically come from the backend or be calculated
    return "$$" // Default for now
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search Query */}
            <form onSubmit={handleSearch} className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search shops, services..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </form>

            {/* Location */}
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Enter location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                className="pl-10 pr-20"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleLocationSearch}
                className="absolute right-1 top-1/2 transform -translate-y-1/2"
              >
                Use GPS
              </Button>
            </div>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
            >
              <option value="rating">Sort by Rating</option>
              <option value="distance">Sort by Distance</option>
              <option value="price">Sort by Price</option>
              <option value="availability">Sort by Availability</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Filter className="mr-2 h-4 w-4" />
                  Filters
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Shop Types */}
                <div>
                  <h4 className="font-medium mb-3">Service Types</h4>
                  <div className="space-y-2">
                    {shopTypes.map(type => (
                      <label key={type} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={selectedTypes.includes(type)}
                          onChange={() => toggleShopType(type)}
                          className="rounded border-gray-300"
                        />
                        <span className="text-sm">{formatBusinessType(type)}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Price Range */}
                <div>
                  <h4 className="font-medium mb-3">Price Range</h4>
                  <div className="space-y-2">
                    {[
                      { value: 'all', label: 'All Prices' },
                      { value: '$', label: '$ - Budget' },
                      { value: '$$', label: '$$ - Moderate' },
                      { value: '$$$', label: '$$$ - Premium' },
                      { value: '$$$$', label: '$$$$ - Luxury' }
                    ].map(option => (
                      <label key={option.value} className="flex items-center space-x-2">
                        <input
                          type="radio"
                          name="priceRange"
                          value={option.value}
                          checked={priceRange === option.value}
                          onChange={(e) => setPriceRange(e.target.value)}
                          className="border-gray-300"
                        />
                        <span className="text-sm">{option.label}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Results */}
          <div className="lg:col-span-3">
            <div className="mb-4 flex justify-between items-center">
              <h2 className="text-2xl font-bold">
                {searchQuery ? `Results for "${searchQuery}"` : 'Beauty Shops Near You'}
              </h2>
              <span className="text-gray-600">
                {filteredShops.length} shops found
              </span>
            </div>

            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[1, 2, 3, 4].map(i => (
                  <Card key={i} className="animate-pulse">
                    <div className="h-48 bg-gray-200 rounded-t-lg"></div>
                    <CardContent className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {filteredShops.map(shop => (
                  <Card key={shop.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="h-48 bg-gray-200 relative">
                      {(shop.thumbnail || (shop.gallery && shop.gallery.length > 0)) ? (
                        <img
                          src={`http://localhost:8080${shop.thumbnail || shop.gallery[0]}`}
                          alt={shop.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.style.display = 'none'
                            e.target.nextSibling.style.display = 'flex'
                          }}
                        />
                      ) : null}
                      <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center" style={{display: (shop.thumbnail || (shop.gallery && shop.gallery.length > 0)) ? 'none' : 'flex'}}>
                        <span className="text-gray-500 text-sm">No Image</span>
                      </div>
                      <Badge className="absolute top-2 right-2 bg-white text-gray-900">
                        {getPriceRange(shop)}
                      </Badge>
                    </div>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-semibold text-lg">{shop.name}</h3>
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-400 fill-current" />
                          <span className="ml-1 text-sm font-medium">{shop.ratingAverage?.toFixed(1) || 'N/A'}</span>
                          <span className="ml-1 text-sm text-gray-500">({shop.ratingCount || 0})</span>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-1 mb-2">
                        {shop.businessTypes?.map(type => (
                          <Badge key={type} variant="secondary" className="text-xs">
                            {formatBusinessType(type)}
                          </Badge>
                        ))}
                      </div>

                      <div className="space-y-1 text-sm text-gray-600 mb-3">
                        <div className="flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          {shop.address}, {shop.city}, {shop.state}
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          Next available: Today 2:00 PM
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button asChild variant="outline" className="flex-1 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white">
                          <Link to={`/shop/${shop.id}`}>View Details</Link>
                        </Button>
                        <Button asChild className="flex-1 bg-gray-900 hover:bg-gray-800 text-white">
                          <Link to={`/shop/${shop.id}/book`}>Book Now</Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {!isLoading && filteredShops.length === 0 && (
              <Card className="text-center py-12">
                <CardContent>
                  <h3 className="text-lg font-medium mb-2">No shops found</h3>
                  <p className="text-gray-600 mb-4">
                    Try adjusting your search criteria or location.
                  </p>
                  <Button
                    onClick={() => {
                      setSearchQuery('')
                      setSelectedTypes([])
                      setPriceRange('all')
                    }}
                    className="bg-gray-900 hover:bg-gray-800 text-white"
                  >
                    Clear Filters
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default SearchPage
