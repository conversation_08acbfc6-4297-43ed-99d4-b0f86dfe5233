import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '../ui/Button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card'
import webSocketService from '../../lib/websocket'
import useAuthStore from '../../store/authStore'

const SimpleSubscriptionTest = () => {
  const { token } = useAuthStore()
  const [connectionStatus, setConnectionStatus] = useState('disconnected')
  const [subscriptionStatus, setSubscriptionStatus] = useState('not_subscribed')
  const [messages, setMessages] = useState([])
  const [testResults, setTestResults] = useState([])

  const testTopic = 'slots.dfdfe915-da70-4ec1-a7bb-b52d0bb74788.79236043-1fdd-4c7e-b200-74a57730f344.0e41472f-3b93-4821-8026-ee4b11d85068.2025-06-11'

  const addMessage = (message) => {
    const timestamp = new Date().toLocaleTimeString()
    setMessages(prev => [...prev, { ...message, timestamp }])
  }

  const addTestResult = (test, status, details) => {
    const timestamp = new Date().toLocaleTimeString()
    setTestResults(prev => [...prev, { test, status, details, timestamp }])
  }

  // Monitor connection status
  useEffect(() => {
    const checkConnection = () => {
      const connected = webSocketService.isConnected()
      setConnectionStatus(connected ? 'connected' : 'disconnected')
    }

    checkConnection()
    const interval = setInterval(checkConnection, 1000)

    return () => clearInterval(interval)
  }, [])

  const testConnection = async () => {
    try {
      addTestResult('Connection', 'running', 'Connecting to WebSocket...')
      
      await webSocketService.connect(token)
      
      if (webSocketService.isConnected()) {
        addTestResult('Connection', 'success', 'WebSocket connected')
        return true
      } else {
        addTestResult('Connection', 'error', 'Connection failed')
        return false
      }
    } catch (error) {
      addTestResult('Connection', 'error', `Connection failed: ${error.message}`)
      return false
    }
  }

  const testSubscription = () => {
    try {
      addTestResult('Subscription', 'running', 'Subscribing to test topic...')
      
      console.log('🧪 Test: Subscribing to topic:', testTopic)
      
      const unsubscribe = webSocketService.subscribe(testTopic, (message) => {
        console.log('🧪 Test: Received message:', message)
        addMessage({
          type: 'slot_update',
          topic: testTopic,
          data: message
        })
      })
      
      setSubscriptionStatus('subscribed')
      addTestResult('Subscription', 'success', `Subscribed to: ${testTopic}`)
      
      // Wait and check if we get confirmation
      setTimeout(() => {
        addTestResult('Confirmation Check', 'info', 'Checking for backend confirmation...')
      }, 3000)
      
      return unsubscribe
    } catch (error) {
      addTestResult('Subscription', 'error', `Subscription failed: ${error.message}`)
      return null
    }
  }

  const testDirectMessage = () => {
    try {
      addTestResult('Direct Message', 'running', 'Sending direct WebSocket message...')
      
      const testMessage = {
        type: 'SLOT_UPDATE',
        action: 'LOCKED',
        shopId: 'dfdfe915-da70-4ec1-a7bb-b52d0bb74788',
        serviceId: '79236043-1fdd-4c7e-b200-74a57730f344',
        employeeId: '0e41472f-3b93-4821-8026-ee4b11d85068',
        dateTime: '2025-06-11T09:00:00',
        date: '2025-06-11',
        time: '09:00',
        userId: 'test-user',
        timestamp: Date.now()
      }
      
      webSocketService.send({
        type: 'test_message',
        topic: testTopic,
        data: testMessage
      })
      
      addTestResult('Direct Message', 'success', 'Test message sent')
    } catch (error) {
      addTestResult('Direct Message', 'error', `Failed to send message: ${error.message}`)
    }
  }

  const runFullTest = async () => {
    setMessages([])
    setTestResults([])
    
    addTestResult('Full Test', 'running', 'Starting subscription test...')
    
    // Step 1: Connect
    const connected = await testConnection()
    if (!connected) return
    
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Step 2: Subscribe
    const unsubscribe = testSubscription()
    if (!unsubscribe) return
    
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // Step 3: Send test message
    testDirectMessage()
    
    addTestResult('Full Test', 'success', 'Test completed - check messages panel')
  }

  const clearLogs = () => {
    setMessages([])
    setTestResults([])
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return 'text-green-600'
      case 'subscribed': return 'text-blue-600'
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'running': return 'text-blue-600'
      case 'info': return 'text-gray-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected': return '🟢'
      case 'subscribed': return '🔔'
      case 'success': return '✅'
      case 'error': return '❌'
      case 'running': return '⏳'
      case 'info': return 'ℹ️'
      default: return '⚪'
    }
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Simple Subscription Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Status */}
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <span>Connection:</span>
                <span className={getStatusColor(connectionStatus)}>
                  {getStatusIcon(connectionStatus)} {connectionStatus}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span>Subscription:</span>
                <span className={getStatusColor(subscriptionStatus)}>
                  {getStatusIcon(subscriptionStatus)} {subscriptionStatus}
                </span>
              </div>
            </div>

            {/* Test Topic */}
            <div className="p-3 bg-gray-50 rounded">
              <h4 className="font-semibold text-sm">Test Topic:</h4>
              <p className="font-mono text-xs break-all">{testTopic}</p>
            </div>

            {/* Controls */}
            <div className="flex flex-wrap gap-2">
              <Button onClick={runFullTest} className="bg-blue-600 hover:bg-blue-700">
                Run Full Test
              </Button>
              <Button onClick={testConnection} variant="outline">
                Test Connection
              </Button>
              <Button onClick={testSubscription} variant="outline">
                Test Subscription
              </Button>
              <Button onClick={testDirectMessage} variant="outline">
                Send Test Message
              </Button>
              <Button onClick={clearLogs} variant="outline">
                Clear Logs
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Results and Messages */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Results */}
        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-start gap-3 p-2 bg-gray-50 rounded">
                    <span>{getStatusIcon(result.status)}</span>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{result.test}</span>
                        <span className={`text-sm ${getStatusColor(result.status)}`}>
                          {result.status.toUpperCase()}
                        </span>
                        <span className="text-xs text-gray-500">{result.timestamp}</span>
                      </div>
                      <p className="text-sm text-gray-600">{result.details}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Messages */}
        {messages.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Received Messages</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {messages.map((message, index) => (
                  <div key={index} className="p-2 bg-green-50 rounded">
                    <div className="flex items-center gap-2 text-sm">
                      <span className="font-medium">{message.type}</span>
                      <span className="text-gray-500">{message.timestamp}</span>
                    </div>
                    <pre className="text-xs mt-1 overflow-x-auto">
                      {JSON.stringify(message.data, null, 2)}
                    </pre>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Test Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-600">
            <p><strong>1. Run Full Test:</strong> Connects, subscribes, and sends a test message</p>
            <p><strong>2. Check Console:</strong> Look for detailed WebSocket logs</p>
            <p><strong>3. Check Backend:</strong> Should see subscription registration in backend logs</p>
            <p><strong>4. Expected:</strong> Should receive subscription confirmation and test messages</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default SimpleSubscriptionTest
