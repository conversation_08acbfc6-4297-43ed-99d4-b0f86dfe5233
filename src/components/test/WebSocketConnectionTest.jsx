import React, { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '../ui/Button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card'
import webSocketService from '../../lib/websocket'
import useAuthStore from '../../store/authStore'

const WebSocketConnectionTest = () => {
  const [connectionStatus, setConnectionStatus] = useState('disconnected')
  const [subscriptionStatus, setSubscriptionStatus] = useState('not_subscribed')
  const [messages, setMessages] = useState([])
  const [testResults, setTestResults] = useState([])
  const { token } = useAuthStore()
  const subscriptionRef = useRef(null)
  const connectionCheckRef = useRef(null)

  const addMessage = (message) => {
    const timestamp = new Date().toLocaleTimeString()
    setMessages(prev => [...prev, { ...message, timestamp }])
  }

  const addTestResult = (test, status, details) => {
    const timestamp = new Date().toLocaleTimeString()
    setTestResults(prev => [...prev, { test, status, details, timestamp }])
  }

  // Monitor connection status
  useEffect(() => {
    const checkConnection = () => {
      const connected = webSocketService.isConnected()
      setConnectionStatus(connected ? 'connected' : 'disconnected')
    }

    checkConnection()
    connectionCheckRef.current = setInterval(checkConnection, 1000)

    return () => {
      if (connectionCheckRef.current) {
        clearInterval(connectionCheckRef.current)
      }
    }
  }, [])

  const testConnection = async () => {
    try {
      addTestResult('Connection', 'running', 'Attempting to connect...')
      
      await webSocketService.connect(token)
      
      if (webSocketService.isConnected()) {
        addTestResult('Connection', 'success', 'WebSocket connected successfully')
      } else {
        addTestResult('Connection', 'error', 'Connection failed - not connected after attempt')
      }
    } catch (error) {
      addTestResult('Connection', 'error', `Connection failed: ${error.message}`)
    }
  }

  const testSubscription = () => {
    try {
      addTestResult('Subscription', 'running', 'Attempting to subscribe...')
      
      const testTopic = 'slots.test-shop.test-service.test-employee.2025-01-20'
      
      const unsubscribe = webSocketService.subscribe(testTopic, (message) => {
        addMessage({
          type: 'subscription_message',
          topic: testTopic,
          data: message
        })
      })
      
      subscriptionRef.current = unsubscribe
      setSubscriptionStatus('subscribed')
      addTestResult('Subscription', 'success', `Subscribed to topic: ${testTopic}`)
    } catch (error) {
      addTestResult('Subscription', 'error', `Subscription failed: ${error.message}`)
    }
  }

  const testUnsubscription = () => {
    try {
      addTestResult('Unsubscription', 'running', 'Attempting to unsubscribe...')
      
      if (subscriptionRef.current) {
        webSocketService.unsubscribe(subscriptionRef.current)
        subscriptionRef.current = null
        setSubscriptionStatus('not_subscribed')
        addTestResult('Unsubscription', 'success', 'Successfully unsubscribed')
      } else {
        addTestResult('Unsubscription', 'error', 'No active subscription to unsubscribe from')
      }
    } catch (error) {
      addTestResult('Unsubscription', 'error', `Unsubscription failed: ${error.message}`)
    }
  }

  const testSlotLocking = () => {
    try {
      addTestResult('Slot Locking', 'running', 'Testing slot lock WebSocket message...')
      
      const testSlotData = {
        shopId: 'test-shop',
        serviceId: 'test-service',
        employeeId: 'test-employee',
        dateTime: '2025-01-20T10:00:00'
      }
      
      webSocketService.lockSlot(testSlotData)
      addTestResult('Slot Locking', 'success', 'Slot lock message sent via WebSocket')
    } catch (error) {
      addTestResult('Slot Locking', 'error', `Slot locking failed: ${error.message}`)
    }
  }

  const testPing = () => {
    try {
      addTestResult('Ping', 'running', 'Sending ping...')
      webSocketService.sendPing()
      addTestResult('Ping', 'success', 'Ping sent successfully')
    } catch (error) {
      addTestResult('Ping', 'error', `Ping failed: ${error.message}`)
    }
  }

  const disconnect = () => {
    try {
      addTestResult('Disconnect', 'running', 'Disconnecting...')
      webSocketService.disconnect()
      setSubscriptionStatus('not_subscribed')
      addTestResult('Disconnect', 'success', 'Disconnected successfully')
    } catch (error) {
      addTestResult('Disconnect', 'error', `Disconnect failed: ${error.message}`)
    }
  }

  const clearLogs = () => {
    setMessages([])
    setTestResults([])
  }

  const runAllTests = async () => {
    clearLogs()
    
    // Test 1: Connection
    await testConnection()
    
    // Wait a bit for connection to stabilize
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Test 2: Subscription
    testSubscription()
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Test 3: Ping
    testPing()
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Test 4: Slot locking
    testSlotLocking()
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Test 5: Unsubscription
    testUnsubscription()
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return 'text-green-600'
      case 'subscribed': return 'text-blue-600'
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'running': return 'text-yellow-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected': return '🟢'
      case 'subscribed': return '🔔'
      case 'success': return '✅'
      case 'error': return '❌'
      case 'running': return '⏳'
      default: return '⚪'
    }
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>WebSocket Connection Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Status indicators */}
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <span>Connection:</span>
                <span className={getStatusColor(connectionStatus)}>
                  {getStatusIcon(connectionStatus)} {connectionStatus}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span>Subscription:</span>
                <span className={getStatusColor(subscriptionStatus)}>
                  {getStatusIcon(subscriptionStatus)} {subscriptionStatus}
                </span>
              </div>
            </div>

            {/* Control buttons */}
            <div className="flex flex-wrap gap-2">
              <Button onClick={runAllTests} className="bg-blue-600 hover:bg-blue-700">
                Run All Tests
              </Button>
              <Button onClick={testConnection} variant="outline">
                Test Connection
              </Button>
              <Button onClick={testSubscription} variant="outline">
                Test Subscription
              </Button>
              <Button onClick={testPing} variant="outline">
                Send Ping
              </Button>
              <Button onClick={testSlotLocking} variant="outline">
                Test Slot Lock
              </Button>
              <Button onClick={testUnsubscription} variant="outline">
                Unsubscribe
              </Button>
              <Button onClick={disconnect} variant="outline">
                Disconnect
              </Button>
              <Button onClick={clearLogs} variant="outline">
                Clear Logs
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-start gap-3 p-2 bg-gray-50 rounded">
                  <span>{getStatusIcon(result.status)}</span>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{result.test}</span>
                      <span className={`text-sm ${getStatusColor(result.status)}`}>
                        {result.status.toUpperCase()}
                      </span>
                      <span className="text-xs text-gray-500">{result.timestamp}</span>
                    </div>
                    <p className="text-sm text-gray-600">{result.details}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Messages */}
      {messages.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>WebSocket Messages</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {messages.map((message, index) => (
                <div key={index} className="p-2 bg-blue-50 rounded">
                  <div className="flex items-center gap-2 text-sm">
                    <span className="font-medium">{message.type}</span>
                    <span className="text-gray-500">{message.timestamp}</span>
                  </div>
                  <pre className="text-xs mt-1 overflow-x-auto">
                    {JSON.stringify(message.data, null, 2)}
                  </pre>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default WebSocketConnectionTest
