import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '../ui/Button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card'
import usePersistentSlotUpdates from '../../hooks/usePersistentSlotUpdates'
import webSocketService from '../../lib/websocket'
import { appointmentAPI } from '../../lib/api'

/**
 * Test component for the enhanced WebSocket slot workflow
 * Tests the subscription lifecycle: subscribe -> unsubscribe -> re-subscribe
 */
const EnhancedSlotWorkflowTest = () => {
  const [isOnSlotSelection, setIsOnSlotSelection] = useState(true)
  const [messages, setMessages] = useState([])
  const [selectedDate, setSelectedDate] = useState('2025-01-20')
  const [lockedSlot, setLockedSlot] = useState(null)
  const [isLocking, setIsLocking] = useState(false)

  // Test data
  const testData = {
    shopId: '123e4567-e89b-12d3-a456-426614174000',
    serviceId: '123e4567-e89b-12d3-a456-426614174001',
    employeeId: '123e4567-e89b-12d3-a456-426614174002'
  }

  const addMessage = (message) => {
    const timestamp = new Date().toLocaleTimeString()
    setMessages(prev => [...prev, { ...message, timestamp }])
  }

  const handleSlotUpdate = (update) => {
    console.log('🎯 Received slot update:', update)

    // Handle different message formats
    let slotData = update
    if (update.data && update.data.type === 'SLOT_UPDATE') {
      slotData = update.data
    }

    addMessage({
      type: 'slot-update',
      content: `Slot update: ${slotData.action || 'unknown'} - ${slotData.dateTime || 'unknown time'}`,
      data: slotData
    })
  }

  // Use the enhanced hook
  const { isConnected, isSubscribed, connectionError, currentTopic } = usePersistentSlotUpdates(
    testData.shopId,
    testData.serviceId,
    testData.employeeId,
    selectedDate,
    handleSlotUpdate,
    isOnSlotSelection
  )

  useEffect(() => {
    addMessage({ 
      type: 'info', 
      content: `Hook state changed: Connected=${isConnected}, Subscribed=${isSubscribed}, Topic=${currentTopic}` 
    })
  }, [isConnected, isSubscribed, currentTopic])

  const connectWebSocket = async () => {
    try {
      await webSocketService.connect('test-token')
      addMessage({ type: 'success', content: 'WebSocket connected successfully' })
    } catch (error) {
      addMessage({ type: 'error', content: `Connection failed: ${error.message}` })
    }
  }

  const disconnectWebSocket = () => {
    webSocketService.disconnect()
    addMessage({ type: 'info', content: 'WebSocket disconnected' })
  }

  const simulateSlotSelection = () => {
    setIsOnSlotSelection(true)
    addMessage({ type: 'action', content: 'Simulated: User on slot selection screen' })
  }

  const lockTestSlot = async () => {
    if (isLocking) return

    setIsLocking(true)
    addMessage({ type: 'action', content: 'Attempting to lock test slot...' })

    try {
      const testSlotDateTime = `${selectedDate}T10:00:00`
      const lockRequest = {
        shopId: testData.shopId,
        serviceId: testData.serviceId,
        employeeId: testData.employeeId,
        dateTime: testSlotDateTime
      }

      console.log('🔒 Locking slot with request:', lockRequest)
      const response = await appointmentAPI.lockSlot(lockRequest)
      console.log('🔒 Lock response:', response)

      if (response.lockToken || response.data?.lockToken) {
        setLockedSlot({
          ...lockRequest,
          lockToken: response.lockToken || response.data.lockToken
        })
        setIsOnSlotSelection(false) // Move to payment simulation
        addMessage({
          type: 'success',
          content: `✅ Slot locked successfully! Token: ${response.lockToken || response.data.lockToken}`
        })
      } else {
        addMessage({ type: 'error', content: '❌ Failed to lock slot - no token received' })
      }
    } catch (error) {
      console.error('❌ Failed to lock slot:', error)
      addMessage({
        type: 'error',
        content: `❌ Failed to lock slot: ${error.response?.data?.message || error.message}`
      })
    } finally {
      setIsLocking(false)
    }
  }

  const unlockTestSlot = async () => {
    if (!lockedSlot || isLocking) return

    setIsLocking(true)
    addMessage({ type: 'action', content: 'Attempting to unlock test slot...' })

    try {
      console.log('🔓 Unlocking slot:', lockedSlot)
      await appointmentAPI.unlockSlot(lockedSlot)

      setLockedSlot(null)
      setIsOnSlotSelection(true) // Return to slot selection
      addMessage({ type: 'success', content: '✅ Slot unlocked successfully!' })
    } catch (error) {
      console.error('❌ Failed to unlock slot:', error)
      addMessage({
        type: 'error',
        content: `❌ Failed to unlock slot: ${error.response?.data?.message || error.message}`
      })
    } finally {
      setIsLocking(false)
    }
  }

  const simulateBackToSlots = () => {
    setIsOnSlotSelection(true)
    addMessage({ type: 'action', content: 'Simulated: User went back to slot selection' })
  }

  const changeDateAndStayOnSlots = () => {
    const newDate = selectedDate === '2025-01-20' ? '2025-01-21' : '2025-01-20'
    setSelectedDate(newDate)
    addMessage({ type: 'action', content: `Simulated: Date changed to ${newDate} while on slot selection` })
  }

  const clearMessages = () => {
    setMessages([])
  }

  const getMessageColor = (type) => {
    switch (type) {
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'slot-update': return 'text-blue-600'
      case 'action': return 'text-purple-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Enhanced WebSocket Slot Workflow Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Status Display */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded">
            <div>
              <strong>Connected:</strong> 
              <span className={isConnected ? 'text-green-600' : 'text-red-600'}>
                {isConnected ? ' ✅' : ' ❌'}
              </span>
            </div>
            <div>
              <strong>Subscribed:</strong> 
              <span className={isSubscribed ? 'text-green-600' : 'text-red-600'}>
                {isSubscribed ? ' ✅' : ' ❌'}
              </span>
            </div>
            <div>
              <strong>On Slot Selection:</strong> 
              <span className={isOnSlotSelection ? 'text-green-600' : 'text-orange-600'}>
                {isOnSlotSelection ? ' ✅' : ' ❌'}
              </span>
            </div>
            <div>
              <strong>Date:</strong> 
              <span className="text-blue-600">{selectedDate}</span>
            </div>
          </div>

          {/* Current Topic */}
          {currentTopic && (
            <div className="p-3 bg-blue-50 rounded">
              <strong>Current Topic:</strong> <code className="text-sm">{currentTopic}</code>
            </div>
          )}

          {/* Locked Slot Info */}
          {lockedSlot && (
            <div className="p-3 bg-yellow-50 rounded">
              <strong>Locked Slot:</strong>
              <div className="text-sm mt-1">
                <div>DateTime: {lockedSlot.dateTime}</div>
                <div>Token: <code>{lockedSlot.lockToken}</code></div>
              </div>
            </div>
          )}

          {/* Connection Error */}
          {connectionError && (
            <div className="p-3 bg-red-50 text-red-700 rounded">
              <strong>Error:</strong> {connectionError}
            </div>
          )}

          {/* Control Buttons */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Button onClick={connectWebSocket} variant="outline" size="sm">
              Connect WS
            </Button>
            <Button onClick={disconnectWebSocket} variant="outline" size="sm">
              Disconnect WS
            </Button>
            <Button onClick={clearMessages} variant="outline" size="sm">
              Clear Messages
            </Button>
            <Button onClick={changeDateAndStayOnSlots} variant="outline" size="sm">
              Change Date
            </Button>
          </div>

          {/* Real Slot Locking Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
            <Button
              onClick={simulateSlotSelection}
              className="bg-green-600 hover:bg-green-700"
              disabled={isOnSlotSelection}
            >
              📅 Go to Slot Selection
            </Button>
            <Button
              onClick={lockTestSlot}
              className="bg-orange-600 hover:bg-orange-700"
              disabled={!isOnSlotSelection || isLocking || !!lockedSlot}
            >
              {isLocking ? '⏳ Locking...' : '🔒 Lock Real Slot'}
            </Button>
            <Button
              onClick={unlockTestSlot}
              className="bg-red-600 hover:bg-red-700"
              disabled={!lockedSlot || isLocking}
            >
              {isLocking ? '⏳ Unlocking...' : '🔓 Unlock Real Slot'}
            </Button>
            <Button
              onClick={simulateBackToSlots}
              className="bg-blue-600 hover:bg-blue-700"
              disabled={isOnSlotSelection}
            >
              ↩️ Back to Slot Selection
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Messages Log */}
      <Card>
        <CardHeader>
          <CardTitle>WebSocket Messages Log</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-96 overflow-y-auto border rounded p-4 bg-gray-50">
            {messages.length === 0 ? (
              <p className="text-gray-500 italic">No messages yet...</p>
            ) : (
              messages.map((msg, index) => (
                <div key={index} className="mb-2 text-sm">
                  <span className="text-gray-400">[{msg.timestamp}]</span>
                  <span className={`ml-2 ${getMessageColor(msg.type)}`}>
                    {msg.content}
                  </span>
                  {msg.data && (
                    <pre className="ml-4 mt-1 text-xs text-gray-600 bg-white p-2 rounded">
                      {JSON.stringify(msg.data, null, 2)}
                    </pre>
                  )}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Expected Workflow */}
      <Card>
        <CardHeader>
          <CardTitle>Expected Workflow</CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li><strong>Connect WebSocket:</strong> Click "Connect WS" to establish connection</li>
            <li><strong>Auto-Subscribe:</strong> Hook should automatically subscribe when on slot selection</li>
            <li><strong>Lock Real Slot:</strong> Click "Lock Real Slot" to actually lock a slot via API (triggers WebSocket broadcast)</li>
            <li><strong>Observe Real-time:</strong> Open another browser tab to see the slot lock update in real-time</li>
            <li><strong>Unlock Real Slot:</strong> Click "Unlock Real Slot" to release the lock (triggers WebSocket broadcast)</li>
            <li><strong>Back to Slots:</strong> Click "Back to Slot Selection" to re-subscribe after simulated payment</li>
            <li><strong>Change Date:</strong> While on slot selection, change date (should resubscribe to new topic)</li>
            <li><strong>Multi-User Testing:</strong> Use multiple browser tabs to simulate concurrent users</li>
          </ol>
        </CardContent>
      </Card>
    </div>
  )
}

export default EnhancedSlotWorkflowTest
