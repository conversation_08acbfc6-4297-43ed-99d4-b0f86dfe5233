import React, { useState, useMemo } from 'react'
import { <PERSON><PERSON> } from '../ui/Button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card'

const DateExtractionTest = () => {
  const [testSlots, setTestSlots] = useState([
    { dateTime: '2025-06-11T09:00:00', available: true },
    { dateTime: '2025-06-11T10:00:00', available: true },
    { dateTime: '2025-06-11T11:00:00', available: false }
  ])
  
  const [selectedDate, setSelectedDate] = useState('2025-06-10')

  // Extract the actual date from available slots for WebSocket subscription
  const actualSlotDate = useMemo(() => {
    if (testSlots?.length > 0) {
      // Extract date from the first slot's dateTime
      const firstSlotDateTime = testSlots[0].dateTime
      const slotDate = firstSlotDateTime.split('T')[0] // Get YYYY-MM-DD part
      console.log('📅 Extracted actual slot date for WebSocket:', slotDate, 'from slot:', firstSlotDateTime)
      return slotDate
    }
    // Fallback to selectedDate if no slots available yet
    console.log('📅 Using selectedDate as fallback for WebSocket:', selectedDate)
    return selectedDate
  }, [testSlots, selectedDate])

  const addTestSlot = () => {
    const newTime = `${selectedDate}T${String(12 + testSlots.length).padStart(2, '0')}:00:00`
    setTestSlots(prev => [...prev, { dateTime: newTime, available: true }])
  }

  const clearSlots = () => {
    setTestSlots([])
  }

  const updateSelectedDate = (newDate) => {
    setSelectedDate(newDate)
  }

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Date Extraction Test</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Current State */}
          <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded">
            <div>
              <h4 className="font-semibold">Selected Date (State)</h4>
              <p className="text-lg font-mono">{selectedDate}</p>
            </div>
            <div>
              <h4 className="font-semibold">Actual Slot Date (WebSocket)</h4>
              <p className="text-lg font-mono text-blue-600">{actualSlotDate}</p>
            </div>
          </div>

          {/* Date Mismatch Warning */}
          {selectedDate !== actualSlotDate && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
              <h4 className="font-semibold text-yellow-800">⚠️ Date Mismatch Detected!</h4>
              <p className="text-yellow-700">
                The WebSocket will subscribe to <strong>{actualSlotDate}</strong> but the UI shows <strong>{selectedDate}</strong>.
                This is the root cause of the real-time update issue!
              </p>
            </div>
          )}

          {/* Controls */}
          <div className="flex gap-2">
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => updateSelectedDate(e.target.value)}
              className="px-3 py-2 border rounded"
            />
            <Button onClick={addTestSlot}>Add Test Slot</Button>
            <Button onClick={clearSlots} variant="outline">Clear Slots</Button>
          </div>

          {/* Test Slots */}
          <div>
            <h4 className="font-semibold mb-2">Test Slots ({testSlots.length})</h4>
            <div className="space-y-2">
              {testSlots.map((slot, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-white border rounded">
                  <span className="font-mono">{slot.dateTime}</span>
                  <span className={`px-2 py-1 rounded text-xs ${
                    slot.available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {slot.available ? 'Available' : 'Unavailable'}
                  </span>
                </div>
              ))}
              {testSlots.length === 0 && (
                <p className="text-gray-500 italic">No slots available</p>
              )}
            </div>
          </div>

          {/* WebSocket Topic Preview */}
          <div className="p-4 bg-blue-50 rounded">
            <h4 className="font-semibold text-blue-800">WebSocket Topic Preview</h4>
            <p className="font-mono text-sm text-blue-700">
              slots.dfdfe915-da70-4ec1-a7bb-b52d0bb74788.79236043-1fdd-4c7e-b200-74a57730f344.0e41472f-3b93-4821-8026-ee4b11d85068.{actualSlotDate}
            </p>
          </div>

          {/* Test Scenarios */}
          <div className="space-y-2">
            <h4 className="font-semibold">Test Scenarios</h4>
            <Button 
              onClick={() => {
                setSelectedDate('2025-06-10')
                setTestSlots([
                  { dateTime: '2025-06-11T09:00:00', available: true },
                  { dateTime: '2025-06-11T10:00:00', available: true }
                ])
              }}
              variant="outline"
              className="mr-2"
            >
              Scenario 1: Date Mismatch (Bug)
            </Button>
            <Button 
              onClick={() => {
                setSelectedDate('2025-06-11')
                setTestSlots([
                  { dateTime: '2025-06-11T09:00:00', available: true },
                  { dateTime: '2025-06-11T10:00:00', available: true }
                ])
              }}
              variant="outline"
              className="mr-2"
            >
              Scenario 2: Date Match (Fixed)
            </Button>
            <Button 
              onClick={() => {
                setSelectedDate('2025-06-10')
                setTestSlots([])
              }}
              variant="outline"
            >
              Scenario 3: No Slots (Fallback)
            </Button>
          </div>

          {/* Explanation */}
          <div className="p-4 bg-green-50 rounded">
            <h4 className="font-semibold text-green-800">✅ Fix Explanation</h4>
            <p className="text-green-700 text-sm">
              The fix extracts the actual date from the available slots instead of using the selectedDate state.
              This ensures the WebSocket subscription matches the date of the slots being displayed and locked.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default DateExtractionTest
