import React, { useState, useRef } from 'react'
import { But<PERSON> } from '../ui/Button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { appointmentAPI } from '../../lib/api'

const SlotLockCleanupTest = () => {
  const [testResults, setTestResults] = useState([])
  const [isRunning, setIsRunning] = useState(false)

  const testData = {
    shopId: '123e4567-e89b-12d3-a456-426614174000',
    serviceId: '123e4567-e89b-12d3-a456-426614174001',
    employeeId: '123e4567-e89b-12d3-a456-426614174002',
    dateTime: '2025-01-20T10:00:00'
  }

  const addResult = (test, status, message) => {
    setTestResults(prev => [...prev, {
      test,
      status,
      message,
      timestamp: new Date().toLocaleTimeString()
    }])
  }

  const testSlotLocking = async () => {
    try {
      addResult('Lock Slot', 'running', 'Attempting to lock slot...')
      
      const response = await appointmentAPI.lockSlot(testData)
      const lockToken = response.lockToken || response.data?.lockToken
      
      if (lockToken) {
        addResult('Lock Slot', 'success', `Slot locked with token: ${lockToken.substring(0, 8)}...`)
        return lockToken
      } else {
        addResult('Lock Slot', 'error', 'No lock token received')
        return null
      }
    } catch (error) {
      addResult('Lock Slot', 'error', `Failed to lock slot: ${error.message}`)
      return null
    }
  }

  const testSlotUnlocking = async (lockToken) => {
    try {
      addResult('Unlock Slot', 'running', 'Attempting to unlock slot...')
      
      await appointmentAPI.unlockSlot({
        ...testData,
        lockToken
      })
      
      addResult('Unlock Slot', 'success', 'Slot unlocked successfully')
      return true
    } catch (error) {
      addResult('Unlock Slot', 'error', `Failed to unlock slot: ${error.message}`)
      return false
    }
  }

  const testPaymentLockRelease = async (lockToken) => {
    try {
      addResult('Payment Lock Release', 'running', 'Attempting to release payment lock...')
      
      await appointmentAPI.releasePaymentLock({
        ...testData,
        lockToken
      })
      
      addResult('Payment Lock Release', 'success', 'Payment lock released successfully')
      return true
    } catch (error) {
      addResult('Payment Lock Release', 'error', `Failed to release payment lock: ${error.message}`)
      return false
    }
  }

  const testSessionStorageCleanup = () => {
    addResult('Session Storage', 'running', 'Testing session storage cleanup...')
    
    // Add some test tokens
    const testTokens = [
      `lockToken-${testData.shopId}-${testData.serviceId}-${testData.employeeId}-2025-01-20T10:00:00`,
      `lockToken-${testData.shopId}-${testData.serviceId}-${testData.employeeId}-2025-01-20T11:00:00`,
      'lockToken-invalid-format'
    ]
    
    testTokens.forEach(key => {
      sessionStorage.setItem(key, 'test-token-value')
    })
    
    const beforeCount = Object.keys(sessionStorage).filter(key => key.startsWith('lockToken-')).length
    
    // Simulate cleanup (this would normally be done by the component)
    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith('lockToken-') && key.includes('invalid')) {
        sessionStorage.removeItem(key)
      }
    })
    
    const afterCount = Object.keys(sessionStorage).filter(key => key.startsWith('lockToken-')).length
    
    addResult('Session Storage', 'success', `Cleaned up ${beforeCount - afterCount} invalid tokens`)
  }

  const runAllTests = async () => {
    setIsRunning(true)
    setTestResults([])
    
    try {
      // Test 1: Lock a slot
      const lockToken = await testSlotLocking()
      
      if (lockToken) {
        // Test 2: Unlock the slot
        await testSlotUnlocking(lockToken)
        
        // Test 3: Lock again for payment test
        const lockToken2 = await testSlotLocking()
        
        if (lockToken2) {
          // Test 4: Release payment lock
          await testPaymentLockRelease(lockToken2)
        }
      }
      
      // Test 5: Session storage cleanup
      testSessionStorageCleanup()
      
      addResult('All Tests', 'success', 'Test suite completed')
    } catch (error) {
      addResult('All Tests', 'error', `Test suite failed: ${error.message}`)
    } finally {
      setIsRunning(false)
    }
  }

  const clearResults = () => {
    setTestResults([])
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'running': return 'text-blue-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅'
      case 'error': return '❌'
      case 'running': return '⏳'
      default: return '⚪'
    }
  }

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Slot Lock Cleanup Test Suite</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex gap-2">
            <Button 
              onClick={runAllTests} 
              disabled={isRunning}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isRunning ? 'Running Tests...' : 'Run All Tests'}
            </Button>
            <Button 
              onClick={clearResults} 
              variant="outline"
              disabled={isRunning}
            >
              Clear Results
            </Button>
          </div>

          {testResults.length > 0 && (
            <div className="border rounded-lg p-4 bg-gray-50">
              <h3 className="font-semibold mb-3">Test Results</h3>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-start gap-3 p-2 bg-white rounded border">
                    <span className="text-lg">{getStatusIcon(result.status)}</span>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{result.test}</span>
                        <span className={`text-sm ${getStatusColor(result.status)}`}>
                          {result.status.toUpperCase()}
                        </span>
                        <span className="text-xs text-gray-500">{result.timestamp}</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded">
            <h4 className="font-semibold mb-2">Test Coverage:</h4>
            <ul className="space-y-1">
              <li>• Slot locking functionality</li>
              <li>• Slot unlocking functionality</li>
              <li>• Payment lock release functionality</li>
              <li>• Session storage cleanup</li>
              <li>• Error handling for all scenarios</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default SlotLockCleanupTest
