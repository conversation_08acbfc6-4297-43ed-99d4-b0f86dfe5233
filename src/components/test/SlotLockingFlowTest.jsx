import React, { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '../ui/Button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Input } from '../ui/Input'
import webSocketService from '../../lib/websocket'
import { appointmentAPI } from '../../lib/api'
import useAuthStore from '../../store/authStore'

const SlotLockingFlowTest = () => {
  const { token } = useAuthStore()
  const [connectionStatus, setConnectionStatus] = useState('disconnected')
  const [subscriptionStatus, setSubscriptionStatus] = useState('not_subscribed')
  const [messages, setMessages] = useState([])
  const [testResults, setTestResults] = useState([])
  
  // Test parameters - using real IDs from the logs and current date
  const [testParams, setTestParams] = useState(() => {
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)
    const tomorrowStr = tomorrow.toISOString().split('T')[0]

    return {
      shopId: 'dfdfe915-da70-4ec1-a7bb-b52d0bb74788',
      serviceId: '79236043-1fdd-4c7e-b200-74a57730f344',
      employeeId: '0e41472f-3b93-4821-8026-ee4b11d85068',
      dateTime: `${tomorrowStr}T09:00:00`,
      date: tomorrowStr
    }
  })
  
  const subscriptionRef = useRef(null)
  const connectionCheckRef = useRef(null)

  const addMessage = (message) => {
    const timestamp = new Date().toLocaleTimeString()
    setMessages(prev => [...prev, { ...message, timestamp }])
  }

  const addTestResult = (test, status, details) => {
    const timestamp = new Date().toLocaleTimeString()
    setTestResults(prev => [...prev, { test, status, details, timestamp }])
  }

  // Monitor connection status
  useEffect(() => {
    const checkConnection = () => {
      const connected = webSocketService.isConnected()
      setConnectionStatus(connected ? 'connected' : 'disconnected')
    }

    checkConnection()
    connectionCheckRef.current = setInterval(checkConnection, 1000)

    return () => {
      if (connectionCheckRef.current) {
        clearInterval(connectionCheckRef.current)
      }
    }
  }, [])

  const testConnection = async () => {
    try {
      addTestResult('Connection', 'running', 'Attempting to connect...')
      
      await webSocketService.connect(token)
      
      if (webSocketService.isConnected()) {
        addTestResult('Connection', 'success', 'WebSocket connected successfully')
        return true
      } else {
        addTestResult('Connection', 'error', 'Connection failed - not connected after attempt')
        return false
      }
    } catch (error) {
      addTestResult('Connection', 'error', `Connection failed: ${error.message}`)
      return false
    }
  }

  const testSubscription = () => {
    try {
      addTestResult('Subscription', 'running', 'Attempting to subscribe...')
      
      const topic = `slots.${testParams.shopId}.${testParams.serviceId}.${testParams.employeeId}.${testParams.date}`
      
      const unsubscribe = webSocketService.subscribe(topic, (message) => {
        addMessage({
          type: 'slot_update_received',
          topic: topic,
          data: message
        })
        console.log('🎯 Slot update received in test:', message)
      })
      
      subscriptionRef.current = unsubscribe
      setSubscriptionStatus('subscribed')
      addTestResult('Subscription', 'success', `Subscribed to topic: ${topic}`)
      return true
    } catch (error) {
      addTestResult('Subscription', 'error', `Subscription failed: ${error.message}`)
      return false
    }
  }

  const testSlotLocking = async () => {
    try {
      addTestResult('Slot Locking', 'running', 'Attempting to lock slot via API...')
      
      const lockRequest = {
        shopId: testParams.shopId,
        serviceId: testParams.serviceId,
        employeeId: testParams.employeeId,
        dateTime: testParams.dateTime
      }
      
      console.log('🔒 Locking slot with request:', lockRequest)
      
      const response = await appointmentAPI.lockSlot(lockRequest)
      
      if (response.lockToken || response.data?.lockToken) {
        addTestResult('Slot Locking', 'success', `Slot locked successfully. Token: ${response.lockToken || response.data?.lockToken}`)
        
        // Wait a bit to see if WebSocket message arrives
        setTimeout(() => {
          addTestResult('WebSocket Notification', 'info', 'Waiting for WebSocket notification...')
        }, 1000)
        
        return true
      } else {
        addTestResult('Slot Locking', 'error', 'No lock token received')
        return false
      }
    } catch (error) {
      addTestResult('Slot Locking', 'error', `Slot locking failed: ${error.message}`)
      return false
    }
  }

  const testSlotUnlocking = async () => {
    try {
      addTestResult('Slot Unlocking', 'running', 'Attempting to unlock slot via API...')
      
      const unlockRequest = {
        shopId: testParams.shopId,
        serviceId: testParams.serviceId,
        employeeId: testParams.employeeId,
        dateTime: testParams.dateTime,
        lockToken: 'test-token' // This might fail but should still trigger broadcast
      }
      
      console.log('🔓 Unlocking slot with request:', unlockRequest)
      
      try {
        await appointmentAPI.unlockSlot(unlockRequest)
        addTestResult('Slot Unlocking', 'success', 'Slot unlocked successfully')
      } catch (error) {
        addTestResult('Slot Unlocking', 'warning', `Unlock API failed (expected): ${error.message}`)
      }
      
      // Wait a bit to see if WebSocket message arrives
      setTimeout(() => {
        addTestResult('WebSocket Notification', 'info', 'Waiting for unlock WebSocket notification...')
      }, 1000)
      
      return true
    } catch (error) {
      addTestResult('Slot Unlocking', 'error', `Slot unlocking failed: ${error.message}`)
      return false
    }
  }

  const testDirectWebSocketMessage = () => {
    try {
      addTestResult('Direct WebSocket', 'running', 'Sending direct WebSocket slot lock message...')
      
      const slotData = {
        shopId: testParams.shopId,
        serviceId: testParams.serviceId,
        employeeId: testParams.employeeId,
        dateTime: testParams.dateTime
      }
      
      webSocketService.lockSlot(slotData)
      addTestResult('Direct WebSocket', 'success', 'Direct WebSocket slot lock message sent')
      return true
    } catch (error) {
      addTestResult('Direct WebSocket', 'error', `Direct WebSocket failed: ${error.message}`)
      return false
    }
  }

  const runFullTest = async () => {
    setMessages([])
    setTestResults([])
    
    addTestResult('Full Test', 'running', 'Starting comprehensive slot locking flow test...')
    
    // Step 1: Connect
    const connected = await testConnection()
    if (!connected) return
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Step 2: Subscribe
    const subscribed = testSubscription()
    if (!subscribed) return
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Step 3: Test slot locking via API
    await testSlotLocking()
    
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Step 4: Test slot unlocking via API
    await testSlotUnlocking()
    
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Step 5: Test direct WebSocket message
    testDirectWebSocketMessage()
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    addTestResult('Full Test', 'success', 'Comprehensive test completed')
  }

  const clearLogs = () => {
    setMessages([])
    setTestResults([])
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return 'text-green-600'
      case 'subscribed': return 'text-blue-600'
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'warning': return 'text-yellow-600'
      case 'running': return 'text-blue-600'
      case 'info': return 'text-gray-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected': return '🟢'
      case 'subscribed': return '🔔'
      case 'success': return '✅'
      case 'error': return '❌'
      case 'warning': return '⚠️'
      case 'running': return '⏳'
      case 'info': return 'ℹ️'
      default: return '⚪'
    }
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Comprehensive Slot Locking Flow Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Status indicators */}
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <span>Connection:</span>
                <span className={getStatusColor(connectionStatus)}>
                  {getStatusIcon(connectionStatus)} {connectionStatus}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span>Subscription:</span>
                <span className={getStatusColor(subscriptionStatus)}>
                  {getStatusIcon(subscriptionStatus)} {subscriptionStatus}
                </span>
              </div>
            </div>

            {/* Test parameters */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Shop ID</label>
                <Input
                  value={testParams.shopId}
                  onChange={(e) => setTestParams(prev => ({ ...prev, shopId: e.target.value }))}
                  className="text-xs"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Service ID</label>
                <Input
                  value={testParams.serviceId}
                  onChange={(e) => setTestParams(prev => ({ ...prev, serviceId: e.target.value }))}
                  className="text-xs"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Employee ID</label>
                <Input
                  value={testParams.employeeId}
                  onChange={(e) => setTestParams(prev => ({ ...prev, employeeId: e.target.value }))}
                  className="text-xs"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Date Time</label>
                <Input
                  value={testParams.dateTime}
                  onChange={(e) => setTestParams(prev => ({ ...prev, dateTime: e.target.value }))}
                  className="text-xs"
                />
              </div>
            </div>

            {/* Control buttons */}
            <div className="flex flex-wrap gap-2">
              <Button onClick={runFullTest} className="bg-blue-600 hover:bg-blue-700">
                Run Full Test
              </Button>
              <Button onClick={testConnection} variant="outline">
                Test Connection
              </Button>
              <Button onClick={testSubscription} variant="outline">
                Test Subscription
              </Button>
              <Button onClick={testSlotLocking} variant="outline">
                Test Slot Lock
              </Button>
              <Button onClick={testSlotUnlocking} variant="outline">
                Test Slot Unlock
              </Button>
              <Button onClick={testDirectWebSocketMessage} variant="outline">
                Direct WebSocket
              </Button>
              <Button onClick={clearLogs} variant="outline">
                Clear Logs
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Results and Messages in a grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Results */}
        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-start gap-3 p-2 bg-gray-50 rounded">
                    <span>{getStatusIcon(result.status)}</span>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{result.test}</span>
                        <span className={`text-sm ${getStatusColor(result.status)}`}>
                          {result.status.toUpperCase()}
                        </span>
                        <span className="text-xs text-gray-500">{result.timestamp}</span>
                      </div>
                      <p className="text-sm text-gray-600">{result.details}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* WebSocket Messages */}
        {messages.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>WebSocket Messages</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {messages.map((message, index) => (
                  <div key={index} className="p-2 bg-blue-50 rounded">
                    <div className="flex items-center gap-2 text-sm">
                      <span className="font-medium">{message.type}</span>
                      <span className="text-gray-500">{message.timestamp}</span>
                    </div>
                    <pre className="text-xs mt-1 overflow-x-auto">
                      {JSON.stringify(message.data, null, 2)}
                    </pre>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

export default SlotLockingFlowTest
