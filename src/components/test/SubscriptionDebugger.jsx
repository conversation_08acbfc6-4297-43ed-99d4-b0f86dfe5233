import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '../ui/Button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { DatePicker } from '../ui/DatePicker'
import usePersistentSlotUpdates from '../../hooks/usePersistentSlotUpdates'

const SubscriptionDebugger = () => {
  const [selectedDate, setSelectedDate] = useState(() => {
    const today = new Date()
    return today.toISOString().split('T')[0]
  })
  
  const [messages, setMessages] = useState([])
  
  // Test parameters
  const testParams = {
    shopId: 'dfdfe915-da70-4ec1-a7bb-b52d0bb74788',
    serviceId: '79236043-1fdd-4c7e-b200-74a57730f344',
    employeeId: '0e41472f-3b93-4821-8026-ee4b11d85068'
  }

  const addMessage = (message) => {
    const timestamp = new Date().toLocaleTimeString()
    setMessages(prev => [...prev, { ...message, timestamp }])
  }

  // Use the persistent slot updates hook
  const { isConnected, isSubscribed, connectionError, currentTopic, forceResubscribe } = usePersistentSlotUpdates(
    testParams.shopId,
    testParams.serviceId,
    testParams.employeeId,
    selectedDate,
    (update) => {
      addMessage({
        type: 'slot_update',
        data: update
      })
    }
  )

  const formatLocalDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  const changeToToday = () => {
    const today = new Date()
    const todayStr = formatLocalDate(today)
    console.log('📅 Changing to today:', todayStr)
    console.log('📅 Previous selectedDate:', selectedDate)
    setSelectedDate(todayStr)
  }

  const changeToTomorrow = () => {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    const tomorrowStr = formatLocalDate(tomorrow)
    console.log('📅 Changing to tomorrow:', tomorrowStr)
    console.log('📅 Previous selectedDate:', selectedDate)
    setSelectedDate(tomorrowStr)
  }

  // Debug effect to track selectedDate changes
  useEffect(() => {
    console.log('🔍 SubscriptionDebugger selectedDate changed to:', selectedDate)
  }, [selectedDate])

  const clearMessages = () => {
    setMessages([])
  }

  const manualResubscribe = () => {
    console.log('🔄 Manual resubscribe triggered')
    console.log('📅 Current selectedDate:', selectedDate)
    console.log('📅 Current topic:', currentTopic)

    // Force a date change to trigger resubscription
    const currentDate = selectedDate
    setSelectedDate('temp-date')
    setTimeout(() => {
      setSelectedDate(currentDate)
    }, 100)
  }

  const getStatusColor = (status) => {
    if (status) return 'text-green-600'
    return 'text-red-600'
  }

  const getStatusIcon = (status) => {
    if (status) return '✅'
    return '❌'
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>WebSocket Subscription Debugger</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Current Status */}
            <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded">
              <div>
                <h4 className="font-semibold">Connection Status</h4>
                <p className={`text-lg ${getStatusColor(isConnected)}`}>
                  {getStatusIcon(isConnected)} {isConnected ? 'Connected' : 'Disconnected'}
                </p>
              </div>
              <div>
                <h4 className="font-semibold">Subscription Status</h4>
                <p className={`text-lg ${getStatusColor(isSubscribed)}`}>
                  {getStatusIcon(isSubscribed)} {isSubscribed ? 'Subscribed' : 'Not Subscribed'}
                </p>
              </div>
            </div>

            {/* Current Topic */}
            <div className="p-4 bg-blue-50 rounded">
              <h4 className="font-semibold text-blue-800">Current Topic</h4>
              <p className="font-mono text-sm text-blue-700 break-all">
                {currentTopic || 'No active subscription'}
              </p>
            </div>

            {/* Date Selection */}
            <div className="space-y-2">
              <h4 className="font-semibold">Selected Date: {selectedDate}</h4>
              <div className="flex gap-2">
                <DatePicker
                  date={selectedDate ? new Date(selectedDate) : null}
                  onDateChange={(date) => {
                    if (date) {
                      const newDate = formatLocalDate(date)
                      console.log('📅 DatePicker changed to:', newDate)
                      setSelectedDate(newDate)
                    }
                  }}
                  placeholder="Select date"
                  className="w-64"
                />
                <Button onClick={changeToToday} variant="outline">
                  Today
                </Button>
                <Button onClick={changeToTomorrow} variant="outline">
                  Tomorrow
                </Button>
              </div>
            </div>

            {/* Expected Topic */}
            <div className="p-4 bg-green-50 rounded">
              <h4 className="font-semibold text-green-800">Expected Topic</h4>
              <p className="font-mono text-sm text-green-700 break-all">
                slots.{testParams.shopId}.{testParams.serviceId}.{testParams.employeeId}.{selectedDate}
              </p>
            </div>

            {/* Topic Match Check */}
            {currentTopic && (
              <div className={`p-4 rounded ${
                currentTopic.endsWith(selectedDate) ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
              } border`}>
                <h4 className={`font-semibold ${
                  currentTopic.endsWith(selectedDate) ? 'text-green-800' : 'text-red-800'
                }`}>
                  Topic Match: {currentTopic.endsWith(selectedDate) ? '✅ CORRECT' : '❌ MISMATCH'}
                </h4>
                {!currentTopic.endsWith(selectedDate) && (
                  <p className="text-red-700 text-sm mt-1">
                    The subscription topic doesn't match the selected date. This will cause missed real-time updates!
                  </p>
                )}
              </div>
            )}

            {/* Connection Error */}
            {connectionError && (
              <div className="p-4 bg-red-50 border border-red-200 rounded">
                <h4 className="font-semibold text-red-800">Connection Error</h4>
                <p className="text-red-700 text-sm">{connectionError}</p>
              </div>
            )}

            {/* Controls */}
            <div className="flex gap-2">
              <Button
                onClick={() => {
                  console.log('🔄 Force resubscribing...')
                  forceResubscribe()
                }}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Force Resubscribe
              </Button>
              <Button
                onClick={manualResubscribe}
                className="bg-green-600 hover:bg-green-700"
              >
                Manual Resubscribe
              </Button>
              <Button onClick={clearMessages} variant="outline">
                Clear Messages
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Messages */}
      {messages.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>WebSocket Messages ({messages.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {messages.map((message, index) => (
                <div key={index} className="p-2 bg-blue-50 rounded">
                  <div className="flex items-center gap-2 text-sm">
                    <span className="font-medium">{message.type}</span>
                    <span className="text-gray-500">{message.timestamp}</span>
                  </div>
                  <pre className="text-xs mt-1 overflow-x-auto">
                    {JSON.stringify(message.data, null, 2)}
                  </pre>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-600">
            <p><strong>1. Check Topic Match:</strong> The "Current Topic" should match the "Expected Topic"</p>
            <p><strong>2. Change Date:</strong> Use the date picker or buttons to change dates</p>
            <p><strong>3. Verify Resubscription:</strong> The topic should update immediately when date changes</p>
            <p><strong>4. Test Real-time:</strong> Open another window and lock a slot for the selected date</p>
            <p><strong>5. Expected Result:</strong> You should see slot update messages appear in the messages panel</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default SubscriptionDebugger
