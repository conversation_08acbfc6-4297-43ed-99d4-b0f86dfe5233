import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import webSocketService from '../../lib/websocket'

const WebSocketPerformanceMonitor = () => {
  const [stats, setStats] = useState({
    connected: false,
    activeConnections: 0,
    totalConnections: 0,
    totalMessages: 0,
    topicStats: {},
    subscriptions: 0
  })
  const [messages, setMessages] = useState([])
  const [isMonitoring, setIsMonitoring] = useState(false)

  const addMessage = (message) => {
    setMessages(prev => [
      { ...message, timestamp: new Date().toLocaleTimeString() },
      ...prev.slice(0, 49) // Keep only last 50 messages for performance
    ])
  }

  useEffect(() => {
    if (!isMonitoring) return

    const connectAndMonitor = async () => {
      try {
        await webSocketService.connect('test-token')
        setStats(prev => ({ ...prev, connected: true }))
        addMessage({ type: 'success', content: 'WebSocket connected for monitoring' })

        // Subscribe to monitoring stats
        const unsubscribe = webSocketService.subscribeToMonitoringStats((data) => {
          setStats(prev => ({
            ...prev,
            ...data,
            connected: webSocketService.isConnected()
          }))
          addMessage({ type: 'stats', content: `Stats updated: ${data.activeConnections} connections` })
        })

        // Request stats every 5 seconds
        const statsInterval = setInterval(() => {
          webSocketService.requestStats()
        }, 5000)

        return () => {
          unsubscribe()
          clearInterval(statsInterval)
        }
      } catch (error) {
        addMessage({ type: 'error', content: `Connection failed: ${error.message}` })
        setStats(prev => ({ ...prev, connected: false }))
      }
    }

    connectAndMonitor()
  }, [isMonitoring])

  const startMonitoring = () => {
    setIsMonitoring(true)
    setMessages([])
  }

  const stopMonitoring = () => {
    setIsMonitoring(false)
    webSocketService.disconnect()
    setStats({
      connected: false,
      activeConnections: 0,
      totalConnections: 0,
      totalMessages: 0,
      topicStats: {},
      subscriptions: 0
    })
  }

  const testSlotSubscription = () => {
    const testShopId = '123e4567-e89b-12d3-a456-426614174000'
    const testServiceId = '123e4567-e89b-12d3-a456-426614174001'
    const testDate = '2025-01-15'

    const unsubscribe = webSocketService.subscribeToSlotUpdates(
      testShopId,
      testServiceId,
      testDate,
      (update) => {
        addMessage({ 
          type: 'slot-update', 
          content: `Slot update: ${JSON.stringify(update)}` 
        })
      }
    )

    addMessage({ 
      type: 'info', 
      content: `Subscribed to slots.${testShopId}.${testServiceId}.${testDate}` 
    })

    // Auto-unsubscribe after 30 seconds for testing
    setTimeout(() => {
      unsubscribe()
      addMessage({ type: 'info', content: 'Auto-unsubscribed from test slot updates' })
    }, 30000)
  }

  const testMultipleSubscriptions = () => {
    const testData = [
      { shopId: '123e4567-e89b-12d3-a456-426614174000', serviceId: '123e4567-e89b-12d3-a456-426614174001', date: '2025-01-15' },
      { shopId: '123e4567-e89b-12d3-a456-426614174002', serviceId: '123e4567-e89b-12d3-a456-426614174003', date: '2025-01-16' },
      { shopId: '123e4567-e89b-12d3-a456-426614174004', serviceId: '123e4567-e89b-12d3-a456-426614174005', date: '2025-01-17' }
    ]

    const unsubscribeFunctions = testData.map(({ shopId, serviceId, date }) => {
      return webSocketService.subscribeToSlotUpdates(
        shopId,
        serviceId,
        date,
        (update) => {
          addMessage({ 
            type: 'slot-update', 
            content: `Multi-sub update for ${shopId}: ${JSON.stringify(update)}` 
          })
        }
      )
    })

    addMessage({ 
      type: 'info', 
      content: `Created ${testData.length} test subscriptions` 
    })

    // Auto-cleanup after 60 seconds
    setTimeout(() => {
      unsubscribeFunctions.forEach(unsubscribe => unsubscribe())
      addMessage({ type: 'info', content: 'Cleaned up multiple test subscriptions' })
    }, 60000)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>WebSocket Performance Monitor</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex gap-2">
              {!isMonitoring ? (
                <Button onClick={startMonitoring}>Start Monitoring</Button>
              ) : (
                <Button onClick={stopMonitoring} variant="destructive">Stop Monitoring</Button>
              )}
              
              {isMonitoring && (
                <>
                  <Button onClick={testSlotSubscription} variant="outline">
                    Test Slot Subscription
                  </Button>
                  <Button onClick={testMultipleSubscriptions} variant="outline">
                    Test Multiple Subscriptions
                  </Button>
                </>
              )}
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="p-3 bg-gray-50 rounded">
                <div className="text-sm text-gray-600">Connection</div>
                <div className={`font-bold ${stats.connected ? 'text-green-600' : 'text-red-600'}`}>
                  {stats.connected ? 'Connected' : 'Disconnected'}
                </div>
              </div>
              
              <div className="p-3 bg-gray-50 rounded">
                <div className="text-sm text-gray-600">Active Connections</div>
                <div className="font-bold text-blue-600">{stats.activeConnections}</div>
              </div>
              
              <div className="p-3 bg-gray-50 rounded">
                <div className="text-sm text-gray-600">Total Messages</div>
                <div className="font-bold text-purple-600">{stats.totalMessages}</div>
              </div>
              
              <div className="p-3 bg-gray-50 rounded">
                <div className="text-sm text-gray-600">Topics</div>
                <div className="font-bold text-orange-600">
                  {Object.keys(stats.topicStats || {}).length}
                </div>
              </div>
            </div>

            {stats.topicStats && Object.keys(stats.topicStats).length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Topic Subscriptions</h4>
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {Object.entries(stats.topicStats).map(([topic, count]) => (
                    <div key={topic} className="flex justify-between text-sm">
                      <span className="truncate">{topic}</span>
                      <span className="font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Message Log</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {messages.map((message, index) => (
              <div key={index} className="flex gap-2 text-sm">
                <span className="text-gray-500 min-w-20">{message.timestamp}</span>
                <span className={`font-medium min-w-16 ${
                  message.type === 'success' ? 'text-green-600' :
                  message.type === 'error' ? 'text-red-600' :
                  message.type === 'slot-update' ? 'text-blue-600' :
                  message.type === 'stats' ? 'text-purple-600' :
                  'text-gray-600'
                }`}>
                  {message.type.toUpperCase()}
                </span>
                <span className="flex-1">{message.content}</span>
              </div>
            ))}
            {messages.length === 0 && (
              <div className="text-gray-500 text-center py-4">
                No messages yet. Start monitoring to see WebSocket activity.
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default WebSocketPerformanceMonitor
