import React, { useState } from 'react'
import { But<PERSON> } from '../ui/Button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { appointmentAPI } from '../../lib/api'

/**
 * Simple test component to verify slot locking API is working
 */
const SlotLockingAPITest = () => {
  const [messages, setMessages] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [lockedSlot, setLockedSlot] = useState(null)

  // Test data
  const testData = {
    shopId: '123e4567-e89b-12d3-a456-426614174000',
    serviceId: '123e4567-e89b-12d3-a456-426614174001',
    employeeId: '123e4567-e89b-12d3-a456-426614174002',
    dateTime: '2025-01-20T10:00:00'
  }

  const addMessage = (message) => {
    const timestamp = new Date().toLocaleTimeString()
    setMessages(prev => [...prev, { ...message, timestamp }])
  }

  const testLockSlot = async () => {
    if (isLoading) return
    
    setIsLoading(true)
    addMessage({ type: 'info', content: 'Testing slot lock API...' })
    
    try {
      console.log('🔒 Testing slot lock with data:', testData)
      const response = await appointmentAPI.lockSlot(testData)
      console.log('🔒 Lock response:', response)
      
      if (response.lockToken || response.data?.lockToken) {
        const lockToken = response.lockToken || response.data.lockToken
        setLockedSlot({ ...testData, lockToken })
        addMessage({ 
          type: 'success', 
          content: `✅ Slot locked successfully! Token: ${lockToken}`,
          data: response
        })
      } else {
        addMessage({ 
          type: 'error', 
          content: '❌ Lock failed - no token received',
          data: response
        })
      }
    } catch (error) {
      console.error('❌ Lock failed:', error)
      addMessage({ 
        type: 'error', 
        content: `❌ Lock failed: ${error.response?.data?.message || error.message}`,
        data: error.response?.data
      })
    } finally {
      setIsLoading(false)
    }
  }

  const testUnlockSlot = async () => {
    if (isLoading || !lockedSlot) return
    
    setIsLoading(true)
    addMessage({ type: 'info', content: 'Testing slot unlock API...' })
    
    try {
      console.log('🔓 Testing slot unlock with data:', lockedSlot)
      const response = await appointmentAPI.unlockSlot(lockedSlot)
      console.log('🔓 Unlock response:', response)
      
      setLockedSlot(null)
      addMessage({ 
        type: 'success', 
        content: '✅ Slot unlocked successfully!',
        data: response
      })
    } catch (error) {
      console.error('❌ Unlock failed:', error)
      addMessage({ 
        type: 'error', 
        content: `❌ Unlock failed: ${error.response?.data?.message || error.message}`,
        data: error.response?.data
      })
    } finally {
      setIsLoading(false)
    }
  }

  const clearMessages = () => {
    setMessages([])
  }

  const getMessageColor = (type) => {
    switch (type) {
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'info': return 'text-blue-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Slot Locking API Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Test Data Display */}
          <div className="p-4 bg-gray-50 rounded">
            <h4 className="font-semibold mb-2">Test Data:</h4>
            <div className="text-sm space-y-1">
              <div><strong>Shop ID:</strong> {testData.shopId}</div>
              <div><strong>Service ID:</strong> {testData.serviceId}</div>
              <div><strong>Employee ID:</strong> {testData.employeeId}</div>
              <div><strong>Date Time:</strong> {testData.dateTime}</div>
            </div>
          </div>

          {/* Locked Slot Info */}
          {lockedSlot && (
            <div className="p-4 bg-yellow-50 rounded">
              <h4 className="font-semibold mb-2">Currently Locked Slot:</h4>
              <div className="text-sm">
                <div><strong>Lock Token:</strong> <code>{lockedSlot.lockToken}</code></div>
              </div>
            </div>
          )}

          {/* Control Buttons */}
          <div className="flex gap-4">
            <Button 
              onClick={testLockSlot} 
              disabled={isLoading || !!lockedSlot}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isLoading ? '⏳ Locking...' : '🔒 Test Lock Slot'}
            </Button>
            <Button 
              onClick={testUnlockSlot} 
              disabled={isLoading || !lockedSlot}
              className="bg-red-600 hover:bg-red-700"
            >
              {isLoading ? '⏳ Unlocking...' : '🔓 Test Unlock Slot'}
            </Button>
            <Button 
              onClick={clearMessages} 
              variant="outline"
            >
              Clear Messages
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Messages Log */}
      <Card>
        <CardHeader>
          <CardTitle>API Response Log</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-96 overflow-y-auto border rounded p-4 bg-gray-50">
            {messages.length === 0 ? (
              <p className="text-gray-500 italic">No messages yet...</p>
            ) : (
              messages.map((msg, index) => (
                <div key={index} className="mb-3 text-sm">
                  <span className="text-gray-400">[{msg.timestamp}]</span>
                  <span className={`ml-2 ${getMessageColor(msg.type)}`}>
                    {msg.content}
                  </span>
                  {msg.data && (
                    <pre className="ml-4 mt-1 text-xs text-gray-600 bg-white p-2 rounded overflow-x-auto">
                      {JSON.stringify(msg.data, null, 2)}
                    </pre>
                  )}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li><strong>Test Lock:</strong> Click "Test Lock Slot" to lock a test slot via API</li>
            <li><strong>Check Response:</strong> Verify the API returns a lock token</li>
            <li><strong>Test Unlock:</strong> Click "Test Unlock Slot" to release the lock</li>
            <li><strong>Verify Backend:</strong> Check backend logs for slot locking events</li>
            <li><strong>WebSocket Events:</strong> If slot locking works, WebSocket events should be triggered</li>
          </ol>
        </CardContent>
      </Card>
    </div>
  )
}

export default SlotLockingAPITest
