import React, { useState, useEffect } from 'react'
import webSocketService from '../../lib/websocket'
import { appointmentAPI } from '../../lib/api'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'

const SlotUpdateTest = () => {
  const [isConnected, setIsConnected] = useState(false)
  const [messages, setMessages] = useState([])
  const [lockedSlots, setLockedSlots] = useState(new Set())
  const [testData, setTestData] = useState({
    shopId: 'dfdfe915-da70-4ec1-a7bb-b52d0bb74788',
    serviceId: '6aaffcfd-161e-4525-8824-68c79ec41b01',
    employeeId: '0e41472f-3b93-4821-8026-ee4b11d85068',
    dateTime: '2025-06-11T10:00:00'
  })

  const addMessage = (message) => {
    const timestamp = new Date().toLocaleTimeString()
    setMessages(prev => [...prev, { ...message, timestamp }])
  }

  const connectWebSocket = async () => {
    try {
      await webSocketService.connect('test-token')
      setIsConnected(true)
      addMessage({ type: 'success', content: 'WebSocket connected successfully' })

      // Subscribe to slot updates
      webSocketService.subscribeToSlotUpdates(
        testData.shopId,
        testData.serviceId,
        '2025-06-11',
        (update) => {
          addMessage({ type: 'slot-update', content: `Slot update received: ${JSON.stringify(update)}` })
          
          if (update.type === 'SLOT_UPDATE') {
            const { action, dateTime } = update
            if (action === 'LOCKED') {
              setLockedSlots(prev => new Set([...prev, dateTime]))
            } else if (action === 'UNLOCKED') {
              setLockedSlots(prev => {
                const newSet = new Set(prev)
                newSet.delete(dateTime)
                return newSet
              })
            }
          }
        }
      )

      // Subscribe to general messages
      webSocketService.subscribe('test', (data) => {
        addMessage({ type: 'general', content: `General message: ${JSON.stringify(data)}` })
      })

    } catch (error) {
      addMessage({ type: 'error', content: `Connection failed: ${error.message}` })
    }
  }

  const disconnectWebSocket = () => {
    webSocketService.disconnect()
    setIsConnected(false)
    addMessage({ type: 'info', content: 'WebSocket disconnected' })
  }

  const lockSlot = async () => {
    try {
      addMessage({ type: 'info', content: 'Attempting to lock slot...' })
      const response = await appointmentAPI.lockSlot(testData)
      addMessage({ type: 'success', content: `Slot locked: ${JSON.stringify(response)}` })
    } catch (error) {
      addMessage({ type: 'error', content: `Lock failed: ${error.message}` })
    }
  }

  const unlockSlot = async () => {
    try {
      addMessage({ type: 'info', content: 'Attempting to unlock slot...' })
      const response = await appointmentAPI.unlockSlot(testData)
      addMessage({ type: 'success', content: `Slot unlocked: ${JSON.stringify(response)}` })
    } catch (error) {
      addMessage({ type: 'error', content: `Unlock failed: ${error.message}` })
    }
  }

  const clearMessages = () => {
    setMessages([])
  }

  const getMessageColor = (type) => {
    switch (type) {
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'slot-update': return 'text-blue-600'
      case 'general': return 'text-purple-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Real-time Slot Update Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Connection Controls */}
          <div className="flex space-x-2">
            <Button 
              onClick={connectWebSocket} 
              disabled={isConnected}
              variant={isConnected ? "secondary" : "default"}
            >
              {isConnected ? 'Connected' : 'Connect WebSocket'}
            </Button>
            <Button 
              onClick={disconnectWebSocket} 
              disabled={!isConnected}
              variant="outline"
            >
              Disconnect
            </Button>
            <Button onClick={clearMessages} variant="outline">
              Clear Messages
            </Button>
          </div>

          {/* Test Data Configuration */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Shop ID</label>
              <Input
                value={testData.shopId}
                onChange={(e) => setTestData(prev => ({ ...prev, shopId: e.target.value }))}
                placeholder="Shop ID"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Service ID</label>
              <Input
                value={testData.serviceId}
                onChange={(e) => setTestData(prev => ({ ...prev, serviceId: e.target.value }))}
                placeholder="Service ID"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Employee ID</label>
              <Input
                value={testData.employeeId}
                onChange={(e) => setTestData(prev => ({ ...prev, employeeId: e.target.value }))}
                placeholder="Employee ID"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Date Time</label>
              <Input
                value={testData.dateTime}
                onChange={(e) => setTestData(prev => ({ ...prev, dateTime: e.target.value }))}
                placeholder="YYYY-MM-DDTHH:mm:ss"
              />
            </div>
          </div>

          {/* Slot Actions */}
          <div className="flex space-x-2">
            <Button onClick={lockSlot} disabled={!isConnected}>
              Lock Slot
            </Button>
            <Button onClick={unlockSlot} disabled={!isConnected} variant="outline">
              Unlock Slot
            </Button>
          </div>

          {/* Locked Slots Display */}
          <div>
            <h3 className="text-sm font-medium mb-2">Currently Locked Slots:</h3>
            <div className="bg-gray-100 p-2 rounded min-h-[40px]">
              {lockedSlots.size === 0 ? (
                <span className="text-gray-500">No slots locked</span>
              ) : (
                Array.from(lockedSlots).map(slot => (
                  <span key={slot} className="inline-block bg-red-100 text-red-800 px-2 py-1 rounded text-xs mr-2 mb-1">
                    {slot}
                  </span>
                ))
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Messages Log */}
      <Card>
        <CardHeader>
          <CardTitle>Messages Log</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-black text-white p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
            {messages.length === 0 ? (
              <div className="text-gray-400">No messages yet...</div>
            ) : (
              messages.map((message, index) => (
                <div key={index} className="mb-1">
                  <span className="text-gray-400">[{message.timestamp}]</span>{' '}
                  <span className={getMessageColor(message.type)}>
                    [{message.type.toUpperCase()}]
                  </span>{' '}
                  {message.content}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default SlotUpdateTest
