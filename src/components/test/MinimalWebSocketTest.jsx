import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '../ui/Button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card'
import webSocketService from '../../lib/websocket'

/**
 * Minimal test to isolate WebSocket subscription and broadcasting issues
 */
const MinimalWebSocketTest = () => {
  const [isConnected, setIsConnected] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [messages, setMessages] = useState([])
  const [backendStats, setBackendStats] = useState(null)

  // Use the exact topic from your issue
  const testTopic = 'slots.dfdfe915-da70-4ec1-a7bb-b52d0bb74788.79236043-1fdd-4c7e-b200-74a57730f344.0e41472f-3b93-4821-8026-ee4b11d85068.2025-06-11'

  const addMessage = (message) => {
    const timestamp = new Date().toLocaleTimeString()
    setMessages(prev => [...prev, { ...message, timestamp }])
  }

  const messageHandler = (data) => {
    console.log('📨 Received WebSocket message:', data)
    addMessage({
      type: 'message',
      content: 'WebSocket message received',
      data: data
    })
  }

  const connectWebSocket = async () => {
    try {
      addMessage({ type: 'info', content: '🔌 Connecting to WebSocket...' })
      await webSocketService.connect('test-token')
      setIsConnected(true)
      addMessage({ type: 'success', content: '✅ WebSocket connected' })
    } catch (error) {
      addMessage({ type: 'error', content: `❌ Connection failed: ${error.message}` })
    }
  }

  const subscribeToTopic = () => {
    if (!isConnected) {
      addMessage({ type: 'error', content: '❌ Not connected to WebSocket' })
      return
    }

    addMessage({ type: 'info', content: `📡 Subscribing to: ${testTopic}` })
    webSocketService.subscribe(testTopic, messageHandler)
    setIsSubscribed(true)
    addMessage({ type: 'success', content: '✅ Subscribed to topic' })
  }

  const unsubscribeFromTopic = () => {
    addMessage({ type: 'info', content: `🔌 Unsubscribing from: ${testTopic}` })
    webSocketService.unsubscribe(testTopic)
    setIsSubscribed(false)
    addMessage({ type: 'info', content: '✅ Unsubscribed from topic' })
  }

  const getBackendStats = async () => {
    try {
      addMessage({ type: 'info', content: '📊 Fetching backend WebSocket stats...' })
      const response = await fetch('http://localhost:8080/api/websocket/stats')

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const stats = await response.json()
      setBackendStats(stats)
      addMessage({
        type: 'success',
        content: `📊 Backend stats: ${stats.activeConnections} connections, ${stats.topicStats?.totalTopics || 0} topics`,
        data: stats
      })

      // Check if our topic is registered
      const ourTopicSubscribers = stats.topicStats?.topicSubscriptions?.[testTopic]
      if (ourTopicSubscribers) {
        addMessage({
          type: 'success',
          content: `🎯 Our topic has ${ourTopicSubscribers} subscribers in backend`
        })
      } else {
        addMessage({
          type: 'warning',
          content: '⚠️ Our topic not found in backend subscriptions'
        })
      }
    } catch (error) {
      addMessage({ type: 'error', content: `❌ Failed to get backend stats: ${error.message}` })
    }
  }

  const getDebugSubscriptions = async () => {
    try {
      addMessage({ type: 'info', content: '🔍 Fetching detailed subscription debug info...' })
      const response = await fetch('http://localhost:8080/api/websocket/debug-subscriptions')

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const debug = await response.json()
      addMessage({
        type: 'success',
        content: '🔍 Debug subscriptions fetched',
        data: debug
      })

      // Check if our topic is in the detailed subscriptions
      const ourTopicSessions = debug.topicSubscriptions?.[testTopic]
      if (ourTopicSessions && ourTopicSessions.length > 0) {
        addMessage({
          type: 'success',
          content: `🎯 Our topic has sessions: ${ourTopicSessions.join(', ')}`
        })
      } else {
        addMessage({
          type: 'error',
          content: '❌ Our topic has no sessions in backend tracking!'
        })
      }
    } catch (error) {
      addMessage({ type: 'error', content: `❌ Failed to get debug subscriptions: ${error.message}` })
    }
  }

  const testSlotLock = async () => {
    try {
      addMessage({ type: 'info', content: '🔒 Testing slot lock API...' })
      
      const lockRequest = {
        shopId: 'dfdfe915-da70-4ec1-a7bb-b52d0bb74788',
        serviceId: '79236043-1fdd-4c7e-b200-74a57730f344',
        employeeId: '0e41472f-3b93-4821-8026-ee4b11d85068',
        dateTime: '2025-06-11T10:00:00'
      }

      const response = await fetch('http://localhost:8080/api/appointments/lock-slot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(lockRequest)
      })

      if (!response.ok) {
        const errorData = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorData}`)
      }

      const result = await response.json()
      addMessage({ 
        type: 'success', 
        content: '✅ Slot lock API call successful',
        data: result
      })

      addMessage({ 
        type: 'info', 
        content: '⏳ Waiting for WebSocket broadcast... (should arrive within 1-2 seconds)'
      })

    } catch (error) {
      addMessage({ type: 'error', content: `❌ Slot lock failed: ${error.message}` })
    }
  }

  const sendTestMessage = async () => {
    try {
      addMessage({ type: 'info', content: '📤 Sending test broadcast...' })
      
      const response = await fetch('http://localhost:8080/api/test/websocket/broadcast', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ message: 'Test broadcast message' })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const result = await response.json()
      addMessage({ 
        type: 'success', 
        content: '✅ Test broadcast sent',
        data: result
      })
    } catch (error) {
      addMessage({ type: 'error', content: `❌ Test broadcast failed: ${error.message}` })
    }
  }

  const clearMessages = () => {
    setMessages([])
  }

  const getMessageColor = (type) => {
    switch (type) {
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'warning': return 'text-yellow-600'
      case 'message': return 'text-blue-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Minimal WebSocket Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Status */}
          <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded">
            <div>
              <strong>Connected:</strong> 
              <span className={isConnected ? 'text-green-600' : 'text-red-600'}>
                {isConnected ? ' ✅' : ' ❌'}
              </span>
            </div>
            <div>
              <strong>Subscribed:</strong> 
              <span className={isSubscribed ? 'text-green-600' : 'text-red-600'}>
                {isSubscribed ? ' ✅' : ' ❌'}
              </span>
            </div>
          </div>

          {/* Test Topic */}
          <div className="p-3 bg-blue-50 rounded">
            <strong>Test Topic:</strong>
            <div className="text-xs mt-1 font-mono break-all">{testTopic}</div>
          </div>

          {/* Controls */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Button onClick={connectWebSocket} disabled={isConnected} variant="outline">
              Connect
            </Button>
            <Button onClick={subscribeToTopic} disabled={!isConnected || isSubscribed} variant="outline">
              Subscribe
            </Button>
            <Button onClick={unsubscribeFromTopic} disabled={!isSubscribed} variant="outline">
              Unsubscribe
            </Button>
            <Button onClick={getBackendStats} variant="outline">
              Get Stats
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
            <Button onClick={getDebugSubscriptions} variant="outline">
              🔍 Debug Subs
            </Button>
            <Button onClick={testSlotLock} className="bg-orange-600 hover:bg-orange-700">
              🔒 Test Slot Lock
            </Button>
            <Button onClick={sendTestMessage} className="bg-purple-600 hover:bg-purple-700">
              📤 Test Broadcast
            </Button>
            <Button onClick={clearMessages} variant="outline">
              Clear Messages
            </Button>
          </div>


        </CardContent>
      </Card>

      {/* Messages */}
      <Card>
        <CardHeader>
          <CardTitle>Test Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-96 overflow-y-auto border rounded p-4 bg-gray-50">
            {messages.length === 0 ? (
              <p className="text-gray-500 italic">No messages yet...</p>
            ) : (
              messages.map((msg, index) => (
                <div key={index} className="mb-3 text-sm">
                  <span className="text-gray-400">[{msg.timestamp}]</span>
                  <span className={`ml-2 ${getMessageColor(msg.type)}`}>
                    {msg.content}
                  </span>
                  {msg.data && (
                    <pre className="ml-4 mt-1 text-xs text-gray-600 bg-white p-2 rounded overflow-x-auto">
                      {JSON.stringify(msg.data, null, 2)}
                    </pre>
                  )}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Test Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li><strong>Connect:</strong> Click "Connect" to establish WebSocket connection</li>
            <li><strong>Subscribe:</strong> Click "Subscribe" to subscribe to the test topic</li>
            <li><strong>Debug Subs:</strong> Click "🔍 Debug Subs" to see detailed subscription tracking</li>
            <li><strong>Get Stats:</strong> Click "Get Stats" to verify backend sees your subscription</li>
            <li><strong>Test Slot Lock:</strong> Click "Test Slot Lock" to trigger a real slot lock</li>
            <li><strong>Multi-Tab Test:</strong> Open another tab with this same test and repeat steps 1-3</li>
            <li><strong>Cross-Tab Test:</strong> Lock slot in one tab, should see message in other tab</li>
          </ol>
        </CardContent>
      </Card>
    </div>
  )
}

export default MinimalWebSocketTest
