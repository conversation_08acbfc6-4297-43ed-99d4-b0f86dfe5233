import React, { useState, useEffect, useRef } from 'react'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card'
import { Alert, AlertDescription } from '../ui/Alert'
import { Badge } from '../ui/Badge'
import webSocketService from '../../lib/websocket'
import useAuthStore from '../../store/authStore'

const WebSocketTest = () => {
  const { token, user } = useAuthStore()
  const [connectionStatus, setConnectionStatus] = useState('disconnected')
  const [messages, setMessages] = useState([])
  const [testMessage, setTestMessage] = useState('')
  const [broadcastMessage, setBroadcastMessage] = useState('')
  const [targetUserId, setTargetUserId] = useState('')
  const [userMessage, setUserMessage] = useState('')
  const [stats, setStats] = useState(null)
  const [useNativeSTOMP, setUseNativeSTOMP] = useState(false)
  const messagesEndRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const addMessage = (message) => {
    setMessages(prev => [...prev, {
      id: Date.now(),
      timestamp: new Date().toLocaleTimeString(),
      ...message
    }])
  }

  const connectWebSocket = async () => {
    if (!token) {
      addMessage({ type: 'error', content: 'No authentication token available' })
      return
    }

    try {
      setConnectionStatus('connecting')
      await webSocketService.connect(token, !useNativeSTOMP) // useSockJS = !useNativeSTOMP
      setConnectionStatus('connected')
      addMessage({ type: 'success', content: 'WebSocket connected successfully' })

      // Subscribe to test channels
      webSocketService.subscribe('test.pong', (data) => {
        addMessage({ type: 'pong', content: `Pong received: ${JSON.stringify(data)}` })
      })

      webSocketService.subscribe('test.echo', (data) => {
        addMessage({ type: 'echo', content: `Echo received: ${JSON.stringify(data)}` })
      })

      webSocketService.subscribe('test.broadcast', (data) => {
        addMessage({ type: 'broadcast', content: `Broadcast: ${JSON.stringify(data)}` })
      })

      webSocketService.subscribe('test.message', (data) => {
        addMessage({ type: 'message', content: `User message: ${JSON.stringify(data)}` })
      })

      webSocketService.subscribe('test.stats', (data) => {
        setStats(data)
        addMessage({ type: 'stats', content: `Stats received: ${data.activeConnections} active connections` })
      })

    } catch (error) {
      setConnectionStatus('error')
      addMessage({ type: 'error', content: `Connection failed: ${error.message}` })
    }
  }

  const disconnectWebSocket = () => {
    webSocketService.disconnect()
    setConnectionStatus('disconnected')
    addMessage({ type: 'info', content: 'WebSocket disconnected' })
  }

  const sendPing = () => {
    if (connectionStatus !== 'connected') {
      addMessage({ type: 'error', content: 'WebSocket not connected' })
      return
    }
    webSocketService.send({ type: 'test.ping' })
    addMessage({ type: 'sent', content: 'Ping sent' })
  }

  const sendEcho = () => {
    if (connectionStatus !== 'connected') {
      addMessage({ type: 'error', content: 'WebSocket not connected' })
      return
    }
    webSocketService.send({ type: 'test.echo', message: testMessage })
    addMessage({ type: 'sent', content: `Echo sent: ${testMessage}` })
    setTestMessage('')
  }

  const sendBroadcast = () => {
    if (connectionStatus !== 'connected') {
      addMessage({ type: 'error', content: 'WebSocket not connected' })
      return
    }
    webSocketService.send({ type: 'test.broadcast', message: broadcastMessage })
    addMessage({ type: 'sent', content: `Broadcast sent: ${broadcastMessage}` })
    setBroadcastMessage('')
  }

  const sendUserMessage = () => {
    if (connectionStatus !== 'connected') {
      addMessage({ type: 'error', content: 'WebSocket not connected' })
      return
    }
    webSocketService.send({
      type: 'test.user',
      targetUserId: targetUserId,
      message: userMessage
    })
    addMessage({ type: 'sent', content: `User message sent to ${targetUserId}: ${userMessage}` })
    setUserMessage('')
  }

  const requestStats = () => {
    if (connectionStatus !== 'connected') {
      addMessage({ type: 'error', content: 'WebSocket not connected' })
      return
    }
    webSocketService.send({ type: 'test.stats' })
    addMessage({ type: 'sent', content: 'Stats requested' })
  }

  const testAPICall = async () => {
    try {
      addMessage({ type: 'info', content: 'Testing API call...' })

      // Check if token exists
      const token = localStorage.getItem('token')
      if (!token) {
        addMessage({ type: 'error', content: 'No token found in localStorage' })
        return
      }

      addMessage({ type: 'info', content: `Token found: ${token.substring(0, 20)}...` })

      // Make a direct API call using fetch to test CORS
      const response = await fetch('http://localhost:8080/api/notifications/unread-count', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const data = await response.json()
        addMessage({ type: 'success', content: `API call successful: ${JSON.stringify(data)}` })
      } else {
        addMessage({ type: 'error', content: `API call failed: ${response.status} ${response.statusText}` })
      }
    } catch (error) {
      addMessage({ type: 'error', content: `API call error: ${error.message}` })
    }
  }

  const clearMessages = () => {
    setMessages([])
  }

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'bg-green-500'
      case 'connecting': return 'bg-yellow-500'
      case 'error': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getMessageTypeColor = (type) => {
    switch (type) {
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'pong': return 'text-blue-600'
      case 'echo': return 'text-purple-600'
      case 'broadcast': return 'text-orange-600'
      case 'message': return 'text-indigo-600'
      case 'stats': return 'text-teal-600'
      case 'sent': return 'text-gray-600'
      default: return 'text-gray-800'
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            WebSocket Test Console
            <Badge className={`${getStatusColor()} text-white`}>
              {connectionStatus}
            </Badge>
          </CardTitle>
          <CardDescription>
            Test WebSocket connectivity and STOMP messaging functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Connection Controls */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="useNativeSTOMP"
                checked={useNativeSTOMP}
                onChange={(e) => setUseNativeSTOMP(e.target.checked)}
                disabled={connectionStatus === 'connected'}
              />
              <label htmlFor="useNativeSTOMP" className="text-sm">
                Use Native STOMP (vs SockJS)
              </label>
            </div>
            <Button 
              onClick={connectWebSocket} 
              disabled={connectionStatus === 'connected' || connectionStatus === 'connecting'}
              variant="default"
            >
              Connect
            </Button>
            <Button 
              onClick={disconnectWebSocket} 
              disabled={connectionStatus !== 'connected'}
              variant="outline"
            >
              Disconnect
            </Button>
          </div>

          {/* Test Controls */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Button onClick={sendPing} disabled={connectionStatus !== 'connected'} className="w-full">
                Send Ping
              </Button>
              <div className="flex gap-2">
                <Input
                  placeholder="Echo message"
                  value={testMessage}
                  onChange={(e) => setTestMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendEcho()}
                />
                <Button onClick={sendEcho} disabled={connectionStatus !== 'connected'}>
                  Echo
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex gap-2">
                <Input
                  placeholder="Broadcast message"
                  value={broadcastMessage}
                  onChange={(e) => setBroadcastMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendBroadcast()}
                />
                <Button onClick={sendBroadcast} disabled={connectionStatus !== 'connected'}>
                  Broadcast
                </Button>
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder="Target User ID"
                  value={targetUserId}
                  onChange={(e) => setTargetUserId(e.target.value)}
                />
                <Input
                  placeholder="User message"
                  value={userMessage}
                  onChange={(e) => setUserMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendUserMessage()}
                />
                <Button onClick={sendUserMessage} disabled={connectionStatus !== 'connected'}>
                  Send
                </Button>
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={requestStats} disabled={connectionStatus !== 'connected'} variant="outline">
              Get Stats
            </Button>
            <Button onClick={testAPICall} variant="outline">
              Test API Call
            </Button>
            <Button onClick={clearMessages} variant="outline">
              Clear Messages
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Stats Display */}
      {stats && (
        <Card>
          <CardHeader>
            <CardTitle>Connection Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.activeConnections}</div>
                <div className="text-sm text-gray-600">Active</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{stats.totalConnections}</div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{stats.totalDisconnections}</div>
                <div className="text-sm text-gray-600">Disconnected</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.totalMessages}</div>
                <div className="text-sm text-gray-600">Messages</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Messages Log */}
      <Card>
        <CardHeader>
          <CardTitle>Messages Log</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-96 overflow-y-auto border rounded p-4 bg-gray-50">
            {messages.length === 0 ? (
              <div className="text-gray-500 text-center">No messages yet</div>
            ) : (
              messages.map((message) => (
                <div key={message.id} className="mb-2 text-sm">
                  <span className="text-gray-500">[{message.timestamp}]</span>
                  <span className={`ml-2 font-medium ${getMessageTypeColor(message.type)}`}>
                    {message.type.toUpperCase()}:
                  </span>
                  <span className="ml-2">{message.content}</span>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>
        </CardContent>
      </Card>

      {/* User Info */}
      {user && (
        <Alert>
          <AlertDescription>
            Connected as: {user.firstName} {user.lastName} (ID: {user.id})
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}

export default WebSocketTest
