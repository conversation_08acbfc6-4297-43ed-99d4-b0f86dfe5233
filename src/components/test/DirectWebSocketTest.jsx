import React, { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '../ui/Button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card'

const DirectWebSocketTest = () => {
  const [connectionStatus, setConnectionStatus] = useState('disconnected')
  const [messages, setMessages] = useState([])
  const [testResults, setTestResults] = useState([])
  const socketRef = useRef(null)

  const addMessage = (message) => {
    const timestamp = new Date().toLocaleTimeString()
    setMessages(prev => [...prev, { ...message, timestamp }])
  }

  const addTestResult = (test, status, details) => {
    const timestamp = new Date().toLocaleTimeString()
    setTestResults(prev => [...prev, { test, status, details, timestamp }])
  }

  const connectDirect = () => {
    try {
      addTestResult('Direct Connection', 'running', 'Connecting directly to WebSocket...')
      
      const wsUrl = 'ws://localhost:8080/ws'
      console.log('🔌 Connecting directly to:', wsUrl)
      
      const socket = new WebSocket(wsUrl)
      socketRef.current = socket

      socket.onopen = () => {
        console.log('🔌 Direct WebSocket connection opened')
        setConnectionStatus('connected')
        addTestResult('Direct Connection', 'success', 'Direct WebSocket connected')
      }

      socket.onmessage = (event) => {
        console.log('🔌 Direct WebSocket message received:', event.data)
        addMessage({
          type: 'raw_message',
          data: event.data
        })

        // Check for subscription confirmation
        if (event.data.includes('subscription_confirmed')) {
          console.log('🎯 DIRECT SUBSCRIPTION CONFIRMATION RECEIVED:', event.data)
          addTestResult('Confirmation Received', 'success', 'Subscription confirmation received!')
        }
      }

      socket.onerror = (error) => {
        console.error('🔌 Direct WebSocket error:', error)
        addTestResult('Direct Connection', 'error', 'WebSocket connection error')
      }

      socket.onclose = () => {
        console.log('🔌 Direct WebSocket connection closed')
        setConnectionStatus('disconnected')
        addTestResult('Direct Connection', 'info', 'WebSocket connection closed')
      }

    } catch (error) {
      addTestResult('Direct Connection', 'error', `Connection failed: ${error.message}`)
    }
  }

  const sendDirectSubscription = () => {
    if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      addTestResult('Direct Subscription', 'error', 'WebSocket not connected')
      return
    }

    try {
      addTestResult('Direct Subscription', 'running', 'Sending direct subscription request...')
      
      const subscriptionMessage = {
        type: 'subscribe',
        topic: 'slots.dfdfe915-da70-4ec1-a7bb-b52d0bb74788.79236043-1fdd-4c7e-b200-74a57730f344.0e41472f-3b93-4821-8026-ee4b11d85068.2025-06-11'
      }
      
      const messageStr = JSON.stringify(subscriptionMessage)
      console.log('📡 Sending direct subscription message:', messageStr)
      
      socketRef.current.send(messageStr)
      addTestResult('Direct Subscription', 'success', 'Direct subscription request sent')
      
      // Wait for confirmation
      setTimeout(() => {
        addTestResult('Confirmation Wait', 'info', 'Waiting for subscription confirmation...')
      }, 1000)
      
    } catch (error) {
      addTestResult('Direct Subscription', 'error', `Failed to send subscription: ${error.message}`)
    }
  }

  const sendTestMessage = () => {
    if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      addTestResult('Test Message', 'error', 'WebSocket not connected')
      return
    }

    try {
      addTestResult('Test Message', 'running', 'Sending test message...')
      
      const testMessage = 'Hello from direct WebSocket test'
      console.log('📡 Sending test message:', testMessage)
      
      socketRef.current.send(testMessage)
      addTestResult('Test Message', 'success', 'Test message sent')
      
    } catch (error) {
      addTestResult('Test Message', 'error', `Failed to send test message: ${error.message}`)
    }
  }

  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.close()
      socketRef.current = null
    }
  }

  const runFullTest = async () => {
    setMessages([])
    setTestResults([])
    
    addTestResult('Full Direct Test', 'running', 'Starting direct WebSocket test...')
    
    // Step 1: Connect
    connectDirect()
    
    // Wait for connection
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Step 2: Send subscription
    sendDirectSubscription()
    
    // Wait for confirmation
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // Step 3: Send test message
    sendTestMessage()
    
    addTestResult('Full Direct Test', 'success', 'Direct test completed')
  }

  const clearLogs = () => {
    setMessages([])
    setTestResults([])
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return 'text-green-600'
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'running': return 'text-blue-600'
      case 'info': return 'text-gray-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected': return '🟢'
      case 'success': return '✅'
      case 'error': return '❌'
      case 'running': return '⏳'
      case 'info': return 'ℹ️'
      default: return '⚪'
    }
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [])

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Direct WebSocket Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Status */}
            <div className="flex items-center space-x-2">
              <span>Connection:</span>
              <span className={getStatusColor(connectionStatus)}>
                {getStatusIcon(connectionStatus)} {connectionStatus}
              </span>
            </div>

            {/* Controls */}
            <div className="flex flex-wrap gap-2">
              <Button onClick={runFullTest} className="bg-blue-600 hover:bg-blue-700">
                Run Full Direct Test
              </Button>
              <Button onClick={connectDirect} variant="outline">
                Connect Direct
              </Button>
              <Button onClick={sendDirectSubscription} variant="outline">
                Send Subscription
              </Button>
              <Button onClick={sendTestMessage} variant="outline">
                Send Test Message
              </Button>
              <Button onClick={disconnect} variant="outline">
                Disconnect
              </Button>
              <Button onClick={clearLogs} variant="outline">
                Clear Logs
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Results and Messages */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Results */}
        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-start gap-3 p-2 bg-gray-50 rounded">
                    <span>{getStatusIcon(result.status)}</span>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{result.test}</span>
                        <span className={`text-sm ${getStatusColor(result.status)}`}>
                          {result.status.toUpperCase()}
                        </span>
                        <span className="text-xs text-gray-500">{result.timestamp}</span>
                      </div>
                      <p className="text-sm text-gray-600">{result.details}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Raw Messages */}
        {messages.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Raw WebSocket Messages</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {messages.map((message, index) => (
                  <div key={index} className="p-2 bg-green-50 rounded">
                    <div className="flex items-center gap-2 text-sm">
                      <span className="font-medium">{message.type}</span>
                      <span className="text-gray-500">{message.timestamp}</span>
                    </div>
                    <pre className="text-xs mt-1 overflow-x-auto whitespace-pre-wrap">
                      {typeof message.data === 'string' ? message.data : JSON.stringify(message.data, null, 2)}
                    </pre>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Direct Test Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-600">
            <p><strong>Purpose:</strong> Test WebSocket connection directly without our service layer</p>
            <p><strong>1. Run Full Direct Test:</strong> Connects, subscribes, and sends test message</p>
            <p><strong>2. Check Raw Messages:</strong> Shows exactly what the backend sends</p>
            <p><strong>3. Expected:</strong> Should receive subscription confirmation message</p>
            <p><strong>4. Compare:</strong> Compare with backend logs to see if messages match</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default DirectWebSocketTest
