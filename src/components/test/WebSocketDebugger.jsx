import React, { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON> } from '../ui/Button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card'
import webSocketService from '../../lib/websocket'
import { appointmentAPI } from '../../lib/api'

/**
 * Advanced WebSocket debugging component to trace message flow
 */
const WebSocketDebugger = () => {
  const [messages, setMessages] = useState([])
  const [isConnected, setIsConnected] = useState(false)
  const [subscriptions, setSubscriptions] = useState(new Set())
  const [isLocking, setIsLocking] = useState(false)
  const [lockedSlot, setLockedSlot] = useState(null)
  
  // Test data
  const testData = {
    shopId: 'dfdfe915-da70-4ec1-a7bb-b52d0bb74788',
    serviceId: '79236043-1fdd-4c7e-b200-74a57730f344',
    employeeId: '0e41472f-3b93-4821-8026-ee4b11d85068',
    date: '2025-06-11',
    dateTime: '2025-06-11T10:00:00'
  }

  const messageHandlerRef = useRef(null)

  const addMessage = (message) => {
    const timestamp = new Date().toLocaleTimeString()
    setMessages(prev => [...prev, { ...message, timestamp }])
  }

  // Universal message handler that logs everything
  const universalMessageHandler = (data) => {
    console.log('🔍 RAW WebSocket message received:', data)
    
    addMessage({
      type: 'raw',
      content: 'Raw WebSocket message received',
      data: data
    })

    // Try to parse and understand the message
    try {
      let parsedData = data
      if (typeof data === 'string') {
        try {
          parsedData = JSON.parse(data)
        } catch (e) {
          addMessage({
            type: 'info',
            content: `String message (not JSON): ${data}`
          })
          return
        }
      }

      // Check message type
      if (parsedData.type === 'subscription_confirmed') {
        addMessage({
          type: 'success',
          content: `✅ Subscription confirmed for topic: ${parsedData.topic}`
        })
      } else if (parsedData.type === 'unsubscription_confirmed') {
        addMessage({
          type: 'info',
          content: `🔌 Unsubscription confirmed for topic: ${parsedData.topic}`
        })
      } else if (parsedData.topic && parsedData.data) {
        // Topic-based message
        addMessage({
          type: 'slot-update',
          content: `📡 Topic message: ${parsedData.topic}`,
          data: parsedData
        })
        
        if (parsedData.data.type === 'SLOT_UPDATE') {
          addMessage({
            type: 'slot-update',
            content: `🎯 SLOT UPDATE: ${parsedData.data.action} - ${parsedData.data.dateTime}`,
            data: parsedData.data
          })
        }
      } else {
        addMessage({
          type: 'info',
          content: 'Unknown message format',
          data: parsedData
        })
      }
    } catch (error) {
      addMessage({
        type: 'error',
        content: `Failed to parse message: ${error.message}`,
        data: data
      })
    }
  }

  const connectWebSocket = async () => {
    try {
      addMessage({ type: 'info', content: 'Connecting to WebSocket...' })
      
      await webSocketService.connect('debug-token')
      setIsConnected(true)
      
      addMessage({ type: 'success', content: '✅ WebSocket connected successfully' })
      
      // Set up universal message handler
      messageHandlerRef.current = universalMessageHandler
      
    } catch (error) {
      addMessage({ type: 'error', content: `❌ Connection failed: ${error.message}` })
    }
  }

  const disconnectWebSocket = () => {
    webSocketService.disconnect()
    setIsConnected(false)
    setSubscriptions(new Set())
    addMessage({ type: 'info', content: '🔌 WebSocket disconnected' })
  }

  const subscribeToTestTopic = () => {
    const topic = `slots.${testData.shopId}.${testData.serviceId}.${testData.employeeId}.${testData.date}`
    
    addMessage({ type: 'info', content: `📡 Subscribing to topic: ${topic}` })
    
    webSocketService.subscribe(topic, universalMessageHandler)
    
    setSubscriptions(prev => new Set([...prev, topic]))
    
    addMessage({ type: 'success', content: `✅ Subscribed to: ${topic}` })
  }

  const unsubscribeFromTestTopic = () => {
    const topic = `slots.${testData.shopId}.${testData.serviceId}.${testData.employeeId}.${testData.date}`
    
    addMessage({ type: 'info', content: `🔌 Unsubscribing from topic: ${topic}` })
    
    webSocketService.unsubscribe(topic)
    
    setSubscriptions(prev => {
      const newSet = new Set(prev)
      newSet.delete(topic)
      return newSet
    })
    
    addMessage({ type: 'info', content: `✅ Unsubscribed from: ${topic}` })
  }

  const lockTestSlot = async () => {
    if (isLocking) return

    setIsLocking(true)
    addMessage({ type: 'info', content: '🔒 Attempting to lock test slot...' })

    try {
      const lockRequest = {
        shopId: testData.shopId,
        serviceId: testData.serviceId,
        employeeId: testData.employeeId,
        dateTime: testData.dateTime
      }

      addMessage({ type: 'info', content: `📤 Sending lock request: ${JSON.stringify(lockRequest)}` })

      const response = await appointmentAPI.lockSlot(lockRequest)

      addMessage({ type: 'info', content: `📥 Lock response received` })
      addMessage({ type: 'success', content: `✅ API Response:`, data: response })

      if (response.lockToken || response.data?.lockToken) {
        const lockToken = response.lockToken || response.data.lockToken
        setLockedSlot({ ...lockRequest, lockToken })
        addMessage({ type: 'success', content: `🔒 Slot locked with token: ${lockToken}` })

        // Wait a moment for WebSocket broadcast
        addMessage({ type: 'info', content: '⏳ Waiting for WebSocket broadcast...' })
        setTimeout(() => {
          addMessage({ type: 'info', content: '🎯 If you have another browser tab open, it should show this slot as locked now!' })
        }, 1000)
      } else {
        addMessage({ type: 'error', content: '❌ No lock token received' })
      }
    } catch (error) {
      addMessage({ type: 'error', content: `❌ Lock failed: ${error.message}` })
      console.error('Lock error:', error)
    } finally {
      setIsLocking(false)
    }
  }

  const unlockTestSlot = async () => {
    if (!lockedSlot || isLocking) return
    
    setIsLocking(true)
    addMessage({ type: 'info', content: '🔓 Attempting to unlock test slot...' })
    
    try {
      addMessage({ type: 'info', content: `📤 Sending unlock request: ${JSON.stringify(lockedSlot)}` })
      
      const response = await appointmentAPI.unlockSlot(lockedSlot)
      
      addMessage({ type: 'info', content: `📥 Unlock response received` })
      addMessage({ type: 'success', content: `✅ API Response:`, data: response })
      
      setLockedSlot(null)
      addMessage({ type: 'success', content: '🔓 Slot unlocked successfully' })
    } catch (error) {
      addMessage({ type: 'error', content: `❌ Unlock failed: ${error.message}` })
      console.error('Unlock error:', error)
    } finally {
      setIsLocking(false)
    }
  }

  const clearMessages = () => {
    setMessages([])
  }

  const getMessageColor = (type) => {
    switch (type) {
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'slot-update': return 'text-blue-600'
      case 'raw': return 'text-purple-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>WebSocket Message Flow Debugger</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Status */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded">
            <div>
              <strong>Connected:</strong> 
              <span className={isConnected ? 'text-green-600' : 'text-red-600'}>
                {isConnected ? ' ✅' : ' ❌'}
              </span>
            </div>
            <div>
              <strong>Subscriptions:</strong> 
              <span className="text-blue-600">{subscriptions.size}</span>
            </div>
            <div>
              <strong>Locked Slot:</strong> 
              <span className={lockedSlot ? 'text-orange-600' : 'text-gray-600'}>
                {lockedSlot ? ' 🔒' : ' 🔓'}
              </span>
            </div>
            <div>
              <strong>Messages:</strong> 
              <span className="text-purple-600">{messages.length}</span>
            </div>
          </div>

          {/* Test Data */}
          <div className="p-4 bg-blue-50 rounded">
            <h4 className="font-semibold mb-2">Test Data:</h4>
            <div className="text-sm space-y-1">
              <div><strong>Topic:</strong> <code>slots.{testData.shopId}.{testData.serviceId}.{testData.employeeId}.{testData.date}</code></div>
              <div><strong>DateTime:</strong> {testData.dateTime}</div>
            </div>
          </div>

          {/* Controls */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Button onClick={connectWebSocket} disabled={isConnected} variant="outline">
              Connect WS
            </Button>
            <Button onClick={disconnectWebSocket} disabled={!isConnected} variant="outline">
              Disconnect WS
            </Button>
            <Button onClick={subscribeToTestTopic} disabled={!isConnected} variant="outline">
              Subscribe
            </Button>
            <Button onClick={unsubscribeFromTestTopic} disabled={!isConnected} variant="outline">
              Unsubscribe
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            <Button 
              onClick={lockTestSlot} 
              disabled={!isConnected || isLocking || !!lockedSlot}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isLocking ? '⏳ Locking...' : '🔒 Lock Slot'}
            </Button>
            <Button 
              onClick={unlockTestSlot} 
              disabled={!isConnected || isLocking || !lockedSlot}
              className="bg-red-600 hover:bg-red-700"
            >
              {isLocking ? '⏳ Unlocking...' : '🔓 Unlock Slot'}
            </Button>
            <Button onClick={clearMessages} variant="outline">
              Clear Messages
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Messages Log */}
      <Card>
        <CardHeader>
          <CardTitle>Message Flow Log</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-96 overflow-y-auto border rounded p-4 bg-gray-50">
            {messages.length === 0 ? (
              <p className="text-gray-500 italic">No messages yet...</p>
            ) : (
              messages.map((msg, index) => (
                <div key={index} className="mb-3 text-sm">
                  <span className="text-gray-400">[{msg.timestamp}]</span>
                  <span className={`ml-2 ${getMessageColor(msg.type)}`}>
                    {msg.content}
                  </span>
                  {msg.data && (
                    <pre className="ml-4 mt-1 text-xs text-gray-600 bg-white p-2 rounded overflow-x-auto">
                      {JSON.stringify(msg.data, null, 2)}
                    </pre>
                  )}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default WebSocketDebugger
