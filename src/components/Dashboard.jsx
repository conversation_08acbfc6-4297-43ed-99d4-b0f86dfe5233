import React from 'react'
import { useNavigate } from 'react-router-dom'
import useAuthStore from '../store/authStore'
import { Button } from './ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/Card'

const Dashboard = () => {
  const { user, isOwner, isEmployee, isUser } = useAuthStore()
  const navigate = useNavigate()

  const getUserRoleDisplay = () => {
    if (isOwner()) return 'Business Owner'
    if (isEmployee()) return 'Employee'
    if (isUser()) return 'Customer'
    return 'User'
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            {/* Welcome Card */}
            <Card className="col-span-full">
              <CardHeader>
                <CardTitle>Welcome to BeautyHub</CardTitle>
                <CardDescription>
                  You are logged in as: <span className="font-medium">{getUserRoleDisplay()}</span>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p><strong>Name:</strong> {user?.firstName} {user?.lastName}</p>
                  <p><strong>Email:</strong> {user?.email}</p>
                  <p><strong>User ID:</strong> {user?.id}</p>
                  <p><strong>Roles:</strong> {user?.roles?.join(', ')}</p>
                </div>
              </CardContent>
            </Card>

            {/* Customer Features */}
            {isUser() && (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle>Find Shops</CardTitle>
                    <CardDescription>
                      Browse beauty shops in your area
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button
                      className="w-full"
                      onClick={() => navigate('/search')}
                    >
                      Browse Shops
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>My Appointments</CardTitle>
                    <CardDescription>
                      View and manage your bookings
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button
                      className="w-full"
                      onClick={() => navigate('/appointments')}
                    >
                      View Appointments
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Notifications</CardTitle>
                    <CardDescription>
                      Check your latest updates
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full">
                      View Notifications
                    </Button>
                  </CardContent>
                </Card>
              </>
            )}

            {/* Owner Features */}
            {isOwner() && (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle>My Shops</CardTitle>
                    <CardDescription>
                      Manage your beauty businesses
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full">
                      Manage Shops
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Appointments</CardTitle>
                    <CardDescription>
                      View all shop appointments
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full">
                      View Appointments
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Analytics</CardTitle>
                    <CardDescription>
                      Business performance insights
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full">
                      View Analytics
                    </Button>
                  </CardContent>
                </Card>
              </>
            )}

            {/* Employee Features */}
            {isEmployee() && (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle>My Schedule</CardTitle>
                    <CardDescription>
                      View your work schedule
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full">
                      View Schedule
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>My Appointments</CardTitle>
                    <CardDescription>
                      Manage your client appointments
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full">
                      View Appointments
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>My Services</CardTitle>
                    <CardDescription>
                      Manage your service offerings
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full">
                      Manage Services
                    </Button>
                  </CardContent>
                </Card>
              </>
            )}

            {/* Development Info Card */}
            <Card className="col-span-full bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="text-blue-900">Development Status</CardTitle>
                <CardDescription className="text-blue-700">
                  Backend and Frontend Foundation Complete
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>✅ Backend: Spring Boot 3+ with PostgreSQL, Redis, WebSockets</p>
                  <p>✅ Frontend: React with Tailwind CSS, React Query, Zustand</p>
                  <p>✅ Authentication: JWT with role-based access</p>
                  <p>✅ Database: Entity models and migrations ready</p>
                  <p>🔄 Next: Complete service layer and UI components</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}

export default Dashboard
