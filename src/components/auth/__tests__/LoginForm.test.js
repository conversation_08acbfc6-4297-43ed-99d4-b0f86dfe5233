import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import LoginForm from '../LoginForm';
import { authAPI } from '../../../lib/api';
import { useAuthStore } from '../../../store/authStore';

// Mock the API
jest.mock('../../../lib/api');
jest.mock('../../../store/authStore');

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('LoginForm Component', () => {
  const mockLogin = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    useAuthStore.mockReturnValue({
      login: mockLogin,
      isLoading: false,
      error: null,
    });
  });

  const renderLoginForm = () => {
    return render(
      <BrowserRouter>
        <LoginForm />
      </BrowserRouter>
    );
  };

  test('renders login form with all required fields', () => {
    renderLoginForm();
    
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    expect(screen.getByText(/don't have an account/i)).toBeInTheDocument();
  });

  test('displays validation errors for empty fields', async () => {
    renderLoginForm();
    
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    });
  });

  test('displays validation error for invalid email format', async () => {
    const user = userEvent.setup();
    renderLoginForm();
    
    const emailInput = screen.getByLabelText(/email/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailInput, 'invalid-email');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
    });
  });

  test('submits form with valid credentials', async () => {
    const user = userEvent.setup();
    const mockResponse = {
      data: {
        token: 'mock-jwt-token',
        user: {
          id: '123',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
        },
      },
    };

    authAPI.login.mockResolvedValue(mockResponse);
    mockLogin.mockResolvedValue();

    renderLoginForm();
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(authAPI.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
      expect(mockLogin).toHaveBeenCalledWith(mockResponse.data);
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
    });
  });

  test('displays error message on login failure', async () => {
    const user = userEvent.setup();
    const errorMessage = 'Invalid credentials';
    
    authAPI.login.mockRejectedValue({
      response: {
        data: {
          error: errorMessage,
        },
      },
    });

    renderLoginForm();
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'wrongpassword');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  test('shows loading state during form submission', async () => {
    const user = userEvent.setup();
    
    useAuthStore.mockReturnValue({
      login: mockLogin,
      isLoading: true,
      error: null,
    });

    renderLoginForm();
    
    const submitButton = screen.getByRole('button', { name: /signing in/i });
    expect(submitButton).toBeDisabled();
    expect(screen.getByText(/signing in/i)).toBeInTheDocument();
  });

  test('navigates to register page when clicking sign up link', async () => {
    const user = userEvent.setup();
    renderLoginForm();
    
    const signUpLink = screen.getByText(/sign up/i);
    await user.click(signUpLink);

    expect(mockNavigate).toHaveBeenCalledWith('/register');
  });

  test('handles network errors gracefully', async () => {
    const user = userEvent.setup();
    
    authAPI.login.mockRejectedValue(new Error('Network error'));

    renderLoginForm();
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
    });
  });

  test('clears error message when user starts typing', async () => {
    const user = userEvent.setup();
    
    useAuthStore.mockReturnValue({
      login: mockLogin,
      isLoading: false,
      error: 'Previous error message',
    });

    renderLoginForm();
    
    expect(screen.getByText('Previous error message')).toBeInTheDocument();
    
    const emailInput = screen.getByLabelText(/email/i);
    await user.type(emailInput, 'a');

    await waitFor(() => {
      expect(screen.queryByText('Previous error message')).not.toBeInTheDocument();
    });
  });

  test('prevents form submission when already loading', async () => {
    useAuthStore.mockReturnValue({
      login: mockLogin,
      isLoading: true,
      error: null,
    });

    renderLoginForm();
    
    const submitButton = screen.getByRole('button', { name: /signing in/i });
    fireEvent.click(submitButton);

    expect(authAPI.login).not.toHaveBeenCalled();
  });

  test('focuses on email input when component mounts', () => {
    renderLoginForm();
    
    const emailInput = screen.getByLabelText(/email/i);
    expect(emailInput).toHaveFocus();
  });

  test('allows password visibility toggle', async () => {
    const user = userEvent.setup();
    renderLoginForm();
    
    const passwordInput = screen.getByLabelText(/password/i);
    const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });

    expect(passwordInput).toHaveAttribute('type', 'password');
    
    await user.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'text');
    
    await user.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'password');
  });

  test('handles form submission with Enter key', async () => {
    const user = userEvent.setup();
    const mockResponse = {
      data: {
        token: 'mock-jwt-token',
        user: { id: '123', email: '<EMAIL>' },
      },
    };

    authAPI.login.mockResolvedValue(mockResponse);
    mockLogin.mockResolvedValue();

    renderLoginForm();
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.keyboard('{Enter}');

    await waitFor(() => {
      expect(authAPI.login).toHaveBeenCalled();
    });
  });
});
