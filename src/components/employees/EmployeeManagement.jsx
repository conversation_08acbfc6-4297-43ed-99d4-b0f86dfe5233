import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { employeeAPI } from '../../lib/api'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import Textarea from '../ui/Textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card'
import { Alert, AlertDescription } from '../ui/Alert'
import { Modal } from '../ui/Modal'
import { Plus, Edit, Trash2, Mail, Phone, Calendar, DollarSign, User } from 'lucide-react'

const EmployeeManagement = ({ shopId }) => {
  const queryClient = useQueryClient()
  const [showInviteModal, setShowInviteModal] = useState(false)
  const [editingEmployee, setEditingEmployee] = useState(null)
  const [inviteMode, setInviteMode] = useState('existing') // 'existing' or 'new'
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    password: '',
    confirmPassword: '',
    bio: '',
    specialties: '',
    yearsExperience: '',
    hourlyRate: '',
    commissionRate: ''
  })
  const [errors, setErrors] = useState({})

  // Fetch shop employees
  const { data: employees, isLoading, error } = useQuery({
    queryKey: ['shopEmployees', shopId],
    queryFn: () => employeeAPI.getShopEmployees(shopId),
    enabled: !!shopId
  })

  // Invite existing employee mutation
  const inviteEmployeeMutation = useMutation({
    mutationFn: ({ shopId, employeeData }) => employeeAPI.inviteEmployee(shopId, employeeData),
    onSuccess: () => {
      queryClient.invalidateQueries(['shopEmployees', shopId])
      setShowInviteModal(false)
      resetForm()
    },
    onError: (error) => {
      setErrors({ general: error.response?.data?.message || 'Failed to invite employee' })
    }
  })

  // Create new employee mutation
  const createEmployeeMutation = useMutation({
    mutationFn: ({ shopId, employeeData }) => employeeAPI.createEmployee(shopId, employeeData),
    onSuccess: () => {
      queryClient.invalidateQueries(['shopEmployees', shopId])
      setShowInviteModal(false)
      resetForm()
    },
    onError: (error) => {
      setErrors({ general: error.response?.data?.message || 'Failed to create employee' })
    }
  })

  // Update employee mutation
  const updateEmployeeMutation = useMutation({
    mutationFn: ({ employeeId, employeeData }) => employeeAPI.updateEmployee(employeeId, employeeData),
    onSuccess: () => {
      queryClient.invalidateQueries(['shopEmployees', shopId])
      setEditingEmployee(null)
      resetForm()
    },
    onError: (error) => {
      setErrors({ general: error.response?.data?.message || 'Failed to update employee' })
    }
  })

  // Deactivate employee mutation
  const deactivateEmployeeMutation = useMutation({
    mutationFn: employeeAPI.deactivateEmployee,
    onSuccess: () => {
      queryClient.invalidateQueries(['shopEmployees', shopId])
    },
    onError: (error) => {
      setErrors({ general: error.response?.data?.message || 'Failed to deactivate employee' })
    }
  })

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Valid email is required'
    }

    // Additional validation for new user creation
    if (inviteMode === 'new' && !editingEmployee) {
      if (!formData.firstName.trim()) newErrors.firstName = 'First name is required'
      if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required'
      if (!formData.password) newErrors.password = 'Password is required'
      if (formData.password && formData.password.length < 8) newErrors.password = 'Password must be at least 8 characters'
      if (formData.password && !/(?=.*[a-z])(?=.*[A-Z])/.test(formData.password)) {
        newErrors.password = 'Password must contain at least one uppercase and one lowercase letter'
      }
      if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = 'Passwords do not match'
    }

    if (formData.yearsExperience && parseInt(formData.yearsExperience) < 0) {
      newErrors.yearsExperience = 'Years of experience must be positive'
    }

    if (formData.hourlyRate && parseFloat(formData.hourlyRate) < 0) {
      newErrors.hourlyRate = 'Hourly rate must be positive'
    }

    if (formData.commissionRate && (parseFloat(formData.commissionRate) < 0 || parseFloat(formData.commissionRate) > 100)) {
      newErrors.commissionRate = 'Commission rate must be between 0 and 100'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    if (!validateForm()) return

    const employeeData = {
      ...formData,
      yearsExperience: formData.yearsExperience ? parseInt(formData.yearsExperience) : null,
      hourlyRate: formData.hourlyRate ? parseFloat(formData.hourlyRate) : null,
      commissionRate: formData.commissionRate ? parseFloat(formData.commissionRate) : null
    }

    if (editingEmployee) {
      updateEmployeeMutation.mutate({ employeeId: editingEmployee.id, employeeData })
    } else if (inviteMode === 'new') {
      createEmployeeMutation.mutate({ shopId, employeeData })
    } else {
      inviteEmployeeMutation.mutate({ shopId, employeeData })
    }
  }

  const handleEdit = (employee) => {
    setEditingEmployee(employee)
    setFormData({
      email: employee.email,
      bio: employee.bio || '',
      specialties: employee.specialties || '',
      yearsExperience: employee.yearsExperience?.toString() || '',
      hourlyRate: employee.hourlyRate?.toString() || '',
      commissionRate: employee.commissionRate?.toString() || ''
    })
    setShowInviteModal(true)
  }

  const handleDeactivate = (employeeId, employeeName) => {
    if (window.confirm(`Are you sure you want to deactivate ${employeeName}? They will no longer be able to take appointments.`)) {
      deactivateEmployeeMutation.mutate(employeeId)
    }
  }

  const resetForm = () => {
    setEditingEmployee(null)
    setInviteMode('existing')
    setFormData({
      email: '',
      firstName: '',
      lastName: '',
      password: '',
      confirmPassword: '',
      bio: '',
      specialties: '',
      yearsExperience: '',
      hourlyRate: '',
      commissionRate: ''
    })
    setErrors({})
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load employees. Please try again later.
        </AlertDescription>
      </Alert>
    )
  }

  const employeesList = employees?.data || []

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Employee Management</h2>
          <p className="text-gray-600">Invite and manage your shop employees</p>
        </div>
        <Button onClick={() => setShowInviteModal(true)} className="flex items-center">
          <Plus className="w-4 h-4 mr-2" />
          Invite Employee
        </Button>
      </div>

      {errors.general && (
        <Alert variant="destructive">
          <AlertDescription>{errors.general}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-4">
        {employeesList.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center">
              <p className="text-gray-500 mb-4">No employees yet.</p>
              <Button onClick={() => setShowInviteModal(true)}>Invite Your First Employee</Button>
            </CardContent>
          </Card>
        ) : (
          employeesList.map((employee) => (
            <Card key={employee.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg flex items-center">
                      <User className="w-5 h-5 mr-2" />
                      {employee.name}
                    </CardTitle>
                    <CardDescription className="flex items-center mt-1">
                      <Mail className="w-4 h-4 mr-1" />
                      {employee.email}
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => handleEdit(employee)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    {employee.active && (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => handleDeactivate(employee.id, employee.name)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {employee.bio && (
                    <p className="text-gray-700">{employee.bio}</p>
                  )}
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    {employee.specialties && (
                      <div>
                        <span className="font-medium text-gray-600">Specialties:</span>
                        <div className="text-gray-900">{employee.specialties}</div>
                      </div>
                    )}
                    
                    {employee.yearsExperience && (
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1 text-blue-600" />
                        <span>{employee.yearsExperience} years exp.</span>
                      </div>
                    )}
                    
                    {employee.hourlyRate && (
                      <div className="flex items-center">
                        <DollarSign className="w-4 h-4 mr-1 text-green-600" />
                        <span>${employee.hourlyRate}/hour</span>
                      </div>
                    )}
                    
                    {employee.commissionRate && (
                      <div>
                        <span className="text-gray-600">Commission: {employee.commissionRate}%</span>
                      </div>
                    )}
                  </div>

                  {employee.phone && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Phone className="w-4 h-4 mr-1" />
                      {employee.phone}
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-2 border-t">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      employee.active 
                        ? 'text-green-600 bg-green-100' 
                        : 'text-red-600 bg-red-100'
                    }`}>
                      {employee.active ? 'Active' : 'Inactive'}
                    </span>
                    
                    {employee.hireDate && (
                      <span className="text-xs text-gray-500">
                        Hired: {new Date(employee.hireDate).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Invite/Edit Employee Modal */}
      <Modal
        isOpen={showInviteModal}
        onClose={() => {
          setShowInviteModal(false)
          resetForm()
        }}
        title={editingEmployee ? 'Edit Employee' : 'Add Employee'}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Mode Selection - only show when not editing */}
          {!editingEmployee && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                How would you like to add this employee?
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="existing"
                    checked={inviteMode === 'existing'}
                    onChange={(e) => setInviteMode(e.target.value)}
                    className="mr-2"
                  />
                  Invite existing user
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="new"
                    checked={inviteMode === 'new'}
                    onChange={(e) => setInviteMode(e.target.value)}
                    className="mr-2"
                  />
                  Create new account
                </label>
              </div>
            </div>
          )}

          {/* New User Fields - only show when creating new */}
          {inviteMode === 'new' && !editingEmployee && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    First Name *
                  </label>
                  <Input
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    placeholder="John"
                    error={errors.firstName}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Last Name *
                  </label>
                  <Input
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    placeholder="Doe"
                    error={errors.lastName}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Password *
                  </label>
                  <Input
                    type="password"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    placeholder="••••••••"
                    error={errors.password}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Confirm Password *
                  </label>
                  <Input
                    type="password"
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                    placeholder="••••••••"
                    error={errors.confirmPassword}
                  />
                </div>
              </div>
            </>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email Address *
            </label>
            <Input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
              error={errors.email}
              disabled={editingEmployee} // Can't change email when editing
            />
            {!editingEmployee && inviteMode === 'existing' && (
              <p className="text-xs text-gray-500 mt-1">
                The user must already have an account with this email
              </p>
            )}
            {!editingEmployee && inviteMode === 'new' && (
              <p className="text-xs text-gray-500 mt-1">
                A new account will be created with this email
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Bio
            </label>
            <Textarea
              value={formData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              placeholder="Brief description about the employee..."
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Specialties
            </label>
            <Input
              value={formData.specialties}
              onChange={(e) => handleInputChange('specialties', e.target.value)}
              placeholder="e.g., Hair coloring, Manicures, Facials"
            />
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Years Experience
              </label>
              <Input
                type="number"
                min="0"
                value={formData.yearsExperience}
                onChange={(e) => handleInputChange('yearsExperience', e.target.value)}
                placeholder="0"
                error={errors.yearsExperience}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Hourly Rate ($)
              </label>
              <Input
                type="number"
                step="0.01"
                min="0"
                value={formData.hourlyRate}
                onChange={(e) => handleInputChange('hourlyRate', e.target.value)}
                placeholder="0.00"
                error={errors.hourlyRate}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Commission (%)
              </label>
              <Input
                type="number"
                step="0.01"
                min="0"
                max="100"
                value={formData.commissionRate}
                onChange={(e) => handleInputChange('commissionRate', e.target.value)}
                placeholder="0.00"
                error={errors.commissionRate}
              />
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowInviteModal(false)
                resetForm()
              }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={inviteEmployeeMutation.isLoading || updateEmployeeMutation.isLoading || createEmployeeMutation.isLoading}
            >
              {editingEmployee
                ? 'Update Employee'
                : inviteMode === 'new'
                  ? 'Create Employee'
                  : 'Send Invitation'
              }
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  )
}

export default EmployeeManagement
