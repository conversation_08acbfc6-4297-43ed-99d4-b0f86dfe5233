import React, { useEffect, useState } from 'react'
import useAuthStore from '../../store/authStore'

const SimpleAuthDebug = () => {
  const { user, token, isAuthenticated, _hasHydrated } = useAuthStore()
  const [logs, setLogs] = useState([])

  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `${timestamp}: ${message}`])
  }

  useEffect(() => {
    addLog(`Auth state: hydrated=${_hasHydrated}, authenticated=${isAuthenticated}, hasToken=${!!token}, hasUser=${!!user}`)
  }, [isAuthenticated, token, user, _hasHydrated])

  useEffect(() => {
    addLog('Component mounted')
    
    // Check localStorage immediately
    const authStorage = localStorage.getItem('auth-storage')
    addLog(`localStorage auth-storage: ${authStorage ? 'EXISTS' : 'MISSING'}`)
    
    if (authStorage) {
      try {
        const parsed = JSON.parse(authStorage)
        addLog(`Parsed auth data: hasUser=${!!parsed.state?.user}, hasToken=${!!parsed.state?.token}, isAuth=${parsed.state?.isAuthenticated}`)
        addLog(`Full parsed data: ${JSON.stringify(parsed, null, 2)}`)
      } catch (e) {
        addLog(`Failed to parse auth storage: ${e.message}`)
      }
    }
  }, [])

  const handleClearLogs = () => {
    setLogs([])
  }

  const handleCheckStorage = () => {
    const authStorage = localStorage.getItem('auth-storage')
    addLog(`=== STORAGE CHECK ===`)
    addLog(`Raw localStorage: ${authStorage}`)
    if (authStorage) {
      try {
        const parsed = JSON.parse(authStorage)
        addLog(`Parsed: ${JSON.stringify(parsed, null, 2)}`)
      } catch (e) {
        addLog(`Parse error: ${e.message}`)
      }
    }
    addLog(`===================`)
  }

  return (
    <div className="fixed top-4 left-4 w-96 bg-white border border-gray-300 rounded-lg shadow-lg p-4 z-50 max-h-96 overflow-y-auto">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-semibold text-sm">Auth Debug</h3>
        <div className="space-x-1">
          <button
            onClick={handleCheckStorage}
            className="text-xs bg-blue-200 px-2 py-1 rounded"
          >
            Check
          </button>
          <button
            onClick={handleClearLogs}
            className="text-xs bg-gray-200 px-2 py-1 rounded"
          >
            Clear
          </button>
        </div>
      </div>
      
      <div className="text-xs space-y-1">
        <div>Hydrated: {_hasHydrated ? '✅' : '❌'}</div>
        <div>Auth: {isAuthenticated ? '✅' : '❌'}</div>
        <div>Token: {token ? '✅' : '❌'}</div>
        <div>User: {user ? '✅' : '❌'}</div>
      </div>
      
      <div className="mt-2 border-t pt-2">
        <div className="text-xs font-medium mb-1">Logs:</div>
        <div className="text-xs space-y-1 max-h-32 overflow-y-auto">
          {logs.map((log, index) => (
            <div key={index} className="text-gray-600">{log}</div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default SimpleAuthDebug
