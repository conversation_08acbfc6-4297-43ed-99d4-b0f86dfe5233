import React, { useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON>he<PERSON>, ExternalLink } from 'lucide-react'
import { Button } from '../ui/Button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '../ui/DropdownMenu'
import useNotificationStore from '../../store/notificationStore'
import useAuthStore from '../../store/authStore'
import { formatDistanceToNow } from 'date-fns'

const NotificationDropdown = () => {
  const { isAuthenticated, token } = useAuthStore()
  const {
    notifications,
    unreadCount,
    isLoading,
    error,
    fetchUnreadNotifications,
    markAsRead,
    markAllAsRead,
    autoMarkAsSeen,
    clearError
  } = useNotificationStore()

  useEffect(() => {
    // Add a delay to ensure auth state is fully restored from localStorage
    const timer = setTimeout(() => {
      // Only fetch notifications if user is authenticated and has a valid token
      if (isAuthenticated && token && notifications.length === 0 && !isLoading) {
        // Double-check that token exists in localStorage
        const storedToken = localStorage.getItem('token')
        if (storedToken) {
          fetchUnreadNotifications().catch(error => {
            console.error('Failed to fetch notifications in dropdown:', error)
            // Don't show error to user if it's just a 403 - they might not be fully authenticated yet
            if (error.response?.status !== 403) {
              // Let the store handle the error
            }
          })
        }
      }
    }, 100) // Small delay to ensure auth state is restored

    return () => clearTimeout(timer)
  }, [isAuthenticated, token, notifications.length, isLoading, fetchUnreadNotifications])

  const handleNotificationClick = async (notification) => {
    if (!notification.seen) {
      try {
        await markAsRead(notification.id)
      } catch (error) {
        console.error('Failed to mark notification as read:', error)
      }
    }

    // Navigate to action URL if available
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl
    }
  }

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead()
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
    }
  }

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'APPOINTMENT_CONFIRMED':
        return '✅'
      case 'APPOINTMENT_CANCELLED':
        return '❌'
      case 'APPOINTMENT_REMINDER':
        return '⏰'
      case 'NEW_BOOKING':
        return '📅'
      case 'PAYMENT_RECEIVED':
        return '💰'
      case 'PAYMENT_FAILED':
        return '⚠️'
      default:
        return '🔔'
    }
  }

  const formatNotificationTime = (createdAt) => {
    try {
      const date = new Date(createdAt)
      return formatDistanceToNow(date, { addSuffix: true })
    } catch (error) {
      return 'Recently'
    }
  }

  const handleDropdownOpen = () => {
    // Auto-mark notifications as seen when dropdown opens
    if (unreadCount > 0) {
      autoMarkAsSeen()
    }
  }

  return (
    <DropdownMenu onOpenChange={(open) => open && handleDropdownOpen()}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative"
        >
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs text-white">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="w-80 max-h-96 overflow-y-auto"
        align="end"
      >
        <div className="p-3 border-b">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-sm">Notifications</h3>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleMarkAllAsRead}
                className="text-xs h-6 px-2"
              >
                <CheckCheck className="h-3 w-3 mr-1" />
                Mark all read
              </Button>
            )}
          </div>
        </div>

        <div className="max-h-80 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 text-center text-sm text-gray-500">
              Loading notifications...
            </div>
          ) : error ? (
            <div className="p-4 text-center">
              <p className="text-sm text-red-500 mb-2">{error}</p>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  clearError()
                  fetchUnreadNotifications()
                }}
              >
                Try again
              </Button>
            </div>
          ) : notifications.length === 0 ? (
            <div className="p-4 text-center text-sm text-gray-500">
              No recent notifications
            </div>
          ) : (
            <div className="divide-y">
              {notifications.slice(0, 10).map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 hover:bg-gray-50 cursor-pointer transition-colors ${
                    !notification.seen ? 'bg-blue-50 border-l-2 border-l-blue-500' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start space-x-3">
                    <div className="text-lg flex-shrink-0 mt-0.5">
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className={`text-sm font-medium ${
                            !notification.seen ? 'text-gray-900' : 'text-gray-700'
                          }`}>
                            {notification.title}
                          </p>
                          <p className={`text-xs mt-1 ${
                            !notification.seen ? 'text-gray-700' : 'text-gray-500'
                          }`}>
                            {notification.message}
                          </p>
                          <p className="text-xs text-gray-400 mt-1">
                            {formatNotificationTime(notification.createdAt)}
                          </p>
                        </div>
                        <div className="flex items-center space-x-1 ml-2">
                          {!notification.seen && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                          {notification.actionUrl && (
                            <ExternalLink className="h-3 w-3 text-gray-400" />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {notifications.length > 0 && (
          <div className="p-3 border-t">
            <Button
              variant="ghost"
              size="sm"
              className="w-full text-xs"
              onClick={() => {
                window.location.href = '/notifications'
              }}
            >
              View all notifications
            </Button>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default NotificationDropdown
