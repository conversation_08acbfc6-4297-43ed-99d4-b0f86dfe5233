import React, { useState, useEffect, useCallback } from 'react'
import { loadStripe } from '@stripe/stripe-js'
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js'
import { Button } from '../ui/Button'
import { Alert, AlertDescription } from '../ui/Alert'

// Initialize Stripe (replace with your publishable key)
const stripeKey = process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || 'pk_test_mock_key'
console.log('Initializing Stripe with key:', stripeKey.substring(0, 20) + '...')
const stripePromise = loadStripe(stripeKey)

const CheckoutForm = ({ paymentIntent, onSuccess, onError, compact = false, hideButton = false }) => {
  const stripe = useStripe()
  const elements = useElements()
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState(null)

  // Debug Stripe loading
  useEffect(() => {
    console.log('Stripe loading status:', { stripe: !!stripe, elements: !!elements })
    if (!stripe) {
      console.log('Stripe is still loading...')
    }
    if (!elements) {
      console.log('Elements is still loading...')
    }
  }, [stripe, elements])



  const handleSubmit = useCallback(async (event) => {
    event.preventDefault()

    if (!stripe || !elements) {
      return
    }

    setIsProcessing(true)
    setError(null)

    const cardElement = elements.getElement(CardElement)

    try {
      console.log('Starting payment confirmation...')
      console.log('Payment intent:', paymentIntent)
      console.log('Stripe instance:', stripe)
      console.log('Card element:', cardElement)

      if (!stripe || !cardElement) {
        throw new Error('Stripe has not loaded properly')
      }

      if (!paymentIntent?.clientSecret) {
        throw new Error('Payment intent client secret is missing')
      }

      // Confirm payment with Stripe
      const { error: stripeError, paymentIntent: confirmedPaymentIntent } = await stripe.confirmCardPayment(
        paymentIntent.clientSecret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              email: paymentIntent.customerEmail,
              name: paymentIntent.customerName,
            },
          },
        }
      )

      console.log('Stripe confirmation result:', { stripeError, confirmedPaymentIntent })

      if (stripeError) {
        console.error('Stripe error:', stripeError)
        setError(stripeError.message)
        onError?.(stripeError.message)
      } else if (confirmedPaymentIntent.status === 'succeeded') {
        console.log('Payment succeeded!')
        onSuccess?.(confirmedPaymentIntent.payment_method.id)
      } else {
        console.warn('Payment not succeeded, status:', confirmedPaymentIntent.status)
        setError(`Payment status: ${confirmedPaymentIntent.status}`)
        onError?.(`Payment status: ${confirmedPaymentIntent.status}`)
      }
    } catch (err) {
      console.error('Payment confirmation error:', err)
      const errorMessage = err.message || 'An unexpected error occurred.'
      setError(errorMessage)
      onError?.(errorMessage)
    } finally {
      setIsProcessing(false)
    }
  }, [stripe, elements, paymentIntent, onSuccess, onError])

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
        fontFamily: 'system-ui, -apple-system, sans-serif',
      },
      invalid: {
        color: '#9e2146',
      },
    },
    hidePostalCode: false,
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className={compact ? "space-y-4" : "p-4 border rounded-lg bg-gray-50"}>
        {!compact && (
          <div className="mb-4">
            <h3 className="font-medium text-gray-900">Payment Details</h3>
            <p className="text-sm text-gray-600">
              Amount: ${paymentIntent.amount?.toFixed(2)}
            </p>
            <p className="text-sm text-gray-600">
              {paymentIntent.description}
            </p>
          </div>
        )}

        <div className={compact ? "" : "mb-4"}>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Card Information
          </label>
          <div className="p-3 border rounded-md bg-white">
            <CardElement options={cardElementOptions} />
          </div>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!hideButton && (
          <Button
            type="submit"
            disabled={!stripe || isProcessing}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            {isProcessing ? 'Processing...' : `Pay $${paymentIntent.amount?.toFixed(2)}`}
          </Button>
        )}
      </div>

      {!compact && (
        <div className="text-xs text-gray-500 text-center">
          <p>Your payment information is secure and encrypted.</p>
          <p>Powered by Stripe</p>
        </div>
      )}
    </form>
  )
}

const StripeCheckoutForm = ({ paymentIntent, onSuccess, onError, compact = false, hideButton = false }) => {
  // Mock implementation for development
  const [isMockMode] = useState(!process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY)

  console.log('StripeCheckoutForm mode:', {
    isMockMode,
    hasStripeKey: !!process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY,
    stripeKey: process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY?.substring(0, 20) + '...'
  })



  if (isMockMode) {

    return (
      <div className="space-y-4">
        <div className={compact ? "space-y-3" : "p-4 border rounded-lg bg-blue-50"}>
          {!compact && (
            <>
              <h3 className="font-medium text-blue-900 mb-2">Mock Payment Form</h3>
              <p className="text-sm text-blue-700 mb-4">
                This is a mock payment form for development. In production, this will be replaced with actual Stripe integration.
              </p>
            </>
          )}

          <div className="space-y-3">
            {!compact && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Amount
                  </label>
                  <div className="p-2 bg-white border rounded">
                    ${paymentIntent.amount?.toFixed(2)}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <div className="p-2 bg-white border rounded text-sm">
                    {paymentIntent.description}
                  </div>
                </div>
              </>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {compact ? 'Card Number' : 'Mock Card Number'}
              </label>
              <div className="p-2 bg-white border rounded text-sm text-gray-500">
                4242 4242 4242 4242
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Expiry
                </label>
                <div className="p-2 bg-white border rounded text-sm text-gray-500">
                  12/25
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  CVC
                </label>
                <div className="p-2 bg-white border rounded text-sm text-gray-500">
                  123
                </div>
              </div>
            </div>

            {!hideButton && (
              <Button
                onClick={() => onSuccess?.('mock_payment_method_id')}
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                {compact ? `Pay $${paymentIntent.amount?.toFixed(2)}` : 'Complete Mock Payment'}
              </Button>
            )}
          </div>
        </div>

        {!compact && (
          <div className="text-xs text-gray-500 text-center">
            <p>This is a development environment with mock payments.</p>
            <p>No actual charges will be made.</p>
          </div>
        )}
      </div>
    )
  }

  return (
    <Elements stripe={stripePromise}>
      <CheckoutForm
        paymentIntent={paymentIntent}
        onSuccess={onSuccess}
        onError={onError}
        compact={compact}
        hideButton={hideButton}
      />
    </Elements>
  )
}

export default StripeCheckoutForm
