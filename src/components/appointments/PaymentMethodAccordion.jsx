import React, { useState, useEffect } from 'react'
import { CreditCard, Banknote, Check } from 'lucide-react'
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '../ui/Accordion'
import { Button } from '../ui/Button'
import StripeCheckoutForm from './StripeCheckoutForm'
import { cn } from '../../lib/utils'

const PaymentMethodAccordion = ({
  paymentIntent,
  onPaymentSuccess,
  onPaymentError,
  onCashPayment,
  selectedSlot,
  shop,
  isProcessing = false,
  onPaymentFailure // New callback for payment failures
}) => {
  // Determine default payment method based on shop capabilities
  const getDefaultMethod = () => {
    if (shop?.acceptsCardPayments && paymentIntent) {
      return 'card'
    }
    return 'cash'
  }

  const [accordionValue, setAccordionValue] = useState(getDefaultMethod())

  // Update accordion value when shop or payment intent changes
  useEffect(() => {
    const defaultMethod = getDefaultMethod()
    console.log('PaymentMethodAccordion - Setting default method:', defaultMethod)
    console.log('Shop accepts cards:', shop?.acceptsCardPayments)
    console.log('Has payment intent:', !!paymentIntent)
    setAccordionValue(defaultMethod)
  }, [shop?.acceptsCardPayments, paymentIntent])


  const handleCashPayment = () => {
    console.log('PaymentMethodAccordion - handleCashPayment called')
    onCashPayment?.()
  }

  const handleCardPayment = (paymentMethodId) => {
    onPaymentSuccess?.(paymentMethodId)
  }

  const handlePaymentError = (error) => {
    console.error('Payment error:', error)
    const errorMessage = typeof error === 'string' ? error :
                        error?.message ||
                        'Payment processing failed'
    onPaymentError?.(errorMessage)
  }

  const handleAccordionChange = (value) => {
    console.log('Accordion value changed to:', value)
    setAccordionValue(value || '')
  }

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          {shop?.acceptsCardPayments ? "Choose Payment Method" : "Payment Method"}
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          Total: <span className="font-medium">${selectedSlot?.price?.toFixed(2)}</span>
        </p>
      </div>

      <Accordion
        type="single"
        collapsible={false}
        value={accordionValue}
        onValueChange={handleAccordionChange}
        className="border rounded-lg overflow-hidden"
      >
        {/* Card Payment Option - Only show if shop accepts card payments AND payment intent is available */}
        {shop?.acceptsCardPayments && paymentIntent && (
          <AccordionItem value="card" className="border-b-0">
            <AccordionTrigger
              className={cn(
                "hover:bg-blue-50 transition-all duration-200",
                accordionValue === 'card' && "bg-blue-100 border-l-4 border-l-blue-600 shadow-sm"
              )}
            >
              <div className="flex items-center space-x-3">
                <div className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center",
                  accordionValue === 'card' ? "bg-blue-600" : "bg-blue-100"
                )}>
                  <CreditCard className={cn(
                    "w-5 h-5",
                    accordionValue === 'card' ? "text-white" : "text-blue-600"
                  )} />
                </div>
                <div className="text-left">
                  <div className="font-medium text-gray-900">Pay with Card</div>
                  <div className="text-sm text-gray-500">Secure payment via Stripe</div>
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="bg-gray-50 rounded-lg p-4 mt-2">
                <StripeCheckoutForm
                  paymentIntent={paymentIntent}
                  onSuccess={handleCardPayment}
                  onError={handlePaymentError}
                  compact={true}
                  hideButton={false}
                />
              </div>
            </AccordionContent>
          </AccordionItem>
        )}

        {/* Show card payment unavailable message if shop accepts cards but no payment intent */}
        {shop?.acceptsCardPayments && !paymentIntent && (
          <AccordionItem value="card-unavailable" className="border-b-0">
            <AccordionTrigger
              className="hover:bg-red-50 transition-colors cursor-not-allowed opacity-60"
              disabled
            >
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <CreditCard className="w-5 h-5 text-red-600" />
                </div>
                <div className="text-left">
                  <div className="font-medium text-gray-900">Pay with Card</div>
                  <div className="text-sm text-red-500">Temporarily unavailable</div>
                </div>
              </div>
            </AccordionTrigger>
          </AccordionItem>
        )}

        {/* Cash Payment Option - Always show */}
        <AccordionItem value="cash" className="border-b-0">
          <AccordionTrigger
            className={cn(
              "hover:bg-green-50 transition-all duration-200",
              accordionValue === 'cash' && "bg-green-100 border-l-4 border-l-green-600 shadow-sm"
            )}
          >
            <div className="flex items-center space-x-3">
              <div className={cn(
                "w-10 h-10 rounded-full flex items-center justify-center",
                accordionValue === 'cash' ? "bg-green-600" : "bg-green-100"
              )}>
                <Banknote className={cn(
                  "w-5 h-5",
                  accordionValue === 'cash' ? "text-white" : "text-green-600"
                )} />
              </div>
              <div className="text-left">
                <div className="font-medium text-gray-900">Pay at Shop</div>
                <div className="text-sm text-gray-500">
                  {!shop?.acceptsCardPayments
                    ? "Cash only"
                    : !paymentIntent
                      ? "Card payments unavailable"
                      : "Pay when you arrive"
                  }
                </div>
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <div className="bg-gray-50 rounded-lg p-4 mt-2">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Check className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-700">
                      You can pay with {
                        !shop?.acceptsCardPayments
                          ? "cash"
                          : !paymentIntent
                            ? "cash (card payments temporarily unavailable)"
                            : "cash or card"
                      } when you arrive at <strong>{shop?.name}</strong>
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Check className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-700">
                      Your appointment is reserved for 15 minutes after the scheduled time
                    </p>
                  </div>
                </div>

                <div className="mt-4">
                  <Button
                    onClick={handleCashPayment}
                    disabled={isProcessing}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    {isProcessing ? 'Processing...' : 'Complete Booking (Pay at Shop)'}
                  </Button>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <div className="text-xs text-gray-500 text-center mt-4">
        <p>🔒 Your booking is secure and your payment information is protected</p>
      </div>
    </div>
  )
}

export default PaymentMethodAccordion
