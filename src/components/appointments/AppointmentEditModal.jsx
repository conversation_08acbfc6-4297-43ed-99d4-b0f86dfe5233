import React, { useState, useEffect } from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { appointmentAPI } from '../../lib/api'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { Textarea } from '../ui/Textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card'
import { Alert, AlertDescription } from '../ui/Alert'
import { Modal } from '../ui/Modal'
import { Calendar, Clock, User, AlertTriangle } from 'lucide-react'

const AppointmentEditModal = ({ appointment, isOpen, onClose, onSuccess }) => {
  const queryClient = useQueryClient()
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedSlot, setSelectedSlot] = useState(null)
  const [notes, setNotes] = useState('')
  const [errors, setErrors] = useState({})
  const [isRescheduling, setIsRescheduling] = useState(false)

  // Initialize form data when appointment changes
  useEffect(() => {
    if (appointment) {
      const appointmentDate = new Date(appointment.appointmentDateTime)
      setSelectedDate(appointmentDate.toISOString().split('T')[0])
      setNotes(appointment.notes || '')
      setSelectedSlot(null)
      setIsRescheduling(false)
    }
  }, [appointment])

  const canModify = () => {
    if (!appointment) return false
    const appointmentTime = new Date(appointment.appointmentDateTime)
    const now = new Date()
    const hoursUntilAppointment = (appointmentTime - now) / (1000 * 60 * 60)
    return hoursUntilAppointment >= 1 &&
           ['CONFIRMED'].includes(appointment.status)
  }

  // Fetch available slots when date is selected (for both same date and different date)
  const { data: availableSlots, isLoading: slotsLoading, error: slotsError } = useQuery({
    queryKey: ['availableSlots', appointment?.shopId, appointment?.employeeId, appointment?.serviceId, selectedDate],
    queryFn: () => appointmentAPI.getAvailableSlots({
      shopId: appointment.shopId,
      employeeId: appointment.employeeId,
      serviceId: appointment.serviceId,
      date: selectedDate
    }),
    enabled: !!appointment && !!selectedDate && canModify()
  })

  // Update appointment mutation
  const updateAppointmentMutation = useMutation({
    mutationFn: ({ appointmentId, updateData }) => appointmentAPI.updateAppointment(appointmentId, updateData),
    onSuccess: () => {
      queryClient.invalidateQueries(['myAppointments'])
      onSuccess?.()
      onClose()
    },
    onError: (error) => {
      setErrors({ 
        general: error.response?.data?.message || 'Failed to update appointment' 
      })
    }
  })

  const handleSubmit = (e) => {
    e.preventDefault()
    setErrors({})

    if (!appointment) return

    // Validate that appointment can be modified (1 hour before)
    const appointmentTime = new Date(appointment.appointmentDateTime)
    const now = new Date()
    const hoursUntilAppointment = (appointmentTime - now) / (1000 * 60 * 60)

    if (hoursUntilAppointment < 1) {
      setErrors({
        general: 'Appointments can only be modified at least 1 hour before the scheduled time'
      })
      return
    }

    const updateData = {
      notes: notes.trim()
    }

    // If rescheduling, add new appointment time
    if (isRescheduling && selectedSlot) {
      updateData.appointmentDateTime = selectedSlot.dateTime
      // Note: For now, we're updating without slot locking
      // In a production system, you might want to implement slot locking for rescheduling
    }

    updateAppointmentMutation.mutate({
      appointmentId: appointment.id,
      updateData
    })
  }

  const handleDateChange = (e) => {
    const newDate = e.target.value
    setSelectedDate(newDate)
    setSelectedSlot(null)

    // Enable rescheduling if date is selected (even if same date, user might want different time)
    setIsRescheduling(!!newDate)
  }

  const handleSlotSelect = (slot) => {
    setSelectedSlot(slot)
    setErrors({})
  }

  const formatSlotTime = (slot) => {
    const time = new Date(slot.dateTime)
    return time.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    })
  }



  if (!appointment) return null

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Edit Appointment">
      <div className="space-y-6">
        {/* Appointment Info */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <User className="h-5 w-5 mr-2" />
              {appointment.serviceName}
            </CardTitle>
            <CardDescription>
              {appointment.shopName} • {appointment.employeeName}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center text-sm text-gray-600">
              <Calendar className="h-4 w-4 mr-2" />
              <span>Current: {new Date(appointment.appointmentDateTime).toLocaleString()}</span>
            </div>
          </CardContent>
        </Card>

        {!canModify() && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              This appointment cannot be modified. Appointments can only be edited at least 1 hour before the scheduled time.
            </AlertDescription>
          </Alert>
        )}

        {errors.general && (
          <Alert variant="destructive">
            <AlertDescription>{errors.general}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Date Selection for Rescheduling */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Reschedule Date (optional)
            </label>
            <Input
              type="date"
              value={selectedDate}
              onChange={handleDateChange}
              min={new Date().toISOString().split('T')[0]}
              disabled={!canModify()}
            />
            {isRescheduling && (
              <p className="text-sm text-blue-600 mt-1">
                Select a new date to reschedule your appointment
              </p>
            )}
          </div>

          {/* Time Slot Selection */}
          {isRescheduling && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Available Time Slots
              </label>
              {slotsError && (
                <Alert variant="destructive" className="mb-4">
                  <AlertDescription>
                    Failed to load available slots: {slotsError.response?.data?.message || slotsError.message}
                  </AlertDescription>
                </Alert>
              )}
              {slotsLoading ? (
                <div className="text-center py-4">Loading available slots...</div>
              ) : availableSlots?.length > 0 ? (
                <div className="grid grid-cols-3 gap-2 max-h-40 overflow-y-auto">
                  {availableSlots
                    .filter(slot => slot.available && !slot.locked)
                    .map((slot, index) => {
                      const isCurrentSlot = new Date(slot.dateTime).getTime() === new Date(appointment.appointmentDateTime).getTime()
                      return (
                        <Button
                          key={index}
                          type="button"
                          variant={selectedSlot?.dateTime === slot.dateTime ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleSlotSelect(slot)}
                          className={`text-xs ${isCurrentSlot ? 'border-blue-500 bg-blue-50' : ''}`}
                          title={isCurrentSlot ? 'Current appointment time' : ''}
                        >
                          <Clock className="h-3 w-3 mr-1" />
                          {formatSlotTime(slot)}
                          {isCurrentSlot && <span className="ml-1 text-blue-600">•</span>}
                        </Button>
                      )
                    })}
                </div>
              ) : (
                <Alert>
                  <AlertDescription>
                    No available slots for the selected date. Please choose a different date.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes / Comments
            </label>
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add any special requests or comments..."
              rows={3}
              maxLength={1000}
              disabled={!canModify()}
            />
            <p className="text-xs text-gray-500 mt-1">
              {notes.length}/1000 characters
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!canModify() || updateAppointmentMutation.isLoading || (isRescheduling && !selectedSlot)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {updateAppointmentMutation.isLoading ? 'Updating...' : 'Update Appointment'}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  )
}

export default AppointmentEditModal
