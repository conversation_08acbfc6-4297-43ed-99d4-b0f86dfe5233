import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { appointmentAPI } from '../../lib/api'
import useAuthStore from '../../store/authStore'
import { Button } from '../ui/Button'
import { Card, CardContent } from '../ui/Card'
import { Alert, AlertDescription } from '../ui/Alert'
import { Badge } from '../ui/Badge'
import { Modal } from '../ui/Modal'
import { Textarea } from '../ui/Textarea'
import AppointmentEditModal from './AppointmentEditModal'
import { Calendar, Clock, MapPin, User, Edit, Trash2, MessageSquare, AlertTriangle } from 'lucide-react'

const AppointmentManagement = () => {
  const { isAuthenticated } = useAuthStore()
  const queryClient = useQueryClient()
  const [selectedAppointment, setSelectedAppointment] = useState(null)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showCancelModal, setShowCancelModal] = useState(false)
  const [cancelReason, setCancelReason] = useState('')
  const [cancelError, setCancelError] = useState('')

  // Fetch user appointments
  const { data: appointments, isLoading, error } = useQuery({
    queryKey: ['myAppointments'],
    queryFn: () => appointmentAPI.getMyAppointments({ page: 0, size: 50 }),
    enabled: isAuthenticated
  })

  // Cancel appointment mutation
  const cancelAppointmentMutation = useMutation({
    mutationFn: ({ appointmentId, reason }) => appointmentAPI.cancelAppointment(appointmentId, reason),
    onSuccess: () => {
      queryClient.invalidateQueries(['myAppointments'])
      setShowCancelModal(false)
      setSelectedAppointment(null)
      setCancelReason('')
      setCancelError('')
    },
    onError: (error) => {
      setCancelError(error.response?.data?.message || 'Failed to cancel appointment')
    }
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'CONFIRMED':
        return 'bg-green-100 text-green-800'
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800'
      case 'NO_SHOW':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const canModifyAppointment = (appointment) => {
    const appointmentTime = new Date(appointment.appointmentDateTime)
    const now = new Date()
    const hoursUntilAppointment = (appointmentTime - now) / (1000 * 60 * 60)
    return hoursUntilAppointment >= 1 &&
           ['CONFIRMED'].includes(appointment.status)
  }

  const canCancelAppointment = (appointment) => {
    const appointmentTime = new Date(appointment.appointmentDateTime)
    const now = new Date()
    const hoursUntilAppointment = (appointmentTime - now) / (1000 * 60 * 60)
    return hoursUntilAppointment >= 1 &&
           ['CONFIRMED'].includes(appointment.status)
  }

  const handleEditClick = (appointment) => {
    setSelectedAppointment(appointment)
    setShowEditModal(true)
  }

  const handleCancelClick = (appointment) => {
    setSelectedAppointment(appointment)
    setShowCancelModal(true)
    setCancelError('')
  }

  const handleCancelSubmit = (e) => {
    e.preventDefault()
    if (!selectedAppointment) return

    if (!cancelReason.trim()) {
      setCancelError('Please provide a reason for cancellation')
      return
    }

    cancelAppointmentMutation.mutate({
      appointmentId: selectedAppointment.id,
      reason: cancelReason.trim()
    })
  }

  const formatDateTime = (dateTimeString) => {
    const date = new Date(dateTimeString)
    return {
      date: date.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      }),
      time: date.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      })
    }
  }

  if (!isAuthenticated) {
    return (
      <Alert>
        <AlertDescription>
          Please log in to view your appointments.
        </AlertDescription>
      </Alert>
    )
  }

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 pb-6 border-b border-gray-200">
            <div>
              <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-96 mt-2 animate-pulse"></div>
            </div>
            <div className="h-10 bg-gray-200 rounded w-48 animate-pulse"></div>
          </div>
          <div className="space-y-6">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="animate-pulse border-l-4 border-l-gray-200 border border-gray-200 rounded-lg shadow-sm">
                <CardContent className="p-8">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                    <div className="flex-1 space-y-5">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="h-6 bg-gray-200 rounded w-48"></div>
                          <div className="h-4 bg-gray-200 rounded w-64"></div>
                        </div>
                        <div className="h-6 bg-gray-200 rounded w-20"></div>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <div className="h-12 bg-gray-200 rounded"></div>
                        <div className="h-12 bg-gray-200 rounded"></div>
                      </div>
                      <div className="h-4 bg-gray-200 rounded w-32"></div>
                    </div>
                    <div className="flex flex-col space-y-3 lg:w-36 lg:ml-6">
                      <div className="h-8 bg-gray-200 rounded"></div>
                      <div className="h-8 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load appointments. Please try again later.
        </AlertDescription>
      </Alert>
    )
  }

  const appointmentList = appointments?.data?.content || []

  if (appointmentList.length === 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 pb-6 border-b border-gray-200">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">My Appointments</h1>
              <p className="text-gray-600 mt-1">Manage your upcoming and past appointments</p>
            </div>
            <Button
              onClick={() => window.location.href = '/search'}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Calendar className="h-4 w-4 mr-2" />
              Book New Appointment
            </Button>
          </div>

          {/* Empty State */}
          <Card className="border-2 border-dashed border-gray-200 rounded-lg">
            <CardContent className="p-16 text-center">
              <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-8">
                <Calendar className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">No appointments yet</h3>
              <p className="text-gray-500 mb-8 max-w-md mx-auto">
                You haven't booked any appointments yet. Start by browsing our amazing shops and services.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  onClick={() => window.location.href = '/search'}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Book Your First Appointment
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.location.href = '/'}
                >
                  Explore Shops
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 pb-6 border-b border-gray-200">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">My Appointments</h1>
            <p className="text-gray-600 mt-1">Manage your upcoming and past appointments</p>
          </div>
          <Button
            onClick={() => window.location.href = '/search'}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Calendar className="h-4 w-4 mr-2" />
            Book New Appointment
          </Button>
        </div>

        {/* Appointments Grid */}
        <div className="grid gap-6">
        {appointmentList.map((appointment) => {
          const { date, time } = formatDateTime(appointment.appointmentDateTime)
          const canModify = canModifyAppointment(appointment)
          const canCancel = canCancelAppointment(appointment)
          const isUpcoming = new Date(appointment.appointmentDateTime) > new Date()

          return (
            <Card key={appointment.id} className={`hover:shadow-lg transition-all duration-200 border-l-4 border border-gray-200 rounded-lg shadow-sm ${
              appointment.status === 'CONFIRMED' ? 'border-l-green-500' :
              appointment.status === 'PENDING' ? 'border-l-yellow-500' :
              appointment.status === 'CANCELLED' ? 'border-l-red-500' :
              appointment.status === 'COMPLETED' ? 'border-l-blue-500' :
              'border-l-gray-300'
            }`}>
              <CardContent className="p-8">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                  {/* Left Section - Appointment Details */}
                  <div className="flex-1 space-y-5">
                    {/* Service and Status */}
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 flex items-center">
                          <User className="h-5 w-5 mr-2 text-blue-600" />
                          {appointment.serviceName}
                        </h3>
                        <p className="text-gray-600 flex items-center mt-1">
                          <MapPin className="h-4 w-4 mr-1" />
                          {appointment.shopName} • {appointment.employeeName}
                        </p>
                      </div>
                      <Badge className={`${getStatusColor(appointment.status)} px-3 py-1 text-sm font-medium`}>
                        {appointment.status}
                      </Badge>
                    </div>

                    {/* Date and Time */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      <div className="flex items-center text-gray-700">
                        <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                        <div>
                          <p className="font-medium">{date}</p>
                          <p className="text-sm text-gray-500">
                            {isUpcoming ? 'Upcoming' : 'Past'}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center text-gray-700">
                        <Clock className="h-4 w-4 mr-2 text-blue-600" />
                        <div>
                          <p className="font-medium">{time}</p>
                          <p className="text-sm text-gray-500">
                            {appointment.serviceDurationMinutes} minutes
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Notes */}
                    {appointment.notes && (
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <div className="flex items-start text-gray-700">
                          <MessageSquare className="h-4 w-4 mr-2 mt-0.5 text-blue-600" />
                          <div>
                            <p className="text-sm font-medium">Notes:</p>
                            <p className="text-sm">{appointment.notes}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Payment Info */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Total: </span>
                        <span className="text-lg font-bold text-gray-900">${appointment.totalAmount}</span>
                        {appointment.paymentType && (
                          <span className="ml-2 text-gray-500">• {appointment.paymentType}</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Right Section - Actions */}
                  <div className="flex flex-col space-y-3 lg:w-36 lg:ml-6">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditClick(appointment)}
                      disabled={!canModify}
                      className="flex items-center justify-center hover:bg-blue-50 hover:border-blue-300"
                    >
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleCancelClick(appointment)}
                      disabled={!canCancel}
                      className="flex items-center justify-center text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-300"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Cancel
                    </Button>

                    {(!canModify || !canCancel) && (
                      <div className={`text-xs p-3 rounded border ${
                        appointment.status === 'CANCELLED'
                          ? 'text-red-600 bg-red-50 border-red-200'
                          : appointment.status === 'COMPLETED'
                          ? 'text-blue-600 bg-blue-50 border-blue-200'
                          : 'text-yellow-600 bg-yellow-50 border-yellow-200'
                      }`}>
                        <AlertTriangle className="h-3 w-3 inline mr-1" />
                        <span className="block">
                          {appointment.status === 'CANCELLED'
                            ? 'Appointment has been cancelled'
                            : appointment.status === 'COMPLETED'
                            ? 'Appointment has been completed'
                            : !canModify && !canCancel
                            ? 'Cannot modify (1h+ required)'
                            : !canModify
                            ? 'Cannot edit (1h+ required)'
                            : 'Cannot cancel (1h+ required)'
                          }
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
        </div>

        {/* Edit Modal */}
        <AppointmentEditModal
          appointment={selectedAppointment}
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false)
            setSelectedAppointment(null)
          }}
          onSuccess={() => {
            // Modal will close automatically on success
          }}
        />

        {/* Cancel Modal */}
        <Modal
          isOpen={showCancelModal}
          onClose={() => {
            setShowCancelModal(false)
            setSelectedAppointment(null)
            setCancelReason('')
            setCancelError('')
          }}
          title="Cancel Appointment"
        >
          <div className="space-y-4">
            {selectedAppointment && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium">{selectedAppointment.serviceName}</h4>
                <p className="text-sm text-gray-600">
                  {selectedAppointment.shopName} • {formatDateTime(selectedAppointment.appointmentDateTime).date} at {formatDateTime(selectedAppointment.appointmentDateTime).time}
                </p>
              </div>
            )}

            {cancelError && (
              <Alert variant="destructive">
                <AlertDescription>{cancelError}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleCancelSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reason for cancellation *
                </label>
                <Textarea
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                  placeholder="Please provide a reason for cancelling this appointment..."
                  rows={3}
                  required
                />
              </div>

              <div className="flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowCancelModal(false)
                    setSelectedAppointment(null)
                    setCancelReason('')
                    setCancelError('')
                  }}
                >
                  Keep Appointment
                </Button>
                <Button
                  type="submit"
                  variant="destructive"
                  disabled={cancelAppointmentMutation.isLoading || !cancelReason.trim()}
                >
                  {cancelAppointmentMutation.isLoading ? 'Cancelling...' : 'Cancel Appointment'}
                </Button>
              </div>
            </form>
          </div>
        </Modal>
      </div>
    </div>
  )
}

export default AppointmentManagement
