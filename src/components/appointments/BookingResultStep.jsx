import React, { useState, useEffect } from 'react'
import { Check, X, Calendar, Clock, User, MapPin, CreditCard, Banknote, Home, ArrowLeft } from 'lucide-react'
import { Button } from '../ui/Button'
import { Card, CardContent } from '../ui/Card'
import { useNavigate } from 'react-router-dom'

const BookingResultStep = ({
  isSuccess,
  appointment,
  error,
  onComplete,
  onRetry,
  shop,
  selectedSlot
}) => {
  const navigate = useNavigate()
  const [currentStep, setCurrentStep] = useState(0)
  const [showDetails, setShowDetails] = useState(false)

  const successSteps = [
    { icon: Check, text: 'Appointment Confirmed', delay: 500 },
    { icon: Calendar, text: 'Added to Calendar', delay: 1000 },
    { icon: User, text: 'Notification Sent', delay: 1500 },
  ]

  const errorSteps = [
    { icon: X, text: 'Booking Failed', delay: 500 },
  ]

  const steps = isSuccess ? successSteps : errorSteps

  useEffect(() => {
    // Animate through steps
    steps.forEach((step, index) => {
      setTimeout(() => {
        setCurrentStep(index + 1)
      }, step.delay)
    })

    // Show details after animations
    setTimeout(() => {
      setShowDetails(true)
    }, isSuccess ? 2000 : 1000)
  }, [isSuccess, onComplete])

  const formatDateTime = (dateTime) => {
    const date = new Date(dateTime)

    if (isNaN(date.getTime())) {
      return {
        date: 'Invalid Date',
        time: 'Invalid Time'
      }
    }

    return {
      date: date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }

  // Merge appointment data with selectedSlot to get complete details
  const appointmentDetails = appointment ? {
    ...selectedSlot,
    ...appointment
  } : selectedSlot

  // Debug payment type
  console.log('BookingResultStep - appointmentDetails.paymentType:', appointmentDetails?.paymentType)
  console.log('BookingResultStep - appointment object:', appointment)
  console.log('BookingResultStep - selectedSlot object:', selectedSlot)

  const dateTimeValue = appointmentDetails?.dateTime || appointmentDetails?.appointmentDateTime
  const dateTime = dateTimeValue ? formatDateTime(dateTimeValue) : null

  return (
    <div className="max-w-2xl mx-auto">
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          {/* Animated Header */}
          <div className={`relative p-8 text-center ${
            isSuccess 
              ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50' 
              : 'bg-gradient-to-br from-red-50 via-pink-50 to-rose-50'
          }`}>
            {/* Main Icon */}
            <div className="relative mb-6">
              <div className={`w-20 h-20 rounded-full flex items-center justify-center mx-auto ${
                isSuccess 
                  ? 'bg-green-500 animate-bounce' 
                  : 'bg-red-500 animate-pulse'
              }`}>
                {isSuccess ? (
                  <Check className="w-10 h-10 text-white" strokeWidth={3} />
                ) : (
                  <X className="w-10 h-10 text-white" strokeWidth={3} />
                )}
              </div>
              
              {/* Ripple effect */}
              {isSuccess && (
                <>
                  <div className="absolute inset-0 w-20 h-20 bg-green-400 rounded-full mx-auto animate-ping opacity-20"></div>
                  <div className="absolute inset-0 w-20 h-20 bg-green-300 rounded-full mx-auto animate-ping opacity-10" style={{ animationDelay: '0.5s' }}></div>
                </>
              )}
            </div>

            {/* Title */}
            <h2 className={`text-2xl font-bold mb-2 ${
              isSuccess ? 'text-green-800' : 'text-red-800'
            }`}>
              {isSuccess ? 'Booking Confirmed!' : 'Booking Failed'}
            </h2>

            <p className={`text-sm ${
              isSuccess ? 'text-green-600' : 'text-red-600'
            }`}>
              {isSuccess 
                ? 'Your appointment has been successfully booked'
                : 'There was an issue processing your booking'
              }
            </p>

            {/* Animated Steps */}
            <div className="mt-6 space-y-3">
              {steps.map((step, index) => {
                const Icon = step.icon
                const isActive = currentStep > index
                
                return (
                  <div
                    key={index}
                    className={`flex items-center justify-center space-x-3 transition-all duration-500 ${
                      isActive 
                        ? 'opacity-100 transform translate-y-0' 
                        : 'opacity-30 transform translate-y-4'
                    }`}
                  >
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center transition-all duration-300 ${
                      isActive 
                        ? (isSuccess ? 'bg-green-500 text-white scale-110' : 'bg-red-500 text-white scale-110')
                        : 'bg-gray-200 text-gray-400'
                    }`}>
                      <Icon className="w-3 h-3" />
                    </div>
                    <span className={`text-sm font-medium transition-colors duration-300 ${
                      isActive 
                        ? (isSuccess ? 'text-green-800' : 'text-red-800')
                        : 'text-gray-400'
                    }`}>
                      {step.text}
                    </span>
                    {isActive && (
                      <div className={`w-2 h-2 rounded-full animate-pulse ${
                        isSuccess ? 'bg-green-500' : 'bg-red-500'
                      }`}></div>
                    )}
                  </div>
                )
              })}
            </div>
          </div>

          {/* Details Section */}
          {showDetails && (
            <div className="p-6 bg-white animate-fade-in">
              {isSuccess && appointmentDetails && dateTime ? (
                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900 mb-4">Appointment Details</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3">
                      <Calendar className="w-5 h-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{dateTime.date}</p>
                        <p className="text-xs text-gray-500">Date</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <Clock className="w-5 h-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{dateTime.time}</p>
                        <p className="text-xs text-gray-500">Time</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <MapPin className="w-5 h-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{shop?.name}</p>
                        <p className="text-xs text-gray-500">Location</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      {appointmentDetails.paymentType === 'CARD' ? (
                        <CreditCard className="w-5 h-5 text-gray-400" />
                      ) : (
                        <Banknote className="w-5 h-5 text-gray-400" />
                      )}
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {appointmentDetails.paymentType === 'CARD' ? 'Paid Online' : 'Pay at Shop'}
                        </p>
                        <p className="text-xs text-gray-500">Payment</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      🎉 Your appointment is confirmed! Please arrive 5-10 minutes early.
                    </p>
                  </div>
                </div>
              ) : !isSuccess && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-red-800 mb-4">What went wrong?</h3>
                  <p className="text-sm text-gray-700">
                    {error || 'An unexpected error occurred while processing your booking.'}
                  </p>
                  
                  <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
                    <p className="text-sm text-yellow-800">
                      💡 Don't worry! You can try booking again or contact the shop directly.
                    </p>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-3 mt-6">
                {isSuccess ? (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => navigate('/')}
                      className="flex-1"
                    >
                      <Home className="w-4 h-4 mr-2" />
                      Go Home
                    </Button>
                    <Button
                      onClick={() => navigate(`/shop/${shop?.id}`)}
                      className="flex-1"
                    >
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Back to Shop
                    </Button>
                  </>
                ) : (
                  <>
                    <Button variant="outline" onClick={onRetry} className="flex-1">
                      Try Again
                    </Button>
                    <Button onClick={() => navigate(`/shop/${shop?.id}`)} className="flex-1">
                      Back to Shop
                    </Button>
                  </>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Custom CSS for animations */}
      <style>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fade-in {
          animation: fade-in 0.6s ease-out forwards;
        }
      `}</style>
    </div>
  )
}

export default BookingResultStep
