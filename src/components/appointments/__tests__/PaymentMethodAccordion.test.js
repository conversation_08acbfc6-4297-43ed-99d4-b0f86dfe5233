import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import PaymentMethodAccordion from '../PaymentMethodAccordion'

describe('PaymentMethodAccordion Component', () => {
  const mockProps = {
    selectedSlot: { price: 50.00 },
    onPaymentSuccess: jest.fn(),
    onPaymentError: jest.fn(),
    onCashPayment: jest.fn(),
    isProcessing: false
  }

  test('shows both payment options when shop accepts card payments', () => {
    const shop = { acceptsCardPayments: true, name: 'Test Shop' }
    
    render(
      <PaymentMethodAccordion 
        {...mockProps}
        shop={shop}
        paymentIntent={{ amount: 50, description: 'Test payment' }}
      />
    )
    
    expect(screen.getByText('Choose Payment Method')).toBeInTheDocument()
    expect(screen.getByText('Pay with Card')).toBeInTheDocument()
    expect(screen.getByText('Pay at Shop')).toBeInTheDocument()
  })

  test('shows only cash payment when shop does not accept card payments', () => {
    const shop = { acceptsCardPayments: false, name: 'Cash Only Shop' }
    
    render(
      <PaymentMethodAccordion 
        {...mockProps}
        shop={shop}
      />
    )
    
    expect(screen.getByText('Payment Method')).toBeInTheDocument()
    expect(screen.queryByText('Pay with Card')).not.toBeInTheDocument()
    expect(screen.getByText('Pay at Shop')).toBeInTheDocument()
    expect(screen.getByText('Cash only')).toBeInTheDocument()
  })

  test('calls onCashPayment when Complete Booking button is clicked', () => {
    const shop = { acceptsCardPayments: false, name: 'Cash Only Shop' }

    render(
      <PaymentMethodAccordion
        {...mockProps}
        shop={shop}
      />
    )

    // The cash accordion should be open by default for cash-only shops
    // Click the Complete Booking button
    const completeButton = screen.getByText('Complete Booking (Pay at Shop)')
    fireEvent.click(completeButton)

    expect(mockProps.onCashPayment).toHaveBeenCalled()
  })

  test('displays correct total amount', () => {
    const shop = { acceptsCardPayments: true, name: 'Test Shop' }
    
    render(
      <PaymentMethodAccordion 
        {...mockProps}
        shop={shop}
        paymentIntent={{ amount: 75.50, description: 'Test payment' }}
      />
    )
    
    expect(screen.getByText('$50.00')).toBeInTheDocument()
  })

  test('shows processing state when booking is in progress', () => {
    const shop = { acceptsCardPayments: false, name: 'Cash Only Shop' }

    render(
      <PaymentMethodAccordion
        {...mockProps}
        shop={shop}
        isProcessing={true}
      />
    )

    // The cash accordion should be open by default for cash-only shops
    expect(screen.getByText('Processing...')).toBeInTheDocument()
  })
})
