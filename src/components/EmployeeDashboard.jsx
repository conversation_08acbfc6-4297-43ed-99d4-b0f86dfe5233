import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { appointmentAPI, scheduleAPI, employeeAPI } from '../lib/api'
import useAuthStore from '../store/authStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/Card'
import { Alert, AlertDescription } from './ui/Alert'
import { Button } from './ui/Button'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from './ui/Tabs'
import ScheduleManagement from './schedule/ScheduleManagement'
import AppointmentList from './appointments/AppointmentList'
import { 
  Calendar, 
  Clock, 
  Users, 
  Building,
  MapPin,
  Phone,
  Mail
} from 'lucide-react'

const EmployeeDashboard = () => {
  const { user } = useAuthStore()
  const [selectedShop, setSelectedShop] = useState(null)

  // Fetch employee profile and shops
  const { data: employeeShops, isLoading: shopsLoading, error: shopsError } = useQuery({
    queryKey: ['myEmployeeShops'],
    queryFn: () => employeeAPI.getMyEmployeeShops(),
    enabled: !!user
  })

  // Fetch my schedule
  const { data: mySchedule, isLoading: scheduleLoading } = useQuery({
    queryKey: ['mySchedule'],
    queryFn: () => scheduleAPI.getMySchedule(),
    enabled: !!user
  })

  // Fetch my appointments
  const { data: myAppointments, isLoading: appointmentsLoading } = useQuery({
    queryKey: ['myEmployeeAppointments'],
    queryFn: () => appointmentAPI.getMyAppointments({ page: 0, size: 10 }),
    enabled: !!user
  })

  if (shopsLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-6">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </main>
      </div>
    )
  }

  if (shopsError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <Alert variant="destructive">
              <AlertDescription>
                Failed to load your employee information. Please try again later.
              </AlertDescription>
            </Alert>
          </div>
        </main>
      </div>
    )
  }

  const shopsList = employeeShops?.data || []

  if (shopsList.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <Card>
              <CardContent className="p-6 text-center">
                <Building className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Employee Profiles Found</h3>
                <p className="text-gray-500 mb-4">
                  You don't have any employee profiles yet. Contact a shop owner to get invited as an employee.
                </p>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    )
  }

  // Get current employee profile for selected shop or first shop
  const currentEmployeeProfile = selectedShop 
    ? shopsList.find(shop => shop.id === selectedShop.id)
    : shopsList[0]

  const scheduleData = mySchedule?.data?.find(schedule => 
    schedule.shopId === currentEmployeeProfile?.id
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          
          {/* Welcome Section */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome to your Employee Dashboard
            </h2>
            <p className="text-gray-600">
              Manage your schedule, view appointments, and track your work across different shops.
            </p>
          </div>

          {/* Shop Selection */}
          {shopsList.length > 1 && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Shop</h3>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {shopsList.map((shop) => (
                  <Card 
                    key={shop.id} 
                    className={`cursor-pointer transition-all ${
                      currentEmployeeProfile?.id === shop.id 
                        ? 'ring-2 ring-blue-500 bg-blue-50' 
                        : 'hover:shadow-md'
                    }`}
                    onClick={() => setSelectedShop(shop)}
                  >
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center">
                        <Building className="w-5 h-5 mr-2" />
                        {shop.name}
                      </CardTitle>
                      <CardDescription>{shop.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-1 text-sm text-gray-600">
                        {shop.address && (
                          <div className="flex items-center">
                            <MapPin className="w-4 h-4 mr-2" />
                            {shop.address}
                          </div>
                        )}
                        {shop.phone && (
                          <div className="flex items-center">
                            <Phone className="w-4 h-4 mr-2" />
                            {shop.phone}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Current Shop Info */}
          {currentEmployeeProfile && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building className="w-5 h-5 mr-2" />
                  Currently Managing: {currentEmployeeProfile.name}
                </CardTitle>
                <CardDescription>
                  Your employee profile and schedule for this shop
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Role:</span>
                    <div className="text-gray-900">Employee</div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Status:</span>
                    <div className="text-gray-900">
                      <span className="px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100">
                        Active
                      </span>
                    </div>
                  </div>
                  {currentEmployeeProfile.specialties && (
                    <div>
                      <span className="font-medium text-gray-700">Specialties:</span>
                      <div className="text-gray-900">{currentEmployeeProfile.specialties}</div>
                    </div>
                  )}
                  {currentEmployeeProfile.yearsExperience && (
                    <div>
                      <span className="font-medium text-gray-700">Experience:</span>
                      <div className="text-gray-900">{currentEmployeeProfile.yearsExperience} years</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Today's Appointments</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {appointmentsLoading ? 'Loading...' : (myAppointments?.data?.content?.filter(apt => 
                    new Date(apt.appointmentDateTime).toDateString() === new Date().toDateString()
                  ).length || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Scheduled for today
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Working Hours</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {scheduleLoading ? 'Loading...' : (
                    scheduleData ? 
                      `${Object.values(scheduleData.schedule).filter(slots => slots.length > 0).length} days` :
                      '0 days'
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  Per week
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Appointments</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {appointmentsLoading ? 'Loading...' : (myAppointments?.data?.totalElements || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  All time
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Tabs */}
          <Tabs defaultValue="schedule" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="schedule" className="flex items-center">
                <Clock className="w-4 h-4 mr-2" />
                My Schedule
              </TabsTrigger>
              <TabsTrigger value="appointments" className="flex items-center">
                <Calendar className="w-4 h-4 mr-2" />
                My Appointments
              </TabsTrigger>
              <TabsTrigger value="profile" className="flex items-center">
                <Users className="w-4 h-4 mr-2" />
                Profile
              </TabsTrigger>
            </TabsList>

            <TabsContent value="schedule">
              {currentEmployeeProfile && (
                <ScheduleManagement 
                  shopId={currentEmployeeProfile.id}
                  employeeId={currentEmployeeProfile.employeeId}
                  isOwnSchedule={true}
                />
              )}
            </TabsContent>

            <TabsContent value="appointments">
              <AppointmentList />
            </TabsContent>

            <TabsContent value="profile">
              <Card>
                <CardHeader>
                  <CardTitle>Employee Profile</CardTitle>
                  <CardDescription>
                    Your professional information and settings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Name:</span>
                        <div className="text-gray-900">{user?.firstName} {user?.lastName}</div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Email:</span>
                        <div className="text-gray-900">{user?.email}</div>
                      </div>
                      {user?.phone && (
                        <div>
                          <span className="font-medium text-gray-700">Phone:</span>
                          <div className="text-gray-900">{user.phone}</div>
                        </div>
                      )}
                      <div>
                        <span className="font-medium text-gray-700">Role:</span>
                        <div className="text-gray-900">{user?.role}</div>
                      </div>
                    </div>
                    
                    <div className="pt-4 border-t">
                      <Button variant="outline">Edit Profile</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}

export default EmployeeDashboard
