import React, { createContext, useContext, useState } from 'react'
import { ChevronDown } from 'lucide-react'
import { cn } from '../../lib/utils'

const AccordionContext = createContext()

const Accordion = ({
  type = "single",
  collapsible = false,
  defaultValue,
  value,
  onValueChange,
  children,
  className,
  ...props
}) => {
  // Use controlled value if provided, otherwise use internal state
  const [internalOpenItems, setInternalOpenItems] = useState(() => {
    const initialSet = new Set()
    if (defaultValue) {
      initialSet.add(defaultValue)
    }
    return initialSet
  })

  // Determine current open items based on controlled vs uncontrolled mode
  const openItems = value !== undefined ?
    new Set(value ? [value] : []) :
    internalOpenItems

  const toggleItem = (itemValue) => {
    if (type === "single") {
      // Single accordion - only one item can be open
      const newValue = openItems.has(itemValue) && collapsible ? null : itemValue

      if (value !== undefined) {
        // Controlled mode - call onValueChange
        onValueChange?.(newValue)
      } else {
        // Uncontrolled mode - update internal state
        setInternalOpenItems(prev => {
          const newSet = new Set()
          if (newValue) {
            newSet.add(newValue)
          }
          return newSet
        })
      }
    } else {
      // Multiple accordion - multiple items can be open
      const newSet = new Set(openItems)
      if (newSet.has(itemValue)) {
        if (collapsible) {
          newSet.delete(itemValue)
        }
      } else {
        newSet.add(itemValue)
      }

      if (value !== undefined) {
        // For multiple mode in controlled state, we'd need to handle array of values
        // This is a simplified implementation for single mode focus
        onValueChange?.(Array.from(newSet))
      } else {
        setInternalOpenItems(newSet)
      }
    }
  }

  return (
    <AccordionContext.Provider value={{ openItems, toggleItem, collapsible }}>
      <div className={cn('', className)} {...props}>
        {children}
      </div>
    </AccordionContext.Provider>
  )
}

const AccordionItem = ({ value, children, className, ...props }) => {
  return (
    <div className={cn('border-b', className)} {...props}>
      {React.Children.map(children, child =>
        React.cloneElement(child, { value })
      )}
    </div>
  )
}

const AccordionTrigger = ({ value, children, className, ...props }) => {
  const context = useContext(AccordionContext)
  if (!context) {
    throw new Error('AccordionTrigger must be used within Accordion')
  }

  const { openItems, toggleItem } = context
  const isOpen = openItems.has(value)

  return (
    <button
      type="button"
      className={cn(
        'flex w-full items-center justify-between py-4 px-4 text-left font-medium transition-all hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-200',
        className
      )}
      onClick={() => toggleItem(value)}
      {...props}
    >
      {children}
      <ChevronDown
        className={cn(
          'h-4 w-4 shrink-0 transition-transform duration-200',
          isOpen && 'rotate-180'
        )}
      />
    </button>
  )
}

const AccordionContent = ({ value, children, className, ...props }) => {
  const context = useContext(AccordionContext)
  if (!context) {
    throw new Error('AccordionContent must be used within Accordion')
  }

  const { openItems } = context
  const isOpen = openItems.has(value)

  if (!isOpen) {
    return null
  }

  return (
    <div
      className={cn(
        'overflow-hidden animate-in slide-in-from-top-2 duration-300',
        className
      )}
    >
      <div className="px-4 pb-4" {...props}>
        {children}
      </div>
    </div>
  )
}

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent }
