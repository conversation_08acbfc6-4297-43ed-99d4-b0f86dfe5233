import React, { useState } from 'react'
import { format } from 'date-fns'
import { Calendar as CalendarIcon } from 'lucide-react'
import { Button } from './Button'
import { Calendar } from './Calendar'
import { Popover, PopoverContent, PopoverTrigger } from './Popover'

const DatePicker = ({ 
  date, 
  onDateChange, 
  placeholder = "Pick a date",
  disabled = false,
  className = "",
  ...props 
}) => {
  const [open, setOpen] = useState(false)

  const handleDateSelect = (selectedDate) => {
    if (selectedDate) {
      onDateChange(selectedDate)
      setOpen(false)
    }
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={`w-full justify-start text-left font-normal ${
            !date && "text-muted-foreground"
          } ${className}`}
          disabled={disabled}
          {...props}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "PPP") : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleDateSelect}
          disabled={(date) => date < new Date().setHours(0, 0, 0, 0)}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  )
}

export { DatePicker }
