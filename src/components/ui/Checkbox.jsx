import React from 'react'
import { Check } from 'lucide-react'

const Checkbox = ({ checked, onChange, className = '', disabled = false, ...props }) => {
  return (
    <button
      type="button"
      role="checkbox"
      aria-checked={checked}
      disabled={disabled}
      onClick={() => onChange && onChange(!checked)}
      className={`
        inline-flex items-center justify-center w-4 h-4 border-2 rounded transition-colors
        ${checked 
          ? 'bg-gray-900 border-gray-900 text-white' 
          : 'bg-white border-gray-300 hover:border-gray-400'
        }
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${className}
      `}
      {...props}
    >
      {checked && <Check className="w-3 h-3" />}
    </button>
  )
}

export { Checkbox }
export default Checkbox
