import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '../Accordion'

describe('Accordion Component', () => {
  const TestAccordion = ({ defaultValue = null }) => (
    <Accordion type="single" defaultValue={defaultValue}>
      <AccordionItem value="item1">
        <AccordionTrigger>Item 1</AccordionTrigger>
        <AccordionContent>Content 1</AccordionContent>
      </AccordionItem>
      <AccordionItem value="item2">
        <AccordionTrigger>Item 2</AccordionTrigger>
        <AccordionContent>Content 2</AccordionContent>
      </AccordionItem>
    </Accordion>
  )

  test('renders accordion items', () => {
    render(<TestAccordion />)
    
    expect(screen.getByText('Item 1')).toBeInTheDocument()
    expect(screen.getByText('Item 2')).toBeInTheDocument()
  })

  test('opens item when clicked', () => {
    render(<TestAccordion />)
    
    const trigger1 = screen.getByText('Item 1')
    fireEvent.click(trigger1)
    
    expect(screen.getByText('Content 1')).toBeInTheDocument()
  })

  test('opens default item on mount', () => {
    render(<TestAccordion defaultValue="item1" />)
    
    expect(screen.getByText('Content 1')).toBeInTheDocument()
  })

  test('closes open item when another is clicked (single mode)', () => {
    render(<TestAccordion defaultValue="item1" />)
    
    // Item 1 should be open by default
    expect(screen.getByText('Content 1')).toBeInTheDocument()
    
    // Click item 2
    const trigger2 = screen.getByText('Item 2')
    fireEvent.click(trigger2)
    
    // Item 1 should be closed, item 2 should be open
    expect(screen.queryByText('Content 1')).not.toBeInTheDocument()
    expect(screen.getByText('Content 2')).toBeInTheDocument()
  })
})
