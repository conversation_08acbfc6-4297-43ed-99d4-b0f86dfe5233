import React, { useState, useCallback } from 'react'
import { useMutation } from '@tanstack/react-query'
import { CreditCard, Check, AlertCircle } from 'lucide-react'
import { subscriptionAPI } from '../../lib/api'
import { Button } from '../ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Input } from '../ui/Input'
import { Badge } from '../ui/Badge'

const StripeSubscriptionForm = React.forwardRef(({ businessName, initialData = {} }, ref) => {
  const [selectedPlan, setSelectedPlan] = useState(initialData?.selectedPlan || 'basic')
  const [paymentData, setPaymentData] = useState({
    cardNumber: initialData?.cardNumber || '',
    expiryDate: initialData?.expiryDate || '',
    cvv: initialData?.cvv || '',
    cardholderName: initialData?.cardholderName || '',
    billingAddress: {
      line1: initialData?.billingAddress?.line1 || '',
      city: initialData?.billingAddress?.city || '',
      state: initialData?.billingAddress?.state || '',
      postalCode: initialData?.billingAddress?.postalCode || '',
      country: 'US'
    }
  })
  const [errors, setErrors] = useState({})

  const plans = [
    {
      id: 'basic',
      name: 'Basic Plan',
      price: 29.99,
      interval: 'month',
      features: [
        'Unlimited appointment bookings',
        'Customer management',
        'Basic analytics',
        'Email notifications',
        'Mobile app access'
      ],
      recommended: true
    },
    {
      id: 'premium',
      name: 'Premium Plan',
      price: 49.99,
      interval: 'month',
      features: [
        'Everything in Basic',
        'Advanced analytics & reports',
        'Marketing tools',
        'Priority customer support',
        'Custom branding',
        'Multiple staff accounts'
      ],
      recommended: false
    }
  ]

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setPaymentData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }))
    } else {
      setPaymentData(prev => ({ ...prev, [field]: value }))
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }))
    }
  }

  const formatCardNumber = (value) => {
    // Remove all non-digits
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '')
    // Add spaces every 4 digits
    const matches = v.match(/\d{4,16}/g)
    const match = matches && matches[0] || ''
    const parts = []
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4))
    }
    if (parts.length) {
      return parts.join(' ')
    } else {
      return v
    }
  }

  const formatExpiryDate = (value) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '')
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4)
    }
    return v
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!paymentData.cardNumber.replace(/\s/g, '')) {
      newErrors.cardNumber = 'Card number is required'
    } else if (paymentData.cardNumber.replace(/\s/g, '').length < 16) {
      newErrors.cardNumber = 'Invalid card number'
    }
    
    if (!paymentData.expiryDate) {
      newErrors.expiryDate = 'Expiry date is required'
    } else if (!/^\d{2}\/\d{2}$/.test(paymentData.expiryDate)) {
      newErrors.expiryDate = 'Invalid expiry date format (MM/YY)'
    }
    
    if (!paymentData.cvv) {
      newErrors.cvv = 'CVV is required'
    } else if (paymentData.cvv.length < 3) {
      newErrors.cvv = 'Invalid CVV'
    }
    
    if (!paymentData.cardholderName.trim()) {
      newErrors.cardholderName = 'Cardholder name is required'
    }
    
    if (!paymentData.billingAddress.line1.trim()) {
      newErrors['billingAddress.line1'] = 'Billing address is required'
    }
    
    if (!paymentData.billingAddress.city.trim()) {
      newErrors['billingAddress.city'] = 'City is required'
    }
    
    if (!paymentData.billingAddress.state.trim()) {
      newErrors['billingAddress.state'] = 'State is required'
    }
    
    if (!paymentData.billingAddress.postalCode.trim()) {
      newErrors['billingAddress.postalCode'] = 'Postal code is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Expose functions to parent via ref
  React.useImperativeHandle(ref, () => ({
    getCurrentFormData: () => ({
      selectedPlan,
      ...paymentData
    }),
    validateForm,
    isValid: () => validateForm()
  }), [selectedPlan, paymentData])

  return (
    <div className="space-y-6">
      {/* Plan Selection */}
      {/* Demo Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <span className="text-white text-sm font-bold">!</span>
          </div>
          <div>
            <h4 className="font-semibold text-blue-900 mb-1">Demo Mode</h4>
            <p className="text-blue-800 text-sm">
              This is a <strong>fake payment form</strong> for demonstration purposes.
              No real charges will be made and no actual Stripe subscription will be created.
              You can use any fake card details to proceed.
            </p>
            <p className="text-blue-700 text-xs mt-2">
              💡 Try: Card number 4242 4242 4242 4242, any future expiry date, any CVV
            </p>
          </div>
        </div>
      </div>

      <div>
        <h4 className="font-semibold mb-4">Choose Your Plan</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {plans.map(plan => (
            <Card 
              key={plan.id} 
              className={`cursor-pointer transition-all ${
                selectedPlan === plan.id 
                  ? 'ring-2 ring-gray-900 border-gray-900' 
                  : 'hover:border-gray-300'
              }`}
              onClick={() => setSelectedPlan(plan.id)}
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{plan.name}</CardTitle>
                  {plan.recommended && (
                    <Badge className="bg-gray-900 text-white">Recommended</Badge>
                  )}
                </div>
                <div className="text-2xl font-bold">
                  ${plan.price}
                  <span className="text-sm font-normal text-gray-600">/{plan.interval}</span>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Payment Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="w-5 h-5 mr-2" />
            Payment Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          {errors.general && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
              <AlertCircle className="w-4 h-4 mr-2" />
              {errors.general}
            </div>
          )}
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Card Number</label>
              <Input
                value={paymentData.cardNumber}
                onChange={(e) => handleInputChange('cardNumber', formatCardNumber(e.target.value))}
                placeholder="1234 5678 9012 3456"
                maxLength={19}
                className={errors.cardNumber ? 'border-red-500' : ''}
              />
              {errors.cardNumber && <p className="text-red-500 text-sm mt-1">{errors.cardNumber}</p>}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Expiry Date</label>
                <Input
                  value={paymentData.expiryDate}
                  onChange={(e) => handleInputChange('expiryDate', formatExpiryDate(e.target.value))}
                  placeholder="MM/YY"
                  maxLength={5}
                  className={errors.expiryDate ? 'border-red-500' : ''}
                />
                {errors.expiryDate && <p className="text-red-500 text-sm mt-1">{errors.expiryDate}</p>}
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">CVV</label>
                <Input
                  value={paymentData.cvv}
                  onChange={(e) => handleInputChange('cvv', e.target.value.replace(/\D/g, '').substring(0, 4))}
                  placeholder="123"
                  maxLength={4}
                  className={errors.cvv ? 'border-red-500' : ''}
                />
                {errors.cvv && <p className="text-red-500 text-sm mt-1">{errors.cvv}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Cardholder Name</label>
              <Input
                value={paymentData.cardholderName}
                onChange={(e) => handleInputChange('cardholderName', e.target.value)}
                placeholder="John Doe"
                className={errors.cardholderName ? 'border-red-500' : ''}
              />
              {errors.cardholderName && <p className="text-red-500 text-sm mt-1">{errors.cardholderName}</p>}
            </div>

            <div>
              <h5 className="font-medium mb-3">Billing Address</h5>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Address</label>
                  <Input
                    value={paymentData.billingAddress.line1}
                    onChange={(e) => handleInputChange('billingAddress.line1', e.target.value)}
                    placeholder="123 Main Street"
                    className={errors['billingAddress.line1'] ? 'border-red-500' : ''}
                  />
                  {errors['billingAddress.line1'] && <p className="text-red-500 text-sm mt-1">{errors['billingAddress.line1']}</p>}
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">City</label>
                    <Input
                      value={paymentData.billingAddress.city}
                      onChange={(e) => handleInputChange('billingAddress.city', e.target.value)}
                      placeholder="New York"
                      className={errors['billingAddress.city'] ? 'border-red-500' : ''}
                    />
                    {errors['billingAddress.city'] && <p className="text-red-500 text-sm mt-1">{errors['billingAddress.city']}</p>}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">State</label>
                    <Input
                      value={paymentData.billingAddress.state}
                      onChange={(e) => handleInputChange('billingAddress.state', e.target.value)}
                      placeholder="NY"
                      className={errors['billingAddress.state'] ? 'border-red-500' : ''}
                    />
                    {errors['billingAddress.state'] && <p className="text-red-500 text-sm mt-1">{errors['billingAddress.state']}</p>}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Postal Code</label>
                  <Input
                    value={paymentData.billingAddress.postalCode}
                    onChange={(e) => handleInputChange('billingAddress.postalCode', e.target.value)}
                    placeholder="10001"
                    className={errors['billingAddress.postalCode'] ? 'border-red-500' : ''}
                  />
                  {errors['billingAddress.postalCode'] && <p className="text-red-500 text-sm mt-1">{errors['billingAddress.postalCode']}</p>}
                </div>
              </div>
            </div>

          </div>
        </CardContent>
      </Card>
    </div>
  )
})

export default StripeSubscriptionForm
