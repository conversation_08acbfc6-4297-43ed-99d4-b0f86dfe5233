import React from 'react'
import { X } from 'lucide-react'
import ShopCreationWizard from './ShopCreationWizard'

const ShopCreationModal = ({ isOpen, onClose, onSuccess }) => {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
          
          {/* Content */}
          <div className="p-6">
            <ShopCreationWizard 
              onClose={onClose}
              onSuccess={onSuccess}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default ShopCreationModal
