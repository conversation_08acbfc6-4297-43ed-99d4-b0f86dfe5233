import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Heart } from 'lucide-react'

const AnimatedSuccessScreen = ({ onComplete, businessName }) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [showConfetti, setShow<PERSON>onfetti] = useState(false)

  const steps = [
    { icon: Check, text: 'Account Created', delay: 500 },
    { icon: Star, text: 'Subscription Activated', delay: 1000 },
    { icon: Heart, text: 'Welcome to BeautyHub!', delay: 1500 },
  ]

  useEffect(() => {
    // Start confetti animation
    setShowConfetti(true)

    // Animate through steps with fixed delays
    const timer1 = setTimeout(() => setCurrentStep(1), 500)
    const timer2 = setTimeout(() => setCurrentStep(2), 1000)
    const timer3 = setTimeout(() => setCurrentStep(3), 1500)

    // Auto-complete after all animations
    const completeTimer = setTimeout(() => {
      console.log('AnimatedSuccessScreen: Animation complete, calling onComplete')
      onComplete()
    }, 3000) // Reduced from 4000 to 3000

    return () => {
      clearTimeout(timer1)
      clearTimeout(timer2)
      clearTimeout(timer3)
      clearTimeout(completeTimer)
    }
  }, [onComplete])

  // Confetti particles
  const confettiParticles = Array.from({ length: 50 }, (_, i) => (
    <div
      key={i}
      className={`absolute w-2 h-2 rounded-full animate-bounce ${
        i % 4 === 0 ? 'bg-pink-400' :
        i % 4 === 1 ? 'bg-purple-400' :
        i % 4 === 2 ? 'bg-blue-400' : 'bg-yellow-400'
      }`}
      style={{
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
        animationDelay: `${Math.random() * 2}s`,
        animationDuration: `${1 + Math.random() * 2}s`
      }}
    />
  ))

  return (
    <div className="relative min-h-[500px] flex items-center justify-center bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 rounded-lg overflow-hidden">
      {/* Confetti */}
      {showConfetti && (
        <div className="absolute inset-0 pointer-events-none">
          {confettiParticles}
        </div>
      )}

      {/* Floating sparkles */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 20 }, (_, i) => (
          <Sparkles
            key={i}
            className={`absolute text-yellow-400 animate-pulse ${
              i % 2 === 0 ? 'w-4 h-4' : 'w-6 h-6'
            }`}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      {/* Main content */}
      <div className="relative z-10 text-center space-y-8 px-8">
        {/* Main success icon */}
        <div className="relative">
          <div className="w-24 h-24 bg-green-500 rounded-full flex items-center justify-center mx-auto animate-bounce">
            <Check className="w-12 h-12 text-white" strokeWidth={3} />
          </div>
          
          {/* Ripple effect */}
          <div className="absolute inset-0 w-24 h-24 bg-green-400 rounded-full mx-auto animate-ping opacity-20"></div>
          <div className="absolute inset-0 w-24 h-24 bg-green-300 rounded-full mx-auto animate-ping opacity-10" style={{ animationDelay: '0.5s' }}></div>
        </div>

        {/* Title */}
        <div className="space-y-2">
          <h1 className="text-4xl font-bold text-gray-900 animate-fade-in">
            Congratulations! 🎉
          </h1>
          <p className="text-xl text-gray-600 animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {businessName ? `${businessName} is now live on BeautyHub!` : 'Your shop is now live on BeautyHub!'}
          </p>
        </div>

        {/* Progress steps */}
        <div className="space-y-4">
          {steps.map((step, index) => {
            const Icon = step.icon
            const isActive = currentStep > index
            
            return (
              <div
                key={index}
                className={`flex items-center justify-center space-x-3 transition-all duration-500 ${
                  isActive 
                    ? 'opacity-100 transform translate-y-0' 
                    : 'opacity-30 transform translate-y-4'
                }`}
              >
                <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 ${
                  isActive 
                    ? 'bg-green-500 text-white scale-110' 
                    : 'bg-gray-200 text-gray-400'
                }`}>
                  <Icon className="w-4 h-4" />
                </div>
                <span className={`text-lg font-medium transition-colors duration-300 ${
                  isActive ? 'text-gray-900' : 'text-gray-400'
                }`}>
                  {step.text}
                </span>
                {isActive && (
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                )}
              </div>
            )
          })}
        </div>

        {/* Fun message */}
        <div className="bg-white/80 backdrop-blur-sm rounded-lg p-6 border border-white/20 shadow-lg animate-fade-in" style={{ animationDelay: '2s' }}>
          <p className="text-gray-700 text-lg">
            🚀 You're all set to start accepting bookings and growing your business!
          </p>
          <p className="text-gray-500 text-sm mt-2">
            Redirecting to your dashboard in a moment...
          </p>
        </div>
      </div>

      {/* Custom CSS for animations */}
      <style>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fade-in {
          animation: fade-in 0.6s ease-out forwards;
          opacity: 0;
        }
      `}</style>
    </div>
  )
}

export default AnimatedSuccessScreen
