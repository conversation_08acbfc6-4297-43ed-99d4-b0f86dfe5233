import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { authAPI } from '../lib/api'

// Auth store for managing authentication state

const useAuthStore = create(
  persist(
    (set, get) => {
      // Wrap set to track all state changes
      const trackedSet = (newState) => {
        console.log('Auth store state change:', newState)
        if (typeof newState === 'function') {
          return set((state) => {
            const result = newState(state)
            console.log('Auth store state change (function):', result)
            return result
          })
        }
        return set(newState)
      }

      return {
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        _hasHydrated: false,

      login: (userData, token) => {
        console.log('=== LOGIN CALLED ===', { userData, token: !!token })
        trackedSet({
          user: userData,
          token,
          isAuthenticated: true,
        })
        // Remove manual localStorage operations - Zustand persist handles this
      },

      updateUser: (userData) => {
        console.log('=== UPDATE USER CALLED ===', userData)
        trackedSet(state => ({
          user: { ...state.user, ...userData }
        }))
      },

      logout: () => {
        console.log('=== LOGOUT CALLED ===')
        console.trace('Logout call stack')

        trackedSet({
          user: null,
          token: null,
          isAuthenticated: false,
        })
        // Remove manual localStorage operations - Zustand persist handles this

        // Reset notification store when logging out
        try {
          const { reset } = require('./notificationStore').default.getState()
          reset()
        } catch (error) {
          console.error('Error resetting notification store:', error)
        }
      },

      setLoading: (loading) => {
        trackedSet({ isLoading: loading })
      },

      // Initialize auth state from persisted store with validation
      initializeAuth: async () => {
        console.log('=== INITIALIZE AUTH CALLED ===')

        // Clean up any manual localStorage entries to prevent conflicts
        const manualToken = localStorage.getItem('token')
        const manualUser = localStorage.getItem('user')
        if (manualToken || manualUser) {
          console.log('Cleaning up manual localStorage entries...')
          localStorage.removeItem('token')
          localStorage.removeItem('user')
        }

        // Check what's in localStorage before we start
        const authStorage = localStorage.getItem('auth-storage')
        console.log('localStorage auth-storage at init start:', authStorage)

        const state = get()
        const { token, user, isAuthenticated } = state

        console.log('Initializing auth with state:', {
          hasToken: !!token,
          hasUser: !!user,
          isAuthenticated,
          userEmail: user?.email,
          fullState: state
        })

        if (token && user && isAuthenticated) {
          try {
            // First, do a client-side token expiry check
            const isTokenExpired = !get().isTokenValid()

            if (isTokenExpired) {
              console.log('Token is expired, attempting refresh...')
              // Token is expired, try to refresh
              const refreshed = await get().refreshToken()
              if (!refreshed) {
                console.log('Token refresh failed during initialization - keeping user logged in for now')
                // Don't logout immediately on initialization failure
                // Let the API interceptor handle it when actual API calls are made
                return
              }
            } else {
              // Token appears valid client-side, now test backend validation
              console.log('Token appears valid client-side, testing backend validation...')

              try {
                const isValid = await get().validateToken()
                if (isValid) {
                  console.log('Backend validation successful during initialization')
                } else {
                  console.log('Backend validation failed during initialization, but keeping user logged in')
                }
              } catch (error) {
                console.log('Backend validation error during initialization:', error.message)
                // Don't logout on initialization validation failure
                // The API interceptor will handle token refresh when needed
              }
            }

            console.log('Auth initialization completed successfully')
          } catch (error) {
            console.error('Auth initialization failed:', error)
            // Don't logout during initialization unless it's a clear auth failure
            // Let the API interceptor handle token refresh when actual API calls are made
            console.log('Keeping user logged in despite initialization error')
          }
        } else {
          console.log('No valid auth state found, user remains logged out')
        }
      },

      // Validate current token
      validateToken: async () => {
        const { token } = get()
        if (!token) {
          console.log('No token to validate')
          return false
        }

        console.log('Validating token with server...')

        // First check client-side validity
        const clientSideValid = get().isTokenValid()
        console.log('Client-side token validation:', clientSideValid)

        if (!clientSideValid) {
          console.log('Token is expired client-side, skipping server validation')
          return false
        }

        try {
          const response = await authAPI.validateToken()
          console.log('Server validation response:', response.data)

          if (response.data.valid) {
            // Update user data if validation returns updated user info
            if (response.data.user) {
              trackedSet((state) => ({
                user: response.data.user
              }))
            }
            console.log('Token validation successful')
            return true
          }
          console.log('Server says token is invalid')
          return false
        } catch (error) {
          console.error('Token validation failed:', error)
          console.log('Validation error details:', {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            message: error.message
          })

          // If it's a network error, fall back to client-side validation
          if (error.code === 'NETWORK_ERROR' || !error.response) {
            console.log('Network error during validation, falling back to client-side check')
            return get().isTokenValid()
          }

          return false
        }
      },

      // Refresh token
      refreshToken: async () => {
        const { token: currentToken, user: currentUser } = get()
        if (!currentToken) {
          console.log('No token to refresh')
          return false
        }

        console.log('Starting token refresh...')
        console.log('Current user before refresh:', currentUser)

        try {
          const response = await authAPI.refreshToken()
          const { token, id, email, firstName, lastName, avatar, roles } = response.data

          console.log('Token refresh response received:', { id, email, firstName, lastName, avatar, roles })

          // Update store - Zustand persist will handle localStorage
          trackedSet({
            user: { id, email, firstName, lastName, avatar, roles },
            token,
            isAuthenticated: true,
          })

          console.log('Token refreshed successfully, new user data:', { id, email, firstName, lastName, avatar, roles })
          return true
        } catch (error) {
          console.error('Token refresh failed:', error)
          console.log('Error details:', {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            message: error.message
          })

          // Only logout for authentication errors, not network errors
          if (error.response?.status === 401 || error.response?.status === 403) {
            console.log('Authentication failed, logging out')
            get().logout()
          } else if (error.code === 'NETWORK_ERROR' || !error.response) {
            console.log('Network error during token refresh, keeping user logged in')
            // Don't logout on network errors - user might be offline temporarily
          } else {
            console.log('Unknown error during token refresh, keeping user logged in')
          }

          return false
        }
      },

      // Check if token is expired (client-side check)
      isTokenValid: () => {
        const { token } = get()
        if (!token) {
          console.log('No token to validate')
          return false
        }

        try {
          // Decode JWT payload (without verification - just for expiry check)
          const payload = JSON.parse(atob(token.split('.')[1]))
          const currentTime = Date.now() / 1000
          const timeUntilExpiry = payload.exp - currentTime

          console.log('Token validation details:', {
            issued: new Date(payload.iat * 1000).toLocaleString(),
            expires: new Date(payload.exp * 1000).toLocaleString(),
            currentTime: new Date(currentTime * 1000).toLocaleString(),
            timeUntilExpiry: `${Math.round(timeUntilExpiry / 60)} minutes`,
            isValid: payload.exp > currentTime
          })

          return payload.exp > currentTime
        } catch (error) {
          console.error('Error checking token validity:', error)
          return false
        }
      },

      // Check if user has specific role
      hasRole: (role) => {
        const { user } = get()
        return user?.roles?.includes(`ROLE_${role}`) || false
      },

      // Check if user is owner
      isOwner: () => get().hasRole('OWNER'),

      // Check if user is employee
      isEmployee: () => get().hasRole('EMPLOYEE'),

      // Check if user is regular user
      isUser: () => get().hasRole('USER'),

      // Update user data (for refreshing user info without full re-auth)
      setUser: (userData) => {
        console.log('=== SET USER CALLED ===', userData)
        trackedSet({ user: userData })
      },

      // Mark store as hydrated
      markAsHydrated: () => {
        console.log('=== MARK AS HYDRATED CALLED ===')
        trackedSet({ _hasHydrated: true })
      },

      }
    },
    {
      name: 'auth-storage',
      version: 1, // Add version to force refresh if needed
      partialize: (state) => {
        console.log('Partializing state for storage:', {
          user: !!state.user,
          token: !!state.token,
          isAuthenticated: state.isAuthenticated
        })
        const result = {
          user: state.user,
          token: state.token,
          isAuthenticated: state.isAuthenticated,
        }
        console.log('Partialize result:', result)
        return result
      },
      merge: (persistedState, currentState) => {
        console.log('Merging persisted state:', persistedState)
        console.log('With current state:', currentState)
        const merged = {
          ...currentState,
          ...persistedState,
        }
        console.log('Merged result:', merged)
        return merged
      },
      onRehydrateStorage: () => {
        console.log('Setting up rehydration callback...')
        return (state, error) => {
          console.log('Rehydration callback called with:', { state: !!state, error: !!error })

          if (error) {
            console.error('Auth store rehydration failed:', error)
            return
          }

          console.log('Auth store rehydrated successfully:', {
            hasToken: !!state?.token,
            hasUser: !!state?.user,
            isAuthenticated: state?.isAuthenticated,
            userEmail: state?.user?.email,
            fullState: state
          })
        }
      },
    }
  )
)

// Mark as hydrated after rehydration is complete
// Use a longer delay to ensure Zustand persist has finished
setTimeout(() => {
  console.log('Marking store as hydrated...')
  const currentState = useAuthStore.getState()
  console.log('Current state before marking hydrated:', {
    hasToken: !!currentState.token,
    hasUser: !!currentState.user,
    isAuthenticated: currentState.isAuthenticated
  })
  useAuthStore.getState().markAsHydrated()
}, 500) // Increased delay

export default useAuthStore
