import { create } from 'zustand'
import { notificationAPI } from '../lib/api'
import webSocketService from '../lib/websocket'

const useNotificationStore = create((set, get) => ({
  // State
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
  isConnected: false,

  // Actions
  fetchNotifications: async (page = 0, size = 20) => {
    set({ isLoading: true, error: null })
    try {
      const response = await notificationAPI.getNotifications({ page, size })
      set({
        notifications: response.data.notifications,
        isLoading: false
      })
      return response.data
    } catch (error) {
      set({
        error: error.response?.data?.message || 'Failed to fetch notifications',
        isLoading: false
      })
      throw error
    }
  },

  fetchUnreadNotifications: async () => {
    set({ isLoading: true, error: null })
    try {
      const response = await notificationAPI.getNotifications({ page: 0, size: 10 })
      const unreadNotifications = response.data.notifications.filter(n => !n.seen)
      set({
        notifications: unreadNotifications,
        isLoading: false
      })
      return unreadNotifications
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch unread notifications'

      // Don't set error state for authentication issues during initialization
      if (error.response?.status === 403) {
        console.warn('Authentication required for notifications:', errorMessage)
        set({ isLoading: false })
      } else if (error.code === 'ERR_NETWORK') {
        console.warn('Network error fetching notifications (likely CORS during initialization):', errorMessage)
        set({ isLoading: false })
      } else {
        set({
          error: errorMessage,
          isLoading: false
        })
      }
      throw error
    }
  },

  fetchUnreadCount: async () => {
    try {
      const response = await notificationAPI.getUnreadCount()
      set({ unreadCount: response.data.unreadCount })
      return response.data.unreadCount
    } catch (error) {
      console.error('Failed to fetch unread count:', error)
      // Don't set error state for authentication issues during initialization
      if (error.response?.status === 403) {
        console.warn('Authentication required for unread count')
      } else if (error.code === 'ERR_NETWORK') {
        console.warn('Network error fetching unread count (likely CORS during initialization)')
      } else {
        set({ error: error.response?.data?.message || 'Failed to fetch unread count' })
      }
      return 0
    }
  },

  markAsRead: async (notificationId) => {
    try {
      await notificationAPI.markAsRead(notificationId)
      
      // Update local state
      const { notifications, unreadCount } = get()
      const updatedNotifications = notifications.map(notification =>
        notification.id === notificationId
          ? { ...notification, seen: true, readAt: new Date().toISOString() }
          : notification
      )
      
      const wasUnread = notifications.find(n => n.id === notificationId && !n.seen)
      const newUnreadCount = wasUnread ? Math.max(0, unreadCount - 1) : unreadCount
      
      set({
        notifications: updatedNotifications,
        unreadCount: newUnreadCount
      })
    } catch (error) {
      set({ error: error.response?.data?.message || 'Failed to mark notification as read' })
      throw error
    }
  },

  markAllAsRead: async () => {
    try {
      await notificationAPI.markAllAsRead()
      
      // Update local state
      const { notifications } = get()
      const updatedNotifications = notifications.map(notification => ({
        ...notification,
        seen: true,
        readAt: new Date().toISOString()
      }))
      
      set({
        notifications: updatedNotifications,
        unreadCount: 0
      })
    } catch (error) {
      set({ error: error.response?.data?.message || 'Failed to mark all notifications as read' })
      throw error
    }
  },

  addNotification: (notification) => {
    const { notifications, unreadCount } = get()
    set({
      notifications: [notification, ...notifications],
      unreadCount: unreadCount + 1
    })
  },

  updateUnreadCount: (count) => {
    set({ unreadCount: count })
  },

  clearError: () => {
    set({ error: null })
  },

  autoMarkAsSeen: async () => {
    try {
      await notificationAPI.autoMarkAsSeen()

      // Update local state - mark all notifications as seen
      const { notifications } = get()
      const updatedNotifications = notifications.map(notification => ({
        ...notification,
        seen: true,
        readAt: new Date().toISOString()
      }))

      set({
        notifications: updatedNotifications,
        unreadCount: 0
      })
    } catch (error) {
      console.error('Failed to auto-mark notifications as seen:', error)
      // Don't set error state for this operation as it's automatic
    }
  },

  reset: () => {
    set({
      notifications: [],
      unreadCount: 0,
      isLoading: false,
      error: null,
      isConnected: false
    })
  },

  // WebSocket connection management
  connectWebSocket: (userId, token) => {
    if (!webSocketService.isConnected()) {
      console.log('WebSocket not connected, cannot subscribe to notifications')
      return
    }

    try {
      // Subscribe to user-specific notifications using WebSocket topics
      webSocketService.subscribeToNotifications((notification) => {
        console.log('Received real-time notification via WebSocket:', notification)
        get().addNotification(notification)
      })

      // Subscribe to notification count updates
      webSocketService.subscribeToNotificationCount((data) => {
        console.log('Received unread count update via WebSocket:', data)
        get().updateUnreadCount(data.unreadCount)
      })

      // Subscribe to admin messages
      webSocketService.subscribeToAdminMessages((message) => {
        console.log('Received admin message:', message)
        // You could show admin messages as special notifications
        get().addNotification({
          id: `admin-${Date.now()}`,
          title: 'Admin Message',
          message: message.message,
          type: 'admin',
          createdAt: new Date(message.timestamp),
          seen: false
        })
      })

      // Subscribe to broadcast messages
      webSocketService.subscribeToBroadcast((broadcast) => {
        console.log('Received broadcast message:', broadcast)
        get().addNotification({
          id: `broadcast-${Date.now()}`,
          title: 'System Announcement',
          message: broadcast.message,
          type: 'broadcast',
          createdAt: new Date(broadcast.timestamp),
          seen: false
        })
      })

      // Authentication is handled automatically by the WebSocket service
      // No need to send additional subscription requests

      set({ isConnected: true })
      console.log('Successfully connected to WebSocket notification channels')
    } catch (error) {
      console.error('Failed to connect to WebSocket notification channels:', error)
      set({ isConnected: false })
    }
  },

  disconnectWebSocket: () => {
    try {
      // Unsubscribe from notification channels using proper topic names
      webSocketService.unsubscribe('notifications')
      webSocketService.unsubscribe('notification-count')
      webSocketService.unsubscribe('admin-message')
      webSocketService.unsubscribe('broadcast')

      set({ isConnected: false })
      console.log('Disconnected from notification channels')
    } catch (error) {
      console.error('Error disconnecting from notification channels:', error)
    }
  },

  // Utility functions
  getNotificationById: (id) => {
    const { notifications } = get()
    return notifications.find(notification => notification.id === id)
  },

  getUnreadNotifications: () => {
    const { notifications } = get()
    return notifications.filter(notification => !notification.seen)
  },

  getNotificationsByType: (type) => {
    const { notifications } = get()
    return notifications.filter(notification => notification.type === type)
  },

  // Initialize store (call this when user logs in)
  initialize: async (userId, token) => {
    try {
      console.log('Initializing notification store for user:', userId)

      // Clear any previous error state
      set({ error: null })

      // Use the token parameter directly instead of checking localStorage
      if (!token) {
        console.warn('No token provided to notification store, skipping initialization')
        return
      }

      // Fetch initial data with error handling
      const results = await Promise.allSettled([
        get().fetchUnreadCount(),
        get().fetchUnreadNotifications()
      ])

      // Log any failures but don't throw
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          const operation = index === 0 ? 'fetchUnreadCount' : 'fetchUnreadNotifications'
          const error = result.reason

          // Only log non-authentication errors as warnings
          if (error.response?.status !== 403) {
            console.warn(`Failed to ${operation}:`, error)
          } else {
            console.log(`Authentication required for ${operation}, will retry when token is available`)
          }
        }
      })

      // Connect to WebSocket notifications if WebSocket is connected
      if (webSocketService.isConnected()) {
        get().connectWebSocket(userId, token)
      } else {
        console.log('WebSocket not connected yet, will connect when available')
        // Wait for WebSocket connection and then connect to notifications
        const checkConnection = () => {
          if (webSocketService.isConnected()) {
            get().connectWebSocket(userId, token)
          } else {
            setTimeout(checkConnection, 1000) // Check again in 1 second
          }
        }
        checkConnection()
      }

      console.log('Notification store initialization completed')
    } catch (error) {
      console.error('Failed to initialize notification store:', error)
      // Don't throw the error to prevent breaking the app initialization
    }
  }
}))

export default useNotificationStore
