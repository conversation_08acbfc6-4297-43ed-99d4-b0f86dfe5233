#!/bin/bash

# BeautyHub Integration Test Runner
# This script runs comprehensive integration tests for the appointment system

set -e

echo "🧪 BeautyHub Integration Test Suite"
echo "=================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to run a specific test class
run_test_class() {
    local test_class=$1
    local test_name=$2
    
    print_status "Running $test_name..."
    
    if ./gradlew test --tests "$test_class" --info; then
        print_success "$test_name completed successfully"
        return 0
    else
        print_error "$test_name failed"
        return 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Redis is running
    if ! redis-cli ping > /dev/null 2>&1; then
        print_warning "Redis is not running. Starting Redis..."
        if command -v redis-server > /dev/null 2>&1; then
            redis-server --daemonize yes
            sleep 2
            if redis-cli ping > /dev/null 2>&1; then
                print_success "Redis started successfully"
            else
                print_error "Failed to start Redis"
                return 1
            fi
        else
            print_error "Redis is not installed. Please install Redis and try again."
            return 1
        fi
    else
        print_success "Redis is running"
    fi
    
    # Check if Gradle is available
    if ! command -v ./gradlew > /dev/null 2>&1; then
        print_error "Gradle wrapper not found. Please ensure you're in the project root directory."
        return 1
    fi
    
    print_success "All prerequisites met"
    return 0
}

# Function to clean test environment
clean_test_environment() {
    print_status "Cleaning test environment..."
    
    # Clean Gradle build cache
    ./gradlew clean > /dev/null 2>&1
    
    # Clean Redis test database
    redis-cli -n 1 flushdb > /dev/null 2>&1
    
    print_success "Test environment cleaned"
}

# Function to run all integration tests
run_all_tests() {
    local failed_tests=()
    local total_tests=0
    local passed_tests=0
    
    echo ""
    print_status "Starting Integration Test Execution"
    echo "======================================"
    
    # Test classes to run
    test_classes=(
        "com.ddimitko.beautyhub.integration.AppointmentSystemIntegrationTest"
        "com.ddimitko.beautyhub.integration.SlotLockingIntegrationTest"
        "com.ddimitko.beautyhub.integration.StripePaymentIntegrationTest"
        "com.ddimitko.beautyhub.integration.DatabaseIntegrationTest"
    )

    test_names=(
        "Appointment System Integration Tests"
        "Slot Locking Integration Tests"
        "Stripe Payment Integration Tests"
        "Database Integration Tests"
    )

    # Run each test class
    for i in "${!test_classes[@]}"; do
        total_tests=$((total_tests + 1))
        echo ""

        if run_test_class "${test_classes[$i]}" "${test_names[$i]}"; then
            passed_tests=$((passed_tests + 1))
        else
            failed_tests+=("${test_names[$i]}")
        fi
    done
    
    # Print summary
    echo ""
    echo "======================================"
    print_status "Integration Test Summary"
    echo "======================================"
    echo "Total Test Classes: $total_tests"
    echo "Passed: $passed_tests"
    echo "Failed: $((total_tests - passed_tests))"
    echo ""
    
    if [ ${#failed_tests[@]} -eq 0 ]; then
        print_success "🎉 All integration tests passed!"
        echo ""
        echo "✅ Appointment System: WORKING"
        echo "✅ Slot Locking: WORKING"
        echo "✅ Stripe Payments: WORKING"
        echo "✅ Database Integration: WORKING"
        echo ""
        return 0
    else
        print_error "❌ Some integration tests failed:"
        for failed_test in "${failed_tests[@]}"; do
            echo "   - $failed_test"
        done
        echo ""
        return 1
    fi
}

# Function to run specific test category
run_specific_tests() {
    local category=$1
    
    case $category in
        "appointment")
            run_test_class "com.ddimitko.beautyhub.integration.AppointmentSystemIntegrationTest" "Appointment System Integration Tests"
            ;;
        "locking")
            run_test_class "com.ddimitko.beautyhub.integration.SlotLockingIntegrationTest" "Slot Locking Integration Tests"
            ;;
        "payment")
            run_test_class "com.ddimitko.beautyhub.integration.StripePaymentIntegrationTest" "Stripe Payment Integration Tests"
            ;;
        "database")
            run_test_class "com.ddimitko.beautyhub.integration.DatabaseIntegrationTest" "Database Integration Tests"
            ;;
        *)
            print_error "Unknown test category: $category"
            echo "Available categories: appointment, locking, payment, database"
            return 1
            ;;
    esac
}

# Function to generate test report
generate_test_report() {
    print_status "Generating test report..."
    
    # Run tests with detailed reporting
    ./gradlew test --tests "com.ddimitko.beautyhub.integration.*" \
        --continue \
        --info \
        --stacktrace \
        > test-results.log 2>&1
    
    # Generate HTML report
    if [ -d "build/reports/tests/test" ]; then
        print_success "Test report generated: build/reports/tests/test/index.html"
        
        # Open report in browser (macOS)
        if command -v open > /dev/null 2>&1; then
            open build/reports/tests/test/index.html
        fi
    else
        print_warning "HTML test report not found"
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -a, --all               Run all integration tests (default)"
    echo "  -c, --category CATEGORY Run specific test category"
    echo "  -r, --report            Generate detailed test report"
    echo "  -v, --verbose           Enable verbose output"
    echo "  --clean                 Clean test environment before running"
    echo ""
    echo "Categories:"
    echo "  appointment             Appointment system tests"
    echo "  locking                 Slot locking tests"
    echo "  payment                 Stripe payment tests"
    echo "  database                Database integration tests"
    echo ""
    echo "Examples:"
    echo "  $0                      # Run all tests"
    echo "  $0 -c appointment       # Run only appointment tests"
    echo "  $0 -r                   # Run all tests and generate report"
    echo "  $0 --clean -a           # Clean environment and run all tests"
}

# Parse command line arguments
VERBOSE=false
CLEAN=false
CATEGORY=""
REPORT=false
RUN_ALL=true

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -a|--all)
            RUN_ALL=true
            shift
            ;;
        -c|--category)
            CATEGORY="$2"
            RUN_ALL=false
            shift 2
            ;;
        -r|--report)
            REPORT=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --clean)
            CLEAN=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    # Check prerequisites
    if ! check_prerequisites; then
        exit 1
    fi
    
    # Clean environment if requested
    if [ "$CLEAN" = true ]; then
        clean_test_environment
    fi
    
    # Run tests
    if [ "$RUN_ALL" = true ]; then
        if ! run_all_tests; then
            exit 1
        fi
    elif [ -n "$CATEGORY" ]; then
        if ! run_specific_tests "$CATEGORY"; then
            exit 1
        fi
    fi
    
    # Generate report if requested
    if [ "$REPORT" = true ]; then
        generate_test_report
    fi
    
    print_success "Integration testing completed successfully! 🚀"
}

# Run main function
main "$@"
