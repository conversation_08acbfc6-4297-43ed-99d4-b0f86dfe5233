#!/bin/bash

# BeautyHub Test Runner Script
# This script runs all tests for the BeautyHub application

set -e  # Exit on any error

echo "🧪 BeautyHub Test Suite Runner"
echo "=============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v java &> /dev/null; then
        print_error "Java is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed or not in PATH"
        exit 1
    fi
    
    print_success "All prerequisites are installed"
}

# Function to run backend tests
run_backend_tests() {
    print_status "Running Backend Tests..."
    echo "========================"
    
    # Unit Tests
    print_status "Running Unit Tests..."
    ./gradlew test --tests "*Test" --continue
    
    if [ $? -eq 0 ]; then
        print_success "Backend unit tests passed"
    else
        print_error "Backend unit tests failed"
        return 1
    fi
    
    # Integration Tests
    print_status "Running Integration Tests..."
    ./gradlew test --tests "*IntegrationTest" --continue
    
    if [ $? -eq 0 ]; then
        print_success "Backend integration tests passed"
    else
        print_error "Backend integration tests failed"
        return 1
    fi
    
    print_success "All backend tests completed successfully"
}

# Function to run frontend tests
run_frontend_tests() {
    print_status "Running Frontend Tests..."
    echo "========================="
    
    cd bhfrontend
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        npm install
    fi
    
    # Run tests
    print_status "Running frontend unit tests..."
    npm test -- --coverage --watchAll=false
    
    if [ $? -eq 0 ]; then
        print_success "Frontend tests passed"
    else
        print_error "Frontend tests failed"
        cd ..
        return 1
    fi
    
    cd ..
    print_success "All frontend tests completed successfully"
}

# Function to run end-to-end tests
run_e2e_tests() {
    print_status "Running End-to-End Tests..."
    echo "============================"
    
    # Check if backend is running
    if ! curl -f http://localhost:8080/api/test/health &> /dev/null; then
        print_warning "Backend is not running. Starting backend..."
        ./gradlew bootRun &
        BACKEND_PID=$!
        
        # Wait for backend to start
        print_status "Waiting for backend to start..."
        for i in {1..30}; do
            if curl -f http://localhost:8080/api/test/health &> /dev/null; then
                print_success "Backend is running"
                break
            fi
            sleep 2
        done
        
        if ! curl -f http://localhost:8080/api/test/health &> /dev/null; then
            print_error "Backend failed to start"
            kill $BACKEND_PID 2>/dev/null || true
            return 1
        fi
    else
        print_success "Backend is already running"
        BACKEND_PID=""
    fi
    
    # Check if frontend is running
    if ! curl -f http://localhost:3000 &> /dev/null; then
        print_warning "Frontend is not running. Starting frontend..."
        cd bhfrontend
        npm start &
        FRONTEND_PID=$!
        cd ..
        
        # Wait for frontend to start
        print_status "Waiting for frontend to start..."
        for i in {1..30}; do
            if curl -f http://localhost:3000 &> /dev/null; then
                print_success "Frontend is running"
                break
            fi
            sleep 2
        done
        
        if ! curl -f http://localhost:3000 &> /dev/null; then
            print_error "Frontend failed to start"
            kill $FRONTEND_PID 2>/dev/null || true
            [ -n "$BACKEND_PID" ] && kill $BACKEND_PID 2>/dev/null || true
            return 1
        fi
    else
        print_success "Frontend is already running"
        FRONTEND_PID=""
    fi
    
    # Run basic API tests
    print_status "Testing API endpoints..."
    
    # Test health endpoint
    if curl -f http://localhost:8080/api/test/health &> /dev/null; then
        print_success "Health endpoint is working"
    else
        print_error "Health endpoint is not working"
        [ -n "$BACKEND_PID" ] && kill $BACKEND_PID 2>/dev/null || true
        [ -n "$FRONTEND_PID" ] && kill $FRONTEND_PID 2>/dev/null || true
        return 1
    fi
    
    # Test hello endpoint
    if curl -f http://localhost:8080/api/test/hello &> /dev/null; then
        print_success "Hello endpoint is working"
    else
        print_error "Hello endpoint is not working"
        [ -n "$BACKEND_PID" ] && kill $BACKEND_PID 2>/dev/null || true
        [ -n "$FRONTEND_PID" ] && kill $FRONTEND_PID 2>/dev/null || true
        return 1
    fi
    
    # Test frontend
    if curl -f http://localhost:3000 &> /dev/null; then
        print_success "Frontend is accessible"
    else
        print_error "Frontend is not accessible"
        [ -n "$BACKEND_PID" ] && kill $BACKEND_PID 2>/dev/null || true
        [ -n "$FRONTEND_PID" ] && kill $FRONTEND_PID 2>/dev/null || true
        return 1
    fi
    
    # Clean up processes we started
    if [ -n "$BACKEND_PID" ]; then
        print_status "Stopping backend..."
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ -n "$FRONTEND_PID" ]; then
        print_status "Stopping frontend..."
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    print_success "End-to-end tests completed successfully"
}

# Function to generate test report
generate_test_report() {
    print_status "Generating Test Report..."
    echo "=========================="
    
    # Backend test results
    if [ -d "build/reports/tests/test" ]; then
        print_success "Backend test report available at: build/reports/tests/test/index.html"
    fi
    
    # Frontend test results
    if [ -d "bhfrontend/coverage" ]; then
        print_success "Frontend coverage report available at: bhfrontend/coverage/lcov-report/index.html"
    fi
    
    # Generate summary
    echo ""
    echo "📊 Test Summary"
    echo "==============="
    
    # Count backend test files
    BACKEND_TESTS=$(find src/test -name "*Test.groovy" | wc -l)
    echo "Backend test files: $BACKEND_TESTS"
    
    # Count frontend test files
    FRONTEND_TESTS=$(find bhfrontend/src -name "*.test.js" | wc -l)
    echo "Frontend test files: $FRONTEND_TESTS"
    
    echo "Total test files: $((BACKEND_TESTS + FRONTEND_TESTS))"
}

# Main execution
main() {
    echo "Starting test execution at $(date)"
    echo ""
    
    # Parse command line arguments
    RUN_BACKEND=true
    RUN_FRONTEND=true
    RUN_E2E=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --backend-only)
                RUN_FRONTEND=false
                RUN_E2E=false
                shift
                ;;
            --frontend-only)
                RUN_BACKEND=false
                RUN_E2E=false
                shift
                ;;
            --e2e)
                RUN_E2E=true
                shift
                ;;
            --all)
                RUN_BACKEND=true
                RUN_FRONTEND=true
                RUN_E2E=true
                shift
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo "Options:"
                echo "  --backend-only    Run only backend tests"
                echo "  --frontend-only   Run only frontend tests"
                echo "  --e2e            Run end-to-end tests"
                echo "  --all            Run all tests including e2e"
                echo "  --help           Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Check prerequisites
    check_prerequisites
    
    # Track overall success
    OVERALL_SUCCESS=true
    
    # Run backend tests
    if [ "$RUN_BACKEND" = true ]; then
        if ! run_backend_tests; then
            OVERALL_SUCCESS=false
        fi
        echo ""
    fi
    
    # Run frontend tests
    if [ "$RUN_FRONTEND" = true ]; then
        if ! run_frontend_tests; then
            OVERALL_SUCCESS=false
        fi
        echo ""
    fi
    
    # Run e2e tests
    if [ "$RUN_E2E" = true ]; then
        if ! run_e2e_tests; then
            OVERALL_SUCCESS=false
        fi
        echo ""
    fi
    
    # Generate report
    generate_test_report
    
    echo ""
    echo "Test execution completed at $(date)"
    
    if [ "$OVERALL_SUCCESS" = true ]; then
        print_success "🎉 All tests passed successfully!"
        exit 0
    else
        print_error "❌ Some tests failed. Please check the output above."
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
