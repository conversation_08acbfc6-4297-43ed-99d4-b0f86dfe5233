/**
 * Simple test script to verify JWT implementation fixes
 * Run this in the browser console after logging in
 */

// Test 1: Check localStorage cleanup
console.log('=== Test 1: localStorage cleanup ===');
const manualToken = localStorage.getItem('token');
const manualUser = localStorage.getItem('user');
const authStorage = localStorage.getItem('auth-storage');

console.log('Manual token entry:', manualToken ? 'EXISTS (BAD)' : 'CLEAN (GOOD)');
console.log('Manual user entry:', manualUser ? 'EXISTS (BAD)' : 'CLEAN (GOOD)');
console.log('Auth storage entry:', authStorage ? 'EXISTS (GOOD)' : 'MISSING (BAD)');

// Test 2: Check auth store state
console.log('\n=== Test 2: Auth store state ===');
try {
  const authStore = window.useAuthStore?.getState?.();
  if (authStore) {
    console.log('Auth store accessible:', 'YES (GOOD)');
    console.log('Is authenticated:', authStore.isAuthenticated);
    console.log('Has token:', !!authStore.token);
    console.log('Has user:', !!authStore.user);
    
    // Test token validation
    if (authStore.token) {
      console.log('Token valid (client-side):', authStore.isTokenValid());
    }
  } else {
    console.log('Auth store accessible:', 'NO (BAD)');
  }
} catch (error) {
  console.error('Error accessing auth store:', error);
}

// Test 3: Check token structure
console.log('\n=== Test 3: Token structure ===');
try {
  const authStorageData = JSON.parse(authStorage || '{}');
  const token = authStorageData.state?.token;
  
  if (token) {
    const parts = token.split('.');
    console.log('Token parts count:', parts.length === 3 ? '3 (GOOD)' : `${parts.length} (BAD)`);
    
    // Decode payload
    const payload = JSON.parse(atob(parts[1]));
    console.log('Token payload:', payload);
    console.log('Token expiry:', new Date(payload.exp * 1000).toLocaleString());
    console.log('Token valid:', payload.exp > Date.now() / 1000 ? 'YES (GOOD)' : 'NO (BAD)');
  } else {
    console.log('No token found in auth storage');
  }
} catch (error) {
  console.error('Error parsing token:', error);
}

// Test 4: Test API interceptor
console.log('\n=== Test 4: API interceptor test ===');
console.log('To test API interceptor, make an authenticated API call and check if Authorization header is set correctly');

// Instructions for manual testing
console.log('\n=== Manual Testing Instructions ===');
console.log('1. Log in to the application');
console.log('2. Check that only "auth-storage" exists in localStorage (no "token" or "user")');
console.log('3. Refresh the page - you should remain logged in');
console.log('4. Restart the server (with create-drop) - you should be logged out after token validation fails');
console.log('5. Check browser network tab to see that API calls include Authorization header');
