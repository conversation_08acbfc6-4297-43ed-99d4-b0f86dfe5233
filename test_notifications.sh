#!/bin/bash

# BeautyHub Notification System Test Script
echo "🧪 Testing BeautyHub Notification System"
echo "========================================"

BASE_URL="http://localhost:8080/api"
TOKEN=""
USER_ID=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS") echo -e "${GREEN}✅ $message${NC}" ;;
        "ERROR") echo -e "${RED}❌ $message${NC}" ;;
        "INFO") echo -e "${BLUE}ℹ️  $message${NC}" ;;
        "WARNING") echo -e "${YELLOW}⚠️  $message${NC}" ;;
    esac
}

# Function to make API calls with error handling
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    local auth_header=""
    
    if [ ! -z "$TOKEN" ]; then
        auth_header="-H \"Authorization: Bearer $TOKEN\""
    fi
    
    if [ ! -z "$data" ]; then
        eval curl -s -X $method "$BASE_URL$endpoint" \
            -H "Content-Type: application/json" \
            $auth_header \
            -d "'$data'"
    else
        eval curl -s -X $method "$BASE_URL$endpoint" \
            -H "Content-Type: application/json" \
            $auth_header
    fi
}

# Test 1: Check if backend is running
echo ""
print_status "INFO" "Step 1: Checking if backend is running..."
response=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/test-data/create-all")
if [ "$response" = "200" ] || [ "$response" = "400" ]; then
    print_status "SUCCESS" "Backend is running on port 8080"
else
    print_status "ERROR" "Backend is not responding (HTTP $response)"
    exit 1
fi

# Test 2: Create test data
echo ""
print_status "INFO" "Step 2: Creating test data..."
response=$(api_call "POST" "/test-data/create-all")
echo "Response: $response"

# Test 3: Register a test user
echo ""
print_status "INFO" "Step 3: Registering test user..."
user_data='{
    "firstName": "Test",
    "lastName": "User",
    "email": "<EMAIL>",
    "password": "TestPass123"
}'

register_response=$(api_call "POST" "/auth/register" "$user_data")
echo "Register response: $register_response"

# Test 4: Login with test user
echo ""
print_status "INFO" "Step 4: Logging in test user..."
login_data='{
    "email": "<EMAIL>",
    "password": "TestPass123"
}'

login_response=$(api_call "POST" "/auth/login" "$login_data")
echo "Login response: $login_response"

# Extract token and user ID from login response
TOKEN=$(echo $login_response | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
USER_ID=$(echo $login_response | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

if [ ! -z "$TOKEN" ]; then
    print_status "SUCCESS" "Login successful, token obtained"
    echo "Token: ${TOKEN:0:20}..."
    echo "User ID: $USER_ID"
else
    print_status "ERROR" "Login failed, no token received"
    echo "Full response: $login_response"
fi

# Test 5: Create test notifications
echo ""
print_status "INFO" "Step 5: Creating test notifications..."
if [ ! -z "$TOKEN" ]; then
    notifications_response=$(api_call "POST" "/test-data/notifications")
    echo "Notifications response: $notifications_response"
    
    if echo "$notifications_response" | grep -q "success\|created"; then
        print_status "SUCCESS" "Test notifications created"
    else
        print_status "WARNING" "Notifications creation response unclear"
    fi
else
    print_status "ERROR" "Cannot create notifications without authentication"
fi

# Test 6: Get notification count
echo ""
print_status "INFO" "Step 6: Getting unread notification count..."
if [ ! -z "$TOKEN" ]; then
    count_response=$(api_call "GET" "/notifications/unread-count")
    echo "Count response: $count_response"
    
    unread_count=$(echo $count_response | grep -o '"unreadCount":[0-9]*' | cut -d':' -f2)
    if [ ! -z "$unread_count" ]; then
        print_status "SUCCESS" "Unread count: $unread_count"
    else
        print_status "WARNING" "Could not parse unread count"
    fi
else
    print_status "ERROR" "Cannot get count without authentication"
fi

# Test 7: Get notifications list
echo ""
print_status "INFO" "Step 7: Getting notifications list..."
if [ ! -z "$TOKEN" ]; then
    list_response=$(api_call "GET" "/notifications")
    echo "List response: $list_response"
    
    if echo "$list_response" | grep -q "notifications"; then
        print_status "SUCCESS" "Notifications list retrieved"
    else
        print_status "WARNING" "Notifications list response unclear"
    fi
else
    print_status "ERROR" "Cannot get notifications without authentication"
fi

# Test 8: Get unread notifications
echo ""
print_status "INFO" "Step 8: Getting unread notifications..."
if [ ! -z "$TOKEN" ]; then
    unread_response=$(api_call "GET" "/notifications/unread")
    echo "Unread response: $unread_response"
    
    if echo "$unread_response" | grep -q "notifications"; then
        print_status "SUCCESS" "Unread notifications retrieved"
    else
        print_status "WARNING" "Unread notifications response unclear"
    fi
else
    print_status "ERROR" "Cannot get unread notifications without authentication"
fi

# Summary
echo ""
echo "🏁 Test Summary"
echo "==============="
print_status "INFO" "Backend: Running ✅"
print_status "INFO" "Authentication: $([ ! -z "$TOKEN" ] && echo "Working ✅" || echo "Failed ❌")"
print_status "INFO" "Notifications API: $([ ! -z "$unread_count" ] && echo "Working ✅" || echo "Needs checking ⚠️")"

echo ""
print_status "INFO" "Next steps:"
echo "1. Open http://localhost:3000 in your browser"
echo "2. Login with: <EMAIL> / TestPass123"
echo "3. Check the notification dropdown in the navbar"
echo "4. Verify that notifications show up with dynamic counts"

echo ""
print_status "INFO" "Test completed!"
