// Test script to check the frontend API call
const axios = require('axios');

const API_BASE_URL = 'http://localhost:8080/api';

async function testShopEmployeesAPI() {
    try {
        console.log('Testing shop employees API...');
        const response = await axios.get(`${API_BASE_URL}/public/shops/fb83c0b5-eed5-4c93-8b38-9b62604883a6/employees`);
        
        console.log('Response status:', response.status);
        console.log('Response data:', JSON.stringify(response.data, null, 2));
        
        const employees = response.data.data;
        console.log('Number of employees:', employees.length);
        
        // Extract services like the frontend does
        const services = employees.reduce((allServices, employee) => {
            console.log(`Processing employee: ${employee.name}`);
            if (employee.services && Array.isArray(employee.services)) {
                console.log(`Employee has ${employee.services.length} services`);
                employee.services.forEach(service => {
                    if (!allServices.find(s => s.id === service.id)) {
                        allServices.push({
                            ...service,
                            employeeId: employee.id,
                            employeeName: employee.name
                        });
                    }
                });
            }
            return allServices;
        }, []);
        
        console.log('Extracted services:', services.length);
        console.log('Services:', services.map(s => ({ id: s.id, name: s.name, employeeName: s.employeeName })));
        
    } catch (error) {
        console.error('Error testing API:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

testShopEmployeesAPI();
