#!/bin/bash

# Performance test script for WebSocket slot locking notifications
# This script tests the optimized WebSocket implementation

echo "🚀 WebSocket Performance Test - Optimized Implementation"
echo "========================================================"

# Configuration
BASE_URL="http://localhost:8080"
SHOP_ID="123e4567-e89b-12d3-a456-426614174000"
SERVICE_ID="123e4567-e89b-12d3-a456-426614174001"
EMPLOYEE_ID="123e4567-e89b-12d3-a456-426614174002"
DATE_TIME="2025-01-15T14:30:00"

echo ""
echo "📊 Step 1: Check initial WebSocket health and performance stats..."
INITIAL_STATS=$(curl -s "$BASE_URL/api/websocket/stats")
echo "  Initial Stats: $INITIAL_STATS"

INITIAL_CONNECTIONS=$(echo $INITIAL_STATS | grep -o '"activeConnections":[0-9]*' | cut -d':' -f2)
echo "  Initial Active Connections: $INITIAL_CONNECTIONS"

echo ""
echo "🔒 Step 2: Test slot locking with performance monitoring..."

# Test multiple slot locks to simulate concurrent users
echo "  Testing concurrent slot locks..."

for i in {1..5}; do
    LOCK_RESPONSE=$(curl -s -X POST "$BASE_URL/api/appointments/lock-slot" \
      -H "Content-Type: application/json" \
      -d "{
        \"shopId\": \"$SHOP_ID\",
        \"serviceId\": \"$SERVICE_ID\",
        \"employeeId\": \"$EMPLOYEE_ID\",
        \"dateTime\": \"2025-01-15T$(printf "%02d" $((14 + i))):30:00\"
      }")
    
    echo "    Lock $i Response: $LOCK_RESPONSE"
    
    # Small delay to simulate real user behavior
    sleep 0.5
done

echo ""
echo "📈 Step 3: Check performance stats after slot operations..."
AFTER_STATS=$(curl -s "$BASE_URL/api/websocket/stats")
echo "  Stats After Operations: $AFTER_STATS"

AFTER_CONNECTIONS=$(echo $AFTER_STATS | grep -o '"activeConnections":[0-9]*' | cut -d':' -f2)
TOTAL_MESSAGES=$(echo $AFTER_STATS | grep -o '"totalMessages":[0-9]*' | cut -d':' -f2)

echo "  Active Connections: $AFTER_CONNECTIONS"
echo "  Total Messages Processed: $TOTAL_MESSAGES"

# Extract topic stats if available
TOPIC_COUNT=$(echo $AFTER_STATS | grep -o '"totalTopics":[0-9]*' | cut -d':' -f2)
if [ ! -z "$TOPIC_COUNT" ]; then
    echo "  Active Topics: $TOPIC_COUNT"
fi

echo ""
echo "🔓 Step 4: Test slot unlocking..."

for i in {1..5}; do
    UNLOCK_RESPONSE=$(curl -s -X POST "$BASE_URL/api/appointments/unlock-slot" \
      -H "Content-Type: application/json" \
      -d "{
        \"shopId\": \"$SHOP_ID\",
        \"serviceId\": \"$SERVICE_ID\",
        \"employeeId\": \"$EMPLOYEE_ID\",
        \"dateTime\": \"2025-01-15T$(printf "%02d" $((14 + i))):30:00\"
      }")
    
    echo "    Unlock $i Response: $UNLOCK_RESPONSE"
    sleep 0.5
done

echo ""
echo "📊 Step 5: Final performance analysis..."
FINAL_STATS=$(curl -s "$BASE_URL/api/websocket/stats")
echo "  Final Stats: $FINAL_STATS"

FINAL_CONNECTIONS=$(echo $FINAL_STATS | grep -o '"activeConnections":[0-9]*' | cut -d':' -f2)
FINAL_MESSAGES=$(echo $FINAL_STATS | grep -o '"totalMessages":[0-9]*' | cut -d':' -f2)

echo ""
echo "🎯 Performance Summary:"
echo "  Connection Stability: $INITIAL_CONNECTIONS → $FINAL_CONNECTIONS"
echo "  Messages Processed: $FINAL_MESSAGES"
echo "  Test Operations: 10 (5 locks + 5 unlocks)"

# Calculate efficiency
if [ ! -z "$FINAL_MESSAGES" ] && [ "$FINAL_MESSAGES" -gt 0 ]; then
    EFFICIENCY=$((1000 / FINAL_MESSAGES))
    echo "  Message Efficiency: ~$EFFICIENCY operations per 1000 messages"
fi

echo ""
echo "✅ Performance test completed!"
echo ""
echo "🔍 Key Performance Indicators:"
echo "  - Connection count should remain stable"
echo "  - Topic-based filtering should reduce unnecessary message processing"
echo "  - Real-time slot updates should be delivered only to relevant subscribers"
echo "  - Memory usage should be optimized through proper subscription cleanup"

echo ""
echo "📝 To monitor real-time performance:"
echo "  1. Open the frontend WebSocket Performance Monitor component"
echo "  2. Start monitoring before running this script"
echo "  3. Observe topic subscription patterns and message filtering"
echo "  4. Check for connection leaks or excessive message broadcasting"

echo ""
echo "🚀 Performance test script completed successfully!"
