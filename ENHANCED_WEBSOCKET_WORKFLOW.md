# Enhanced WebSocket Workflow Implementation

## Overview

This document describes the enhanced WebSocket implementation for BeautyHub's appointment booking system. The new implementation follows a specific workflow where users subscribe to slot updates only when viewing available slots, unsubscribe when they lock a slot and move to payment, and re-subscribe when they return to slot selection.

## Workflow Requirements

### User A (Primary User)
1. **Slot Selection**: User selects shop, service, employee, and date
2. **Auto-Subscribe**: Automatically subscribes to topic: `slots.{shopId}.{serviceId}.{employeeId}.{date}`
3. **View Available Slots**: Sees real-time slot availability
4. **Lock Slot**: Selects a slot, which locks it and moves to payment
5. **Auto-Unsubscribe**: Automatically unsubscribes from slot updates topic
6. **Payment Stage**: User is on payment screen (no slot subscriptions)
7. **Back to Slots**: If user goes back, automatically re-subscribes to slot updates

### User B (Secondary User)
1. **Concurrent Viewing**: Views same shop/service/employee/date combination
2. **Real-time Updates**: Sees User A's slot lock immediately (slot becomes disabled/greyed out)
3. **Slot Unlock**: If User A unlocks slot, User B sees it become available immediately
4. **Persistent Connection**: Stays subscribed while on slot selection screen

## Implementation Components

### 1. Enhanced Hook: `usePersistentSlotUpdates`

**Location**: `src/hooks/usePersistentSlotUpdates.js`

**Key Features**:
- Workflow-aware subscription management
- Automatic subscribe/unsubscribe based on user's current step
- Topic resubscription when parameters change
- Connection monitoring and error handling

**Parameters**:
```javascript
usePersistentSlotUpdates(
  shopId,           // Shop UUID
  serviceId,        // Service UUID  
  employeeId,       // Employee UUID
  date,             // Selected date (YYYY-MM-DD)
  onSlotUpdate,     // Callback for slot updates
  isOnSlotSelection // Boolean: true when user is on slot selection screen
)
```

**Returns**:
```javascript
{
  isConnected,      // WebSocket connection status
  isSubscribed,     // Topic subscription status
  connectionError,  // Connection error message (if any)
  currentTopic,     // Currently subscribed topic
  unsubscribe       // Manual unsubscribe function
}
```

### 2. Enhanced WebSocket Service

**Location**: `src/lib/websocket.js`

**Key Enhancements**:
- Improved subscription lifecycle management
- Better error handling and logging
- Enhanced message parsing for topic-based messages
- Optimized subscription methods

**New Methods**:
```javascript
// Enhanced slot updates subscription
subscribeToSlotUpdates(shopId, serviceId, employeeId, date, callback)

// Returns unsubscribe function for cleanup
```

### 3. Backend WebSocket Handler

**Location**: `src/main/groovy/com/ddimitko/beautyhub/config/WebSocketConfig.groovy`

**Enhancements**:
- Enhanced subscription confirmations with detailed metadata
- Better logging with emojis for easier debugging
- Improved topic validation
- Structured JSON responses for confirmations/denials

**Message Format**:
```json
{
  "type": "subscription_confirmed",
  "topic": "slots.shop-id.service-id.employee-id.date",
  "sessionId": "session-123",
  "timestamp": 1642678800000,
  "authenticated": true
}
```

### 4. Appointment Booking Integration

**Location**: `src/components/appointments/AppointmentBooking.jsx`

**Integration**:
```javascript
// Use enhanced hook with workflow awareness
const { isConnected, isSubscribed, connectionError, currentTopic } = usePersistentSlotUpdates(
  shopId,
  serviceId,
  employeeId,
  selectedDate,
  handleSlotUpdate,
  step === 1  // Only subscribe when on slot selection step (step 1)
)
```

## Topic Structure

### Slot Updates Topic Format
```
slots.{shopId}.{serviceId}.{employeeId}.{date}
```

**Example**:
```
slots.123e4567-e89b-12d3-a456-************.123e4567-e89b-12d3-a456-************.123e4567-e89b-12d3-a456-************.2025-01-20
```

### Message Structure
```json
{
  "topic": "slots.shop-id.service-id.employee-id.date",
  "data": {
    "type": "SLOT_UPDATE",
    "action": "LOCKED|UNLOCKED",
    "shopId": "shop-uuid",
    "serviceId": "service-uuid", 
    "employeeId": "employee-uuid",
    "dateTime": "2025-01-20T10:00:00",
    "date": "2025-01-20",
    "time": "10:00:00",
    "userId": "user-uuid",
    "timestamp": 1642678800000
  },
  "timestamp": 1642678800000
}
```

## Testing

### Test Component: `EnhancedSlotWorkflowTest`

**Location**: `src/components/test/EnhancedSlotWorkflowTest.jsx`

**Access**: 
- Direct: `http://localhost:3000/test/enhanced-workflow`
- Via Test Suite: `http://localhost:3000/test` → "Enhanced Slot Workflow Test"

**Test Scenarios**:
1. **Connection Management**: Connect/disconnect WebSocket
2. **Subscription Lifecycle**: Simulate slot selection → payment → back to slots
3. **Date Changes**: Change date while on slot selection (should resubscribe)
4. **Real-time Updates**: Multi-tab testing for concurrent users

### Expected Test Flow
1. Click "Connect WS" to establish WebSocket connection
2. Hook automatically subscribes when `isOnSlotSelection = true`
3. Click "Lock Slot (Go to Payment)" to simulate moving to payment step
4. Hook automatically unsubscribes from slot updates
5. Click "Back to Slot Selection" to simulate returning
6. Hook automatically re-subscribes to slot updates
7. Click "Change Date" while on slot selection to test topic resubscription

## Backend Integration

### Slot Locking Service
**Location**: `src/main/groovy/com/ddimitko/beautyhub/service/SlotLockingService.groovy`

- Automatically broadcasts slot lock/unlock events via WebSocket
- Uses Redis for slot locking with TTL
- Integrates with `SlotUpdateService` for real-time notifications

### Slot Update Service  
**Location**: `src/main/groovy/com/ddimitko/beautyhub/service/SlotUpdateService.groovy`

- Broadcasts to multiple topic patterns for flexibility
- Supports both RabbitMQ and WebSocket delivery
- Creates standardized slot update messages

## Monitoring and Debugging

### WebSocket Stats Endpoint
```
GET /api/websocket/stats
```

**Response**:
```json
{
  "activeConnections": 5,
  "totalConnections": 25,
  "totalMessages": 150,
  "topicStats": {
    "totalTopics": 3,
    "topicSubscriptions": {
      "slots.shop1.service1.emp1.2025-01-20": 2,
      "notifications": 3
    }
  }
}
```

### Logging

**Frontend Console**:
- 🎯 Subscription events
- 📡 WebSocket connection events  
- 🔌 Unsubscription events
- 📅 Date/parameter changes
- 📨 Slot update messages

**Backend Logs**:
- 📡 Subscription requests
- ✅ Subscription confirmations
- 🔌 Unsubscription events
- ❌ Subscription denials
- 🎯 Topic broadcasts

## Performance Considerations

1. **Topic-based Broadcasting**: Only users subscribed to specific topics receive messages
2. **Automatic Cleanup**: Subscriptions are automatically cleaned up when users leave
3. **Connection Monitoring**: Periodic connection health checks
4. **Memory Management**: Closed sessions are automatically removed from topic subscriptions

## Security

1. **Unauthenticated Access**: Slot topics allow unauthenticated access for guest bookings
2. **Topic Validation**: Server validates topic format before subscription
3. **Session Isolation**: Each session manages its own subscriptions independently

## Future Enhancements

1. **Wildcard Subscriptions**: Support for pattern-based topic subscriptions
2. **Message Persistence**: Store messages for offline users
3. **Rate Limiting**: Prevent subscription spam
4. **Analytics**: Track subscription patterns and usage metrics
