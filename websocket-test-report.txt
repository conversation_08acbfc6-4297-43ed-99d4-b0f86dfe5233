BeautyHub WebSocket Test Report
Generated: Mon Jun  9 02:57:20 EEST 2025
================================

Backend Server Status:
{
  "status": "UP",
  "timestamp": "2025-06-08T23:57:20.035+00:00",
  "application": "BeautyHub",
  "version": "1.0.0"
}

WebSocket Health:
{
  "status": "UP",
  "websocket": {
    "enabled": true,
    "activeConnections": 1,
    "totalConnections": 1
  },
  "timestamp": "2025-06-09T02:57:20.045074"
}

WebSocket Statistics:
{
  "activeConnections": 1,
  "totalConnections": 1,
  "totalDisconnections": 0,
  "totalMessages": 1,
  "sessions": [
    {
      "sessionId": "ozyqtmav",
      "userId": "anonymous",
      "connectTime": 1749426965688,
      "lastActivity": 1749426965713,
      "messageCount": 1
    }
  ]
}

Configuration Check:
- SockJS WebSocket endpoint: http://localhost:8080/ws-notifications
- Native WebSocket: http://localhost:8080/ws

Test Results:
- Backend tests: Run './gradlew test --tests *WebSocketIntegrationTest*'
- Frontend tests: Open http://localhost:3000/test/websocket

