# BeautyHub - Full-Stack Beauty Booking Platform

A comprehensive beauty booking platform built with Spring Boot 3+ backend and React frontend, featuring real-time slot locking, appointment management, and shop subscriptions.

## 🚀 Tech Stack

### Backend
- **Framework**: Spring Boot 3+
- **Database**: PostgreSQL
- **Caching**: Redis
- **Security**: Spring Security with JWT
- **Real-time**: WebSockets (STOMP)
- **Payments**: Stripe Connect
- **Notifications**: Firebase Cloud Messaging
- **Migrations**: Flyway
- **Language**: Groovy

### Frontend
- **Framework**: React 19+
- **Styling**: Tailwind CSS + shadcn/ui
- **State Management**: Zustand + React Query
- **WebSockets**: STOMP client
- **HTTP Client**: Axios

## 📋 Features Implemented

### ✅ Backend Foundation
- [x] Spring Boot 3+ application setup
- [x] PostgreSQL database configuration
- [x] Redis caching configuration
- [x] JWT authentication system
- [x] WebSocket configuration (STOMP)
- [x] Security configuration with role-based access
- [x] Complete entity model (User, Shop, Employee, Service, Appointment, etc.)
- [x] Repository layer with custom queries
- [x] Database migrations (Flyway)
- [x] Slot locking service (Redis-based)
- [x] Basic service layer
- [x] Authentication controllers
- [x] CORS configuration

### ✅ Frontend Foundation
- [x] React application with routing
- [x] Tailwind CSS + shadcn/ui setup
- [x] Authentication system (login/register)
- [x] Zustand store for state management
- [x] React Query for API calls
- [x] WebSocket service
- [x] Protected routes
- [x] Basic UI components (Button, Input, Card)
- [x] Role-based dashboard

### 🔄 In Progress / Next Steps
- [ ] Complete service layer implementation
- [ ] Appointment booking flow
- [ ] Shop management interface
- [ ] Real-time slot updates
- [ ] Stripe Connect integration
- [ ] Firebase FCM integration
- [ ] Employee scheduling system
- [ ] Rating and review system
- [ ] Notification system
- [ ] File upload for images
- [ ] Advanced search and filtering

## 🏗️ Architecture

### Entity Relationships
```
User (1) ←→ (N) Shop (Owner relationship)
User (1) ←→ (1) Employee
Shop (1) ←→ (N) Employee
Employee (1) ←→ (N) Service
Shop (1) ←→ (N) Service
User (1) ←→ (N) Appointment
Shop (1) ←→ (N) Appointment
Employee (1) ←→ (N) Appointment
Service (1) ←→ (N) Appointment
User (1) ←→ (N) Rating
Shop (1) ←→ (N) Rating
User (1) ←→ (N) Notification
Employee (1) ←→ (N) ScheduleSlot
```

### Real-time Features
- **Slot Locking**: Redis-based 5-minute slot locks
- **WebSocket Channels**:
  - `slots.{shopId}.{serviceId}.{date}`: Slot updates
  - `appointments.{userId}`: Appointment updates
  - `notifications.{userId}`: Real-time notifications

## 🚀 Getting Started

### Prerequisites
- Java 24+
- Node.js 18+
- PostgreSQL 13+
- Redis 6+

### Backend Setup
1. **Clone and navigate to project**
   ```bash
   git clone <repository>
   cd beautyhub
   ```

2. **Configure database**
   ```bash
   # Create PostgreSQL database
   createdb beautyhub
   createuser beautyhub_user
   ```

3. **Update application.properties**
   ```properties
   spring.datasource.url=******************************************
   spring.datasource.username=beautyhub_user
   spring.datasource.password=your_password
   ```

4. **Start Redis**
   ```bash
   redis-server
   ```

5. **Run the application**
   ```bash
   ./gradlew bootRun
   ```

### Frontend Setup
1. **Navigate to frontend directory**
   ```bash
   cd bhfrontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm start
   ```

### Testing the Setup
1. **Backend health check**
   ```bash
   curl http://localhost:8080/api/test/hello
   ```

2. **Frontend access**
   - Open http://localhost:3000
   - Register a new account
   - Login and access dashboard

## 📁 Project Structure

```
beautyhub/
├── src/main/groovy/com/ddimitko/beautyhub/
│   ├── entity/          # JPA entities
│   ├── repository/      # Data repositories
│   ├── service/         # Business logic
│   ├── controller/      # REST controllers
│   ├── config/          # Configuration classes
│   ├── security/        # Security components
│   ├── dto/            # Data transfer objects
│   └── enums/          # Enumerations
├── src/main/resources/
│   ├── db/migration/   # Flyway migrations
│   └── application.properties
└── bhfrontend/
    ├── src/
    │   ├── components/  # React components
    │   ├── store/       # Zustand stores
    │   ├── lib/         # Utilities and API
    │   └── App.js
    └── public/
```

## 🔐 Authentication & Authorization

### User Roles
- **USER**: Regular customers who book appointments
- **OWNER**: Business owners who manage shops
- **EMPLOYEE**: Staff members who provide services

### JWT Implementation
- Token-based authentication
- Role-based access control
- Automatic token refresh
- Secure password hashing (BCrypt)

## 🌐 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/register/owner` - Owner registration
- `POST /api/auth/logout` - User logout

### Test Endpoints
- `GET /api/test/hello` - Backend status
- `GET /api/test/health` - Health check

## 🔧 Configuration

### Environment Variables
```properties
# Database
spring.datasource.url=******************************************
spring.datasource.username=beautyhub_user
spring.datasource.password=your_password

# Redis
spring.data.redis.host=localhost
spring.data.redis.port=6379

# JWT
app.jwt.secret=your_jwt_secret
app.jwt.expiration=86400000

# CORS
app.cors.allowed-origins=http://localhost:3000
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📝 License

This project is licensed under the MIT License.

## 🎯 Next Development Phase

The foundation is complete! The next phase will focus on:
1. Completing the service layer
2. Building the appointment booking flow
3. Implementing real-time features
4. Adding payment integration
5. Creating comprehensive UI components

The architecture is solid and ready for rapid feature development.
