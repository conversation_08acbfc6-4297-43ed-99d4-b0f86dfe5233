# Real-time Slot Updates Testing Guide

## Problem Statement
User A is on the slot selection screen and User B locks a slot, but User A is not notified in any way about that lock.

## Testing Strategy

### Phase 1: Component Testing
Test individual components to ensure they work correctly in isolation.

#### 1.1 WebSocket Connection Test
**URL**: `/test` → WebSocket Connection Test

**Steps**:
1. Click "Run All Tests"
2. Verify connection is established
3. Verify subscription works
4. Check for any error messages

**Expected Results**:
- ✅ Connection: SUCCESS
- ✅ Subscription: SUCCESS  
- ✅ Ping: SUCCESS

#### 1.2 Slot Locking Flow Test
**URL**: `/test` → Slot Locking Flow Test

**Steps**:
1. Click "Run Full Test"
2. Watch console logs for detailed debugging
3. Check both "Test Results" and "WebSocket Messages" panels

**Expected Results**:
- ✅ Connection: SUCCESS
- ✅ Subscription: SUCCESS
- ✅ Slot Locking: SUCCESS
- 📨 WebSocket message received with slot update

### Phase 2: Multi-User Testing
Test real-time updates between multiple users.

#### 2.1 Two Browser Windows Test
**Setup**:
1. Open two browser windows/tabs
2. Navigate both to `/test`
3. Run "Slot Locking Flow Test" in both

**Test Procedure**:
1. **Window A**: Click "Test Connection" → "Test Subscription"
2. **Window B**: Click "Test Connection" → "Test Subscription"
3. **Window A**: Click "Test Slot Lock"
4. **Window B**: Check "WebSocket Messages" panel for incoming message

**Expected Results**:
- Window A should show successful slot lock
- Window B should receive WebSocket message about the lock
- Both windows should show the same slot as locked

#### 2.2 Real Appointment Booking Test
**Setup**:
1. Open two browser windows
2. Navigate to a shop's booking page: `/shop/{shopId}/book`
3. Select same service, employee, and date in both windows

**Test Procedure**:
1. **Window A**: Go to slot selection screen
2. **Window B**: Go to slot selection screen
3. **Window A**: Click on a time slot to lock it
4. **Window B**: Observe if the slot becomes visually locked

**Expected Results**:
- Window A should show slot as selected/locked
- Window B should show slot as locked (red background, lock icon)
- Window B should not be able to select the locked slot

### Phase 3: Backend Verification
Verify backend is correctly broadcasting messages.

#### 3.1 Backend Logs Check
**Check these log entries**:
```
INFO  - Broadcasted slot locked event: shop=..., service=..., employee=..., dateTime=..., user=...
INFO  - Broadcasting to topic 'slots.{shopId}.{serviceId}.{employeeId}.{date}' with message: ...
INFO  - Topic broadcast completed for: slots.{shopId}.{serviceId}.{employeeId}.{date}
```

#### 3.2 WebSocket Stats API
**URL**: `GET /api/websocket/stats`

**Check**:
- `activeConnections` > 0
- `topicStats.totalTopics` > 0
- `topicSubscriptions` contains slot topics

### Phase 4: Frontend Debugging
Use browser developer tools to debug frontend issues.

#### 4.1 Console Logs to Monitor
**WebSocket Connection**:
```
🔌 WebSocket RAW message received: ...
📡 RAW SLOT UPDATE RECEIVED: ...
📡 Processing actual update: ...
🔒 Adding slot to locked set: ...
```

**Subscription Process**:
```
🎯 Subscribing to persistent slot updates: slots.{shopId}.{serviceId}.{employeeId}.{date}
✅ Subscription confirmed for topic: slots.{shopId}.{serviceId}.{employeeId}.{date}
✅ Persistent subscription established for topic: ...
```

#### 4.2 Network Tab Monitoring
**WebSocket Messages**:
1. Open Developer Tools → Network tab
2. Filter by "WS" (WebSocket)
3. Click on WebSocket connection
4. Monitor "Messages" tab for incoming/outgoing messages

### Phase 5: Common Issues and Solutions

#### Issue 1: No WebSocket Connection
**Symptoms**: Connection status shows "Offline"
**Solutions**:
- Check backend is running on correct port
- Verify CORS settings allow WebSocket connections
- Check browser console for connection errors

#### Issue 2: Connection but No Subscription
**Symptoms**: Connected but not subscribed
**Solutions**:
- Check subscription message format
- Verify topic name matches backend expectations
- Check for authentication issues

#### Issue 3: Subscription but No Messages
**Symptoms**: Subscribed but no slot update messages
**Solutions**:
- Verify backend is broadcasting to correct topics
- Check message format matches frontend expectations
- Ensure slot locking actually triggers broadcasts

#### Issue 4: Messages Received but Not Processed
**Symptoms**: Messages in console but UI not updating
**Solutions**:
- Check message parsing logic
- Verify slot update handler is called
- Check for type/format mismatches

### Phase 6: Debug Configuration

#### Frontend Debug Settings
Add to `src/lib/websocket.js`:
```javascript
// Enable verbose logging
const DEBUG_WEBSOCKET = true;

if (DEBUG_WEBSOCKET) {
  console.log('🔍 Debug mode enabled for WebSocket');
}
```

#### Backend Debug Settings
Add to `application.yml`:
```yaml
logging:
  level:
    com.ddimitko.beautyhub.service.SlotUpdateService: DEBUG
    com.ddimitko.beautyhub.service.WebSocketMessagingService: DEBUG
    com.ddimitko.beautyhub.service.WebSocketMonitoringService: DEBUG
```

### Phase 7: Expected Message Flow

#### Complete Flow for Slot Lock:
1. **User A**: Clicks slot → API call to `/appointments/lock-slot`
2. **Backend**: `SlotLockingService.lockSlot()` → Redis lock created
3. **Backend**: `SlotUpdateService.broadcastSlotLocked()` called
4. **Backend**: Message published to RabbitMQ + WebSocket broadcast
5. **Backend**: `WebSocketMessagingService.broadcastToTopic()` called
6. **Backend**: `WebSocketMonitoringService.broadcastToTopic()` sends to subscribers
7. **User B**: WebSocket receives message
8. **User B**: `handleSlotUpdate()` processes message
9. **User B**: UI updates to show slot as locked

#### Message Format:
```json
{
  "topic": "slots.{shopId}.{serviceId}.{employeeId}.{date}",
  "data": {
    "type": "SLOT_UPDATE",
    "action": "LOCKED",
    "shopId": "...",
    "serviceId": "...",
    "employeeId": "...",
    "dateTime": "2025-01-20T10:00:00",
    "date": "2025-01-20",
    "time": "10:00:00",
    "userId": "...",
    "timestamp": 1642680000000
  },
  "timestamp": 1642680000000
}
```

## Testing Checklist

### Pre-Test Setup
- [ ] Backend server running
- [ ] Frontend development server running
- [ ] Redis server running
- [ ] RabbitMQ server running (if used)

### Basic Functionality
- [ ] WebSocket connection establishes successfully
- [ ] Subscription to slot topics works
- [ ] Slot locking API calls succeed
- [ ] Backend logs show broadcast messages

### Real-time Updates
- [ ] User A locks slot → User B sees WebSocket message
- [ ] User A unlocks slot → User B sees WebSocket message
- [ ] Slot visual state updates in real-time
- [ ] Multiple users can subscribe to same topics

### Edge Cases
- [ ] Connection loss and reconnection works
- [ ] Subscription persistence across reconnections
- [ ] Cleanup when users leave slot selection
- [ ] Guest user support (no authentication required)

## Success Criteria

The real-time slot updates are working correctly when:

1. ✅ **Connection**: Both users can connect to WebSocket
2. ✅ **Subscription**: Both users subscribe to slot topics
3. ✅ **Broadcasting**: Backend broadcasts slot lock/unlock events
4. ✅ **Reception**: Users receive WebSocket messages for slot changes
5. ✅ **Processing**: Frontend correctly processes and displays updates
6. ✅ **Visual Feedback**: Locked slots are immediately visible to all users

When all criteria are met, User A locking a slot will immediately notify User B through real-time WebSocket updates.
